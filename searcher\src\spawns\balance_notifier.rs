use std::sync::Arc;

use anyhow::{Context, Result};
use lettre::{SmtpTransport, transport::smtp::authentication::Credentials};
use rust_massa_web3::client::grpc_client::PublicGrpcClient;
use tracing::{debug, error, warn};

use crate::{
    config::{
        CONFIG,
        current::{BALANCE_NOTIFIER_INTERVAL, BALANCE_NOTIFIER_THRESHOLD},
    },
    helpers::email::send_email_notification,
    state::AppState,
};

pub async fn start_balance_notifier(
    mut grpc_client: PublicGrpcClient,
    mailer: &SmtpTransport,
) -> Result<()> {
    let current_address = grpc_client
        .get_current_address()
        .context("Failed to get current address")?
        .to_string();

    let mut interval =
        tokio::time::interval(tokio::time::Duration::from_secs(BALANCE_NOTIFIER_INTERVAL));

    loop {
        interval.tick().await;

        let balance = match grpc_client.get_mas_balance(&current_address).await {
            Ok(balance) => balance,
            Err(e) => {
                error!("Failed to get balance: {}", e);

                // Send an email notification
                send_email_notification(
                    "Quedge Bot Balance Notifier",
                    format!("Failed to get balance: {}", e),
                    &mailer,
                )
                .await
                .context("Failed to send an error email in balance notifier in get_mas_balance")?;

                continue; // Skip this iteration and wait for the next tick
            }
        };

        debug!("Balance of {} is {}", &current_address, balance);

        // TEST errror
        // return Err(anyhow::anyhow!(
        //     "Balance is less than {}",
        //     BALANCE_NOTIFIER_THRESHOLD
        // ));

        if balance < BALANCE_NOTIFIER_THRESHOLD {
            warn!(
                "Balance is less than {}. Sending email...",
                BALANCE_NOTIFIER_THRESHOLD
            );

            send_email_notification(
                "Quedge Bot Balance Notifier",
                format!(
                    "Balance of {} is less than {}. Current balance is {}. Please send some funds to this address.",
                    current_address,  BALANCE_NOTIFIER_THRESHOLD, balance
                ),
                &mailer,
            )
            .await.context("Failed to send a notification email in balance notifier")?;
        }
    }
}
