use anyhow::Context;
use serde::Deserialize;
use std::collections::HashMap;

use crate::config::{CONFIG, current::DEFAULT_START_AMOUNT};

#[derive(Debug, Deserialize)]
pub struct ArbStartAmount {
    pub start_amount: f64,
    pub min_profit_percent: f64,
    pub max_profit_percent: f64,
}

#[derive(Debug, Deserialize)]
pub struct TokenArbConfig {
    /// Minimum % profit required to consider arbitrage for this token
    pub min_profit_percent: f64,

    /// Dedault start amount for this token
    pub default_start_amount: f64,

    // Is flash loan supported for this token
    pub is_support_flash_loan: bool,

    /// Flash loan pool address
    pub flash_loan_pool_address: String,

    /// Profit range -> start amount brackets
    pub start_amounts: Vec<ArbStartAmount>,
}

impl TokenArbConfig {
    pub fn get_best_start_amount(&self, estimated_profit_percent: f64) -> f64 {
        self.start_amounts
            .iter()
            .find(|bracket| {
                estimated_profit_percent >= bracket.min_profit_percent
                    && estimated_profit_percent < bracket.max_profit_percent
            })
            .map(|b| b.start_amount)
            .unwrap_or(self.default_start_amount)
    }
}

#[derive(Debug, Deserialize)]
pub struct ArbStartAmounts {
    /// Map of token address -> strategy config
    pub tokens: HashMap<String, TokenArbConfig>,
}

impl ArbStartAmounts {
    pub fn get_start_amount_for_token(
        &self,
        token_address: &str,
        estimated_profit_percent: f64,
    ) -> f64 {
        self.tokens
            .get(token_address)
            .map(|token_config| token_config.get_best_start_amount(estimated_profit_percent))
            .unwrap_or(DEFAULT_START_AMOUNT)
    }

    pub fn get_default_start_amount_for_token(&self, token_address: &str) -> f64 {
        self.tokens
            .get(token_address)
            .map(|token_config| token_config.default_start_amount)
            .unwrap_or(DEFAULT_START_AMOUNT)
    }
}

pub fn load_arb_start_amounts() -> Result<ArbStartAmounts, anyhow::Error> {
    let mut start_amount_path = "./src/config/buildnet/start_am_config.toml";

    // If the chain is mainnet, we use the mainnet config
    if CONFIG.chain == "mainnet" {
        start_amount_path = "./src/config/mainnet/start_am_config.toml";
    }

    let config_str = std::fs::read_to_string(start_amount_path)
        .context("Failed to read arb start amounts config")?;

    let arb_start_amounts: ArbStartAmounts =
        toml::from_str(&config_str).context("Failed to parse arb start amounts config")?;

    Ok(arb_start_amounts)
}
