import { Args, Result, Serializable } from '@massalabs/as-types';
import { Address } from '@massalabs/massa-as-sdk';
import { u256 } from 'as-bignum/assembly';

export class ArbRoute implements Serializable {
  constructor(
    public routerAddress: Address = new Address(),
    public poolAddress: Address = new Address(),
    public tokenInAddress: Address = new Address(),
    public tokenOutAddress: Address = new Address(),
    public amountIn: u256 = u256.Zero,
    public amountOutMin: u256 = u256.Zero,
    public binStep: u64 = 0,
    public to: Address = new Address(),
    public deadline: u64 = 0,
    public isDusa: bool = false,
    public coinsToUse: u64 = 0,
    public swapParams: StaticArray<u8> = new StaticArray<u8>(0),
  ) {}

  serialize(): StaticArray<u8> {
    return new Args()
      .add(this.routerAddress)
      .add(this.poolAddress)
      .add(this.tokenInAddress)
      .add(this.tokenOutAddress)
      .add(this.amountIn)
      .add(this.amountOutMin)
      .add(this.binStep)
      .add(this.to)
      .add(this.deadline)
      .add(this.isDusa)
      .add(this.coinsToUse)
      .add(this.swapParams)
      .serialize();
  }

  deserialize(data: StaticArray<u8>, offset: i32): Result<i32> {
    const args = new Args(data, offset);

    this.routerAddress = new Address(
      args.nextString().expect('Invalid address'),
    );
    this.poolAddress = new Address(args.nextString().expect('Invalid address'));
    this.tokenInAddress = new Address(
      args.nextString().expect('Invalid address'),
    );
    this.tokenOutAddress = new Address(
      args.nextString().expect('Invalid address'),
    );
    this.amountIn = args.nextU256().expect('Invalid amountIn');
    this.amountOutMin = args.nextU256().expect('Invalid amountOutMin');
    this.binStep = args.nextU64().expect('Invalid binStep');
    this.to = new Address(args.nextString().expect('Invalid address'));
    this.deadline = args.nextU64().expect('Invalid deadline');
    this.isDusa = args.nextBool().expect('Invalid isDusa flag');
    this.coinsToUse = args.nextU64().expect('Invalid coins to use');
    this.swapParams = args.nextBytes().expect('Invalid swap params');

    return new Result(args.offset);
  }

  toString(): string {
    return (
      `Router Address: ${this.routerAddress.toString()}\n` +
      `Pool Address: ${this.poolAddress.toString()}\n` +
      `Token In Address: ${this.tokenInAddress.toString()}\n` +
      `Token Out Address: ${this.tokenOutAddress.toString()}\n` +
      `Amount In: ${this.amountIn.toString()}\n` +
      `Amount Out Min: ${this.amountOutMin.toString()}\n` +
      `Bin Step: ${this.binStep.toString()}\n` +
      `To: ${this.to.toString()}\n` +
      `Deadline: ${this.deadline.toString()}\n` +
      `Is Dusa: ${this.isDusa.toString()}\n` +
      `Coins To Use: ${this.coinsToUse.toString()}\n` +
      `Swap Params: ${this.swapParams.toString()}\n`
    );
  }
}
