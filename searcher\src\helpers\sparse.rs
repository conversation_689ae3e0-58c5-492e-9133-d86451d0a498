use std::{
    collections::HashMap,
    fs::File,
    io::{BufWriter, Write},
    process::Command,
};
use sprs::CsMat;

// Export the sparse matrix to a dot file
pub fn export_to_dot(
    matrix: &CsMat<f64>,
    node_index_map: &HashMap<String, usize>,
    filename: &str,
) -> std::io::Result<()> {
    let mut file = BufWriter::new(File::create(filename)?);

    writeln!(file, "digraph G {{")?;

    let mut index_to_token = vec!["".to_string(); node_index_map.len()];
    for (token, &i) in node_index_map {
        index_to_token[i] = token.clone();
    }

    for (row, vec) in matrix.outer_iterator().enumerate() {
        let from_token = &index_to_token[row];
        for (col, &weight) in vec.iter() {
            let to_token = &index_to_token[col];
            writeln!(
                file,
                "\"{}\" -> \"{}\" [label=\"{:.4}\"]",
                from_token, to_token, weight
            )?;
        }
    }

    writeln!(file, "}}")?;
    Ok(())
}

// New function to export directly to PNG
pub fn export_to_png(
    matrix: &CsMat<f64>,
    node_index_map: &HashMap<String, usize>,
    dot_filename: &str,
    png_filename: &str,
) -> std::io::Result<()> {
    // Step 1: Export to .dot
    export_to_dot(matrix, node_index_map, dot_filename)?;

    // Step 2: Run `dot` command to generate PNG
    let output = Command::new("dot")
        .arg("-Tpng")
        .arg(dot_filename)
        .arg("-o")
        .arg(png_filename)
        .output()
        .expect("Failed to execute `dot` command. Is Graphviz installed?");

    if !output.status.success() {
        eprintln!(
            "Error running `dot`: {}",
            String::from_utf8_lossy(&output.stderr)
        );
        return Err(std::io::Error::new(
            std::io::ErrorKind::Other,
            "Dot command failed",
        ));
    }

    Ok(())
}
