use anyhow::{Context, Result, bail};
use byteorder::{<PERSON><PERSON><PERSON><PERSON>, ReadBytesExt};
use rust_massa_web3::{
    alloy_primitives::U256,
    basic_elements::args::{Args, BYTES_256_OFFSET, Serializable},
};
use std::io::{<PERSON><PERSON><PERSON>, <PERSON>};

#[derive(Debug, PartialEq, Clone)]
pub struct ArbRoute {
    pub router_address: String,
    pub pool_address: String,
    pub token_in_address: String,
    pub token_out_address: String,
    pub amount_in: U256,
    pub min_amount_out: U256,
    pub bin_step: u64,
    pub to_address: String,
    pub deadline: u64,
    pub is_dusa: bool,
    pub coin_to_use: u64,
    pub swap_params: Vec<u8>,
}

// --- Serializable Implementation for ArbRoute ---
impl Serializable for ArbRoute {
    fn serialize(&self) -> Vec<u8> {
        // We can use a temporary Args instance to leverage its add_* methods
        // or serialize manually using WriteBytesExt. Using Args is convenient here.
        let mut temp_args = Args::new(); // Assuming Args is in super::args
        // If Args is in the same module, just Args::new()

        temp_args.add_string(&self.router_address);
        temp_args.add_string(&self.pool_address);
        temp_args.add_string(&self.token_in_address);
        temp_args.add_string(&self.token_out_address);
        temp_args.add_u256(self.amount_in);
        temp_args.add_u256(self.min_amount_out);
        temp_args.add_u64(self.bin_step);
        temp_args.add_string(&self.to_address);
        temp_args.add_u64(self.deadline);
        temp_args.add_bool(self.is_dusa);
        temp_args.add_u64(self.coin_to_use);
        temp_args.add_bytes(&self.swap_params);

        temp_args.serialize() // This returns the Vec<u8> from the temp_args
    }

    fn deserialize(data: &[u8], offset: usize) -> Result<(Self, usize)> {
        if offset > data.len() {
            bail!(
                "Initial offset {} is out of bounds for data length {}",
                offset,
                data.len()
            );
        }

        // Use a cursor over the relevant part of the data slice
        let mut cursor = Cursor::new(&data[offset..]);

        // Helper to read a string from the cursor
        // Assumes string is length-prefixed (u32) then UTF-8 bytes
        let read_string_from_cursor = |c: &mut Cursor<&[u8]>| -> Result<String> {
            let len = c
                .read_u32::<LittleEndian>()
                .context("Failed to read string length")? as usize;
            // Ensure cursor has enough data for the string content
            if (c.position() as usize + len) > c.get_ref().len() {
                bail!(
                    "Not enough data in cursor for string content of length {} (already read {} bytes from cursor, total slice len {})",
                    len,
                    c.position(),
                    c.get_ref().len()
                );
            }
            let mut string_buf = vec![0u8; len];
            c.read_exact(&mut string_buf)
                .context("Failed to read string content")?;
            String::from_utf8(string_buf).context("Failed to decode UTF-8 string")
        };

        // Helper to read a byte vector from the cursor
        // Assumes Vec<u8> is length-prefixed (u32) then bytes
        let read_bytes_vec_from_cursor = |c: &mut Cursor<&[u8]>| -> Result<Vec<u8>> {
            let len = c
                .read_u32::<LittleEndian>()
                .context("Failed to read bytes length")? as usize;
            if (c.position() as usize + len) > c.get_ref().len() {
                bail!(
                    "Not enough data in cursor for byte vector of length {} (already read {} bytes from cursor, total slice len {})",
                    len,
                    c.position(),
                    c.get_ref().len()
                );
            }
            let mut bytes_buf = vec![0u8; len];
            c.read_exact(&mut bytes_buf)
                .context("Failed to read bytes content")?;
            Ok(bytes_buf)
        };

        let router_address = read_string_from_cursor(&mut cursor).with_context(|| {
            format!(
                "Deserializing router_address from data at original offset {}",
                offset
            )
        })?;

        let pool_address = read_string_from_cursor(&mut cursor).with_context(|| {
            format!(
                "Deserializing pool_address from data at original offset {}",
                offset
            )
        })?;

        let token_in_address = read_string_from_cursor(&mut cursor).with_context(|| {
            format!(
                "Deserializing token_in_address from data at original offset {}",
                offset
            )
        })?;

        let token_out_address = read_string_from_cursor(&mut cursor).with_context(|| {
            format!(
                "Deserializing token_out_address from data at original offset {}",
                offset
            )
        })?;

        // U256 (32 bytes)
        if (cursor.position() as usize + BYTES_256_OFFSET) > cursor.get_ref().len() {
            bail!(
                "Not enough data in cursor for U256 (amount_in) (already read {} bytes from cursor, total slice len {})",
                cursor.position(),
                cursor.get_ref().len()
            );
        }

        let mut amount_in_bytes = [0u8; BYTES_256_OFFSET];
        cursor.read_exact(&mut amount_in_bytes).with_context(|| {
            format!(
                "Deserializing amount_in (U256) from data at original offset {}",
                offset
            )
        })?;

        let amount_in = U256::from_le_bytes(amount_in_bytes);

        let mut min_amount_out_bytes = [0u8; BYTES_256_OFFSET];

        cursor
            .read_exact(&mut min_amount_out_bytes)
            .with_context(|| {
                format!(
                    "Deserializing min_amount_out (U256) from data at original offset {}",
                    offset
                )
            })?;

        let min_amount_out = U256::from_le_bytes(min_amount_out_bytes);

        let bin_step = cursor.read_u64::<LittleEndian>().with_context(|| {
            format!(
                "Deserializing bin_step (u64) from data at original offset {}",
                offset
            )
        })?;

        let to_address = read_string_from_cursor(&mut cursor).with_context(|| {
            format!(
                "Deserializing to_address from data at original offset {}",
                offset
            )
        })?;

        let deadline = cursor.read_u64::<LittleEndian>().with_context(|| {
            format!(
                "Deserializing deadline (u64) from data at original offset {}",
                offset
            )
        })?;

        // bool (as u8)
        if (cursor.position() as usize + 1) > cursor.get_ref().len() {
            bail!(
                "Not enough data in cursor for bool (is_dusa) (already read {} bytes from cursor, total slice len {})",
                cursor.position(),
                cursor.get_ref().len()
            );
        }
        let is_dusa_byte = cursor.read_u8().with_context(|| {
            format!(
                "Deserializing is_dusa (bool) from data at original offset {}",
                offset
            )
        })?;
        let is_dusa = is_dusa_byte != 0;

        // u64
        let coin_to_use_len = 8; // u64 is 8 bytes
        if (cursor.position() as usize + coin_to_use_len) > cursor.get_ref().len() {
            bail!(
                "Not enough data in cursor for u64 (coin_to_use) (already read {} bytes from cursor, total slice len {})",
                cursor.position(),
                cursor.get_ref().len()
            );
        }
        let coin_to_use = cursor.read_u64::<LittleEndian>().with_context(|| {
            format!(
                "Deserializing coin_to_use (u64) from data at original offset {}",
                offset
            )
        })?;

        let swap_params = read_bytes_vec_from_cursor(&mut cursor).with_context(|| {
            format!(
                "Deserializing swap_params from data at original offset {}",
                offset
            )
        })?;

        let bytes_read = cursor.position() as usize;

        Ok((
            ArbRoute {
                router_address,
                pool_address,
                token_in_address,
                token_out_address,
                amount_in,
                min_amount_out,
                bin_step,
                to_address,
                deadline,
                is_dusa,
                coin_to_use,
                swap_params,
            },
            offset + bytes_read, // New offset in the original data slice
        ))
    }
}

// --- Example Usage and Tests ---
#[cfg(test)]
mod tests {
    use crate::{
        config::current::{
            ARB_CONTRACT_ADDRESS, DUSA_SWAP_ROUTER_ADDRESS, EAGLEFI_POOLS_ADDRESSES,
            EAGLEFI_SWAP_ROUTER_ADDRESS, WMAS_ADDRRESS,
        },
        executor::allownace::increase_max_allowance_arb_to_router,
    };

    use super::*;
    use anyhow::Result;
    use rust_massa_web3::{
        alloy_primitives::utils::parse_units, client::grpc_client::PublicGrpcClient,
        constants::MAX_GAS_CALL, massa_proto_rs::massa::model::v1::OperationExecutionStatus,
    };

    #[tokio::test]
    async fn test_execute_arb_route() -> Result<()> {
        // 1. Test dusa
        let mut grpc_client = PublicGrpcClient::new_from_env()
            .await
            .context("Failed to init massa grpc client")?;

        let amount = "1";
        let min_amount_out = U256::ZERO;
        let amount_in: U256 = parse_units(amount, 9)
            .context("Failed to parse units")?
            .try_into()
            .context("Failed to convert parsed units to U256")?;

        let wmas_address = WMAS_ADDRRESS.to_string();
        let usdc_address = "AS12N76WPYB3QNYKGhV2jZuQs1djdhNJLQgnm7m52pHWecvvj1fCQ".to_string(); // usdc

        let token_in_address = WMAS_ADDRRESS.to_string();
        let token_out_address = usdc_address.clone();
        let arb_contract_address = ARB_CONTRACT_ADDRESS;

        // Firs thing to do is to increase allowance of the arb contract to dusa to  use the token in
        let _ = increase_max_allowance_arb_to_router(
            &DUSA_SWAP_ROUTER_ADDRESS,
            &WMAS_ADDRRESS,
            &mut grpc_client,
        )
        .await
        .context("Failed to increase allowance of the arb contract to dusa to use the wmas")?;

        // increase allowance of the arb contract to  dusa to use the token out
        let _ = increase_max_allowance_arb_to_router(
            &DUSA_SWAP_ROUTER_ADDRESS,
            &usdc_address,
            &mut grpc_client,
        )
        .await
        .context("Failed to increase allowance of the arb contract to dusa to use the usdc")?;

        // increase allowance of the arb contract to  eaglefi to use the token out
        let _ = increase_max_allowance_arb_to_router(
            &EAGLEFI_SWAP_ROUTER_ADDRESS,
            &usdc_address,
            &mut grpc_client,
        )
        .await
        .context("Failed to increase allowance of the arb contract to eaglefi to use the usdc")?;

        // increase allowance of the arb contract to  eaglefi to use the token in
        let _ = increase_max_allowance_arb_to_router(
            &EAGLEFI_SWAP_ROUTER_ADDRESS,
            &WMAS_ADDRRESS,
            &mut grpc_client,
        )
        .await
        .context("Failed to increase allowance of the arb contract to eaglefi to use the wmas")?;

        // first increase allowance of the contract to use the token in
        let args = Args::new()
            .add_string(arb_contract_address)
            .add_u256(amount_in)
            .serialize();

        let expire_period = grpc_client
            .get_absolute_expire_period()
            .await
            .expect("Failed to get absolute expire period");

        let fee = "0.01";

        let increase_allowance_operation_id = grpc_client
            .call_sc(
                &token_in_address,
                "increaseAllowance",
                args,
                fee,
                MAX_GAS_CALL,
                0.01,
                expire_period,
            )
            .await
            .context("Failed to call smart contract")?;

        println!(
            "Operation ID of increase allowance: {}",
            increase_allowance_operation_id
        );

        // wait for the operation to complete speculative
        let increase_allowance_operation_status = grpc_client
            .wait_for_operation(increase_allowance_operation_id, true)
            .await
            .expect("Failed to wait for operation");

        println!(
            "Operation status of increase allowance: {}",
            increase_allowance_operation_status
        );

        assert_eq!(
            increase_allowance_operation_status,
            OperationExecutionStatus::Success as i32
        );

        let bin_step: u64 = 20;

        let coin_to_use: u64 = 1 * 10u64.pow(9);

        let current_time = std::time::SystemTime::now()
            .duration_since(std::time::UNIX_EPOCH)
            .expect("Time went backwards")
            .as_millis() as u64;

        let deadline = current_time + 60 * 10 * 1000; // 10 minutes from now

        dbg!("Current time: {:?}", current_time);
        dbg!("Deadline: {:?}", deadline);

        let arb_route = ArbRoute {
            router_address: DUSA_SWAP_ROUTER_ADDRESS.to_string(),
            pool_address: "DUSA_POOL_ADDRESS".to_string(),
            token_in_address,
            token_out_address,
            amount_in,
            min_amount_out,
            bin_step,
            to_address: ARB_CONTRACT_ADDRESS.to_string(),
            deadline,
            is_dusa: true,
            coin_to_use,
            swap_params: vec![],
        };

        let amount = "0.489762";

        let amount_in: U256 = parse_units(amount, 6)
            .context("Failed to parse units")?
            .try_into()
            .context("Failed to convert parsed units to U256")?;

        let min_amount_out = U256::from(100);

        let arb_route_second_eagle = ArbRoute {
            router_address: EAGLEFI_SWAP_ROUTER_ADDRESS.to_string(),
            pool_address: EAGLEFI_POOLS_ADDRESSES[0].to_string(),
            token_in_address: usdc_address.clone(),
            token_out_address: wmas_address.clone(),
            amount_in,
            min_amount_out,
            bin_step,
            to_address: "AU1Ru6cfN65sUaWycZaerWcUUjRMakgLLuFiWJeTVUAEm9knwpB7".to_string(),
            deadline,
            is_dusa: false,
            coin_to_use,
            swap_params: vec![],
        };

        let args = Args::new()
            .add_serializable_object_array(&[arb_route, arb_route_second_eagle])
            .serialize();

        let expire_period = grpc_client
            .get_absolute_expire_period()
            .await
            .expect("Failed to get absolute expire period");

        let coins = 2.0;

        let operation_id = grpc_client
            .call_sc(
                arb_contract_address,
                "execute",
                args,
                fee,
                MAX_GAS_CALL,
                coins,
                expire_period,
            )
            .await
            .context("Failed to call smart contract")?;

        println!("Operation ID of Execute: {}", operation_id);

        // wait for the operation to complete speculative
        let operation_status = grpc_client
            .wait_for_operation(operation_id, true)
            .await
            .expect("Failed to wait for operation");

        println!("Operation status: {}", operation_status);

        assert_eq!(operation_status, OperationExecutionStatus::Success as i32);

        Ok(())
    }
}
