import { Args } from '@massalabs/as-types';
import { Context, generateEvent, Storage } from '@massalabs/massa-as-sdk';

const NAME_KEY = 'NAME_KEY';

/**
 *
 * This function is meant to be called only one time: when the contract is deployed.
 *
 * @param binaryArgs - Arguments serialized with Args
 */
export function constructor(_: StaticArray<u8>): void {
  // This line is important. It ensures that this function can't be called in the future.
  // If you remove this check, someone could call your constructor function and reset your smart contract.
  assert(Context.isDeployingContract());

  Storage.set(NAME_KEY, 'Ayoub');

  generateEvent('Constructor called');
}

export function setName(binaryArgs: StaticArray<u8>): void {
  const args = new Args(binaryArgs);

  const name = args.nextString().expect('Name is missing');

  Storage.set(NAME_KEY, name);

  generateEvent(`Name set to ${name}`);
}
