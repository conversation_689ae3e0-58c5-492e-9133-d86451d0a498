use crate::state::{AppState, GLOBAL_START_AMOUNTS};
use crate::types::TokenMatrix;
use crate::{config::CONFIG, types::InitArbPoolsResult};
use anyhow::{Context, Result};
use dexes::{dusa, eaglefi};
use executor::allownace::increase_max_allowance_arb_for_all_tokens;
use helpers::detect_and_execute_arb;
use helpers::{display::print_tokens_indices_from_matrix, sparse::export_to_png};
use lettre::SmtpTransport;
use rust_massa_web3::{client::grpc_client::PublicGrpcClient, types::ChainId};
use rustyline::{DefaultEditor, error::ReadlineError};
use state::GlobalState;
use std::{
    collections::{BTreeMap, HashMap, HashSet},
    sync::Arc,
};
use tokio::sync::Mutex;
use tracing::{error, info, warn};
use tracing_subscriber::{EnvFilter, fmt, layer::SubscriberExt, util::SubscriberInitExt};
use types::{DEX, Pool, Token};

pub mod config;
pub mod dexes;
pub mod executor;
pub mod helpers;
pub mod serializable;
pub mod spawns;
pub mod state;
pub mod strategies;
pub mod types;

async fn start_quedge(app_state: Arc<AppState>) -> Result<()> {
    let pools_hash_map = &app_state.pools;
    let grpc_client = app_state.grpc_client.clone();
    let mailer = app_state.mailer.clone();

    // Build the token matrix from the pools hash map
    let token_matrix = strategies::bellman_ford_matrix::build_sparse_matrix(pools_hash_map).await;

    // Display the sparse matrix
    // info!("Displaying the sparse matrix at the start...");
    // print_sparse_matrix(&token_matrix);

    // Export the sparse matrix to a png file
    info!("Exporting the sparse matrix to a png file...");
    let cs_matrix = token_matrix.sparse_matrix.to_csr();

    match export_to_png(
        &cs_matrix,
        &token_matrix.token_indices,
        "./graphs/sparse_matrix.dot",
        "./graphs/sparse_matrix.png",
    ) {
        Ok(_) => info!("Exported the sparse matrix to a png file successfully!"),
        Err(err) => error!("Failed to export the sparse matrix to a png file: {}", err),
    };

    // Update the token matrix in the app state
    let mut token_matrix_guard = app_state.token_matrix.lock().await;

    *token_matrix_guard = token_matrix;

    // Drop the mutex guard
    drop(token_matrix_guard);

    // info!("Displaying the tokens indices from the sparse matrix at the start...");
    // print_tokens_indices_from_matrix(&token_matrix, &all_dusa_tokens, &all_eaglefi_tokens);

    // Open thread that will fetch the arbiter native coin balance and send email if balance is less than 1 MASSA
    let grpc_client_clone = grpc_client.clone();
    let mailer_clone = mailer.clone();

    tokio::spawn(async move {
        if let Err(e) =
            spawns::balance_notifier::start_balance_notifier(grpc_client_clone, &mailer_clone).await
        {
            error!("Fail in the balance notifier thread: {}", e);

            // Send an email notification
            if let Err(email_err) = helpers::email::send_email_notification(
                "Quedge Bot Balance Notifier",
                format!("Failed in the balance notifier thread: {}", e),
                &mailer_clone,
            )
            .await
            {
                error!(
                    "Failed to send an error email in balance notifier: {}",
                    email_err
                );
            }
        };
    });

    // Run a tokio spawn to fetch blockchain event of those pools
    let swap_handler = tokio::spawn(spawns::swap_fetcher::start_swap_fetcher(app_state.clone()));

    // Open thread that will detect and execute arbitrage on the start
    let detect_and_execute_arb_handler = tokio::spawn(async move {
        if let Err(e) = detect_and_execute_arb(app_state.clone()).await {
            error!("Error in detect_and_execute_arb at the start: {}", e);
        }
    });

    // Wait for the decting at the start to finish
    detect_and_execute_arb_handler.await?;

    // wait for the swap event spawn to finish
    swap_handler.await??;

    Ok(())
}

#[tokio::main]
async fn main() -> Result<()> {
    dotenvy::dotenv().ok();

    // Initialize the logger logic
    let file_appender = tracing_appender::rolling::daily("./logs", "quedge.log");
    let (file_writer, _guard) = tracing_appender::non_blocking(file_appender);

    // Console writer (stdout)
    let console_layer = fmt::layer().pretty(); // Optional: makes console output prettier

    // File layer
    let file_layer = fmt::layer().with_writer(file_writer).with_ansi(false); // don't add colors to the file logs

    // 🔥 Only accept logs that match your crate
    let filter = EnvFilter::new("quedge=debug");

    // Combine both
    tracing_subscriber::registry()
        .with(filter)
        .with(console_layer)
        .with(file_layer)
        .init();

    info!("Logger initialized Successfully");

    // Init massa grpc client from rust-massa-web3
    let mut grpc_client = PublicGrpcClient::new_from_env()
        .await
        .context("Failed to init massa grpc client")?;

    if CONFIG.chain == "mainnet" {
        grpc_client
            .set_chain_id(ChainId::MAINNET)
            .await
            .context("Failed to set mainnet chain id")?;
    }

    let init_arb_pools_result = init_arb_pools(grpc_client.clone()).await?;

    let mut attempt = 0;
    let max_retries = config::current::MAX_RETRIES;

    let mailer = helpers::email::init_mailer()
        .await
        .context("Failed to init mailer for email notifications")?;

    // Pint the allowed start tokens
    let allowed_start_tokens = GLOBAL_START_AMOUNTS
        .tokens
        .keys()
        .cloned()
        .collect::<Vec<String>>();

    info!("Allowed start tokens: {:?}", allowed_start_tokens);

    // Initiallize the app_state
    let app_state = Arc::new(AppState {
        pools: init_arb_pools_result.pools_hash_map,
        grpc_client: grpc_client,
        token_matrix: Arc::new(Mutex::new(TokenMatrix::default())),
        all_eaglefi_tokens: init_arb_pools_result.all_eaglefi_tokens,
        all_dusa_tokens: init_arb_pools_result.all_dusa_tokens,
        all_tokens: init_arb_pools_result.all_tokens,
        mailer: mailer.clone(),
    });

    loop {
        match start_quedge(app_state.clone()).await {
            Ok(_) => {
                info!("start_quedge completed successfully");
                break; // Exit the loop on success
            }
            Err(e) => {
                attempt += 1;
                error!(
                    "Error in start_quedge in attempt {}/{}: {}",
                    attempt, max_retries, e
                );

                // Send an email notification
                if let Err(e) = helpers::email::send_email_notification(
                    "Quedge failed to start",
                    e.to_string(),
                    &mailer,
                )
                .await
                {
                    error!("Failed to send email notification: {}", e);
                }

                if attempt >= max_retries {
                    return Err(e.context("Exceeded maximum retry attempts while starting quedge"));
                }
            }
        }
    }

    Ok(())
}

async fn init_arb_pools(mut grpc_client: PublicGrpcClient) -> Result<InitArbPoolsResult> {
    // Init a hash map to store the pools
    let mut pools_hash_map = HashMap::new();

    // Init a hash set to store the tokens. We choose to use a hash set because we don't want to have duplicates
    let mut all_tokens: HashSet<Token> = HashSet::new();

    let mut all_dusa_tokens = HashMap::new();
    let mut all_eaglefi_tokens = HashMap::new();

    // Loop over eaglefi pools and init a pool type for each pool and add it to the hash map
    for pool_address in config::current::EAGLEFI_POOLS_ADDRESSES {
        let pool = eaglefi::get_pool_data(pool_address, &mut grpc_client)
            .await
            .context("Failed to get pool data")?;

        pools_hash_map.insert(pool_address.to_string(), Arc::new(Mutex::new(pool.clone())));
        all_tokens.insert(pool.a_token.clone());
        all_tokens.insert(pool.b_token.clone());
        all_eaglefi_tokens.insert(pool.a_token.address.clone(), pool.a_token);
        all_eaglefi_tokens.insert(pool.b_token.address.clone(), pool.b_token);
    }

    // Loop over dusa pools and init a pool type for each pool and add it to the hash map
    for pool_address in config::current::DUSA_POOLS_ADDRESSES {
        let pool = dusa::get_pool_data(pool_address, &mut grpc_client)
            .await
            .context("Failed to get pool data")?;

        pools_hash_map.insert(pool_address.to_string(), Arc::new(Mutex::new(pool.clone())));
        all_tokens.insert(pool.a_token.clone());
        all_tokens.insert(pool.b_token.clone());
        all_dusa_tokens.insert(pool.a_token.address.clone(), pool.a_token);
        all_dusa_tokens.insert(pool.b_token.address.clone(), pool.b_token);
    }

    // info!("All Pools: {:?}", &pools_hash_map.values());

    let is_increase_allowance: bool =
        std::env::var("IS_INCREASE_ALLOWENCE").unwrap_or("false".to_string()) == "true";

    if is_increase_allowance {
        info!("Increasing max allowance for all tokens....");

        // Increase MAX Allowance for the arb contract on teh dsua and eaglefi routers for all tokens
        increase_max_allowance_arb_for_all_tokens(&mut grpc_client, &all_tokens)
            .await
            .context("Failed to increase max allowance for all tokens")?;

        info!("Increased Needed max allowance for all tokens SUCCESSFULLY!");
    }

    // Insert to pools hash map the pools with same token but different DEXes (Needed for arbitrage detection ETH-D -> ETH-E = 1)
    for token in all_tokens.clone() {
        // Skip if the token is not in both DEXes
        if !(all_eaglefi_tokens.contains_key(&token.address)
            && all_dusa_tokens.contains_key(&token.address))
        {
            continue;
        }

        // Create a pool with the token and its same
        let pool_address = format!("{}-{}", token.address, token.address);
        let mut a_token = token.clone();
        a_token.dex = DEX::EagleFi;

        let mut b_token = token;
        b_token.dex = DEX::Dusa;

        pools_hash_map.insert(
            pool_address.clone(),
            Arc::new(Mutex::new(Pool {
                address: pool_address,
                a_token,
                b_token,
                a_reserve: 0.0,
                b_reserve: 0.0,
                dex: DEX::Both,
                swap_fee: 0.0,
                a_price: 1.0,
                b_price: 1.0,
                active_bin_id: 0,
                bin_step: 0,
                bins: BTreeMap::new(),
            })),
        );
    }

    return Ok(InitArbPoolsResult {
        pools_hash_map,
        all_dusa_tokens,
        all_eaglefi_tokens,
        all_tokens,
    });
}
