pub const EAGLEFI_POOLS_ADDRESSES: &[&str] = &[
    "AS12dny2u8p6DmcPnYurnzqrAG7kMjYUcSD6bDCdJfbYeadYqXaL5", // USDC.e-WMAS
                                                             // "AS1o6HpvRUEvFPeHieU1r7w2raTELDrEY1xPWciMPxpxnn9qCGgn",  // WETH.e-WMAS
                                                             // "AS1uk8tNiv94m6Z6moD1C4ifHfdWDkaPWcSjoEfAZp5rW5BbLW2q",  // DAI.e-WMAS
                                                             // "AS12EctPk6gKmhTYs42kFoV347t26XQFe1DHCbf4kuEQvQvA1mGbk", // POM-WMAS
                                                             // "AS12J3QiBoMZnBBXYCf14LmTjAHT54brK8bQKyE44WycQzF12MPQD", // BTC-WMAS
];
pub const DUSA_POOLS_ADDRESSES: &[&str] = &[
    "AS112Wdy9pM4fvLNLHQXyf7uam9waMPdG5ekr4vxCyQHPkrMMPPY", // USDC.e-WMAS
                                                            // "AS14AxjeYA1K51wwyLexL67atzS75eTCAjUm3GDYTpMeZsarEhp3", // WETH.e-WMAS
                                                            // "AS1dapimnJqDufKGSdZNi8YNGy1xPXco6XyxZ9Fojf8gmt5jEsRg", // POM-WMAS
                                                            // "AS1LedeAXXC2XrzDWrY32NuN8FtJTaAjTf6KB28SuDk5eT5RJPmg", // DAI-USDC
];
pub const WMAS_ADDRRESS: &str = "AS12FW5Rs5YN2zdpEnqwj4iHUUPt9R4Eqjq2qtpJFNKW3mn33RuLU";
pub const USDC_ADDRESS: &str = "AS12N76WPYB3QNYKGhV2jZuQs1djdhNJLQgnm7m52pHWecvvj1fCQ";
pub const FEE_SCALING_FACTOR: f64 = 10_000.0;
pub const EAGLEFI_SWAP_ROUTER_ADDRESS: &str =
    "AS12JfpytS54Pq2Pi5ZP9dZ8kxe21TmwBpoiQuMNvAgB3bSVsCs9k";
pub const DUSA_SWAP_ROUTER_ADDRESS: &str = "AS1XqtvX3rz2RWbnqLfaYVKEjM3VS5pny9yKDdXcmJ5C1vrcLEFd";
// pub const ARB_CONTRACT_ADDRESS: &str = "AS12WxAVbfFLah7hMj6TM81xZseZoJS7toAogYfc7guVH4knFBEBZ";
pub const ARB_CONTRACT_ADDRESS: &str = "AS1VJvdvuU5AZFFKWLD2yxyHsS1CE65imQxQUShqNEf4BqZ2ZHLT";
pub const MAX_DETECTED_CYCLES: usize = 5;
pub const BALANCE_NOTIFIER_INTERVAL: u64 = 60 * 60; // 1 hour
pub const BALANCE_NOTIFIER_THRESHOLD: f64 = 1.0;
pub const MAX_BIN_ID: u32 = 8_388_608;
pub const MAX_RETRIES: usize = 10;
pub const MARGIN: f64 = 0.1;
pub const MIN_PROFIT_PERCENT: f64 = 0.05;
pub const DEFAULT_START_AMOUNT: f64 = 100.0;
pub const DEFAULT_WALLET_ADDRESS: &str = "AU12Yd4kCcsizeeTEK9AZyBnuJNZ1cpp99XfCZgzS77ZKnwTFMpVE";
