pub fn get_a_token_price_from_bin_id(
    bin_id: f64,
    bin_step: f64,
    a_decimals: i32,
    b_decimals: i32,
) -> f64 {
    // Price = (1 + binStep / 10_000) ** (binId - 8388608)
    let price = (1.0 + bin_step / 10_000.0).powf((bin_id as f64) - 8_388_608.0);
    // AdjustedPrice = Price * 10 ** (aDecimals - bDecimals)
    let adjusted_price = price * 10.0_f64.powi(a_decimals - b_decimals);

    adjusted_price
}
// 8_370_999
// 8_388_608