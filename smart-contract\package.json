{"name": "my-massa-sc", "version": "0.0.2", "description": "", "main": "index.js", "scripts": {"test": "asp --summary", "build": "massa-as-compile", "clean": "<PERSON><PERSON><PERSON> build", "deploy": "npm run build && tsx src/deploy.ts", "hello": "tsx src/hello.ts", "prettier": "prettier '**/src/**/*.ts' --check && as-prettier --check assembly", "prettier:fix": "prettier '**/src/**/*.ts' --write && as-prettier --write assembly", "lint": "eslint .", "lint:fix": "eslint . --fix", "fmt:check": "npm run prettier && npm run lint", "fmt": "npm run prettier:fix && npm run lint:fix", "swap:dusa": "npm run build && tsx src/testSwapDusa.ts", "events": "tsx src/events.ts"}, "keywords": [], "author": "", "license": "ISC", "devDependencies": {"@as-pect/cli": "^8.1.0", "@assemblyscript/loader": "^0.27.29", "@massalabs/as-types": "^2.0.0", "@massalabs/eslint-config": "^0.0.11", "@massalabs/massa-as-sdk": "^3.0.0", "@massalabs/massa-sc-compiler": "^0.2.1-dev", "@massalabs/massa-web3": "^5.2.0", "@massalabs/prettier-config-as": "^0.0.2", "@massalabs/sc-standards": "^1.3.0", "@types/node": "^18.11.10", "assemblyscript": "^0.27.29", "assemblyscript-prettier": "^1.0.7", "dotenv": "^16.0.3", "prettier": "^2.8.1", "tslib": "^2.4.0", "tsx": "^4.7.0", "typescript": "^4.8.4", "as-bignum": "github:massalabs/as-bignum#0105eb596b2fa707c00712e811a2efdfcb8a9848"}, "overrides": {"visitor-as": {"assemblyscript": "$assemblyscript"}, "@massalabs/as-types": {"as-bignum": "$as-bignum"}, "@massalabs/sc-standards": {"as-bignum": "$as-bignum"}}, "type": "module", "prettier": "@massalabs/prettier-config-as", "engines": {"node": ">=16"}}