import 'dotenv/config';
import {
  Account,
  Arg<PERSON>,
  Ma<PERSON>,
  SmartContract,
  JsonRpcProvider,
  OperationStatus,
  parseUnits,
  ArrayTypes,
  MRC20,
} from '@massalabs/massa-web3';
import { getScByteCode } from './utils';

const wmasAddress = 'AS12FW5Rs5YN2zdpEnqwj4iHUUPt9R4Eqjq2qtpJFNKW3mn33RuLU';
const usdcAddress = 'AS12N76WPYB3QNYKGhV2jZuQs1djdhNJLQgnm7m52pHWecvvj1fCQ';
const usdcDecimals = 6;
const wmasDecimals = 9;
const dusaRouterAddress =
  'AS1XqtvX3rz2RWbnqLfaYVKEjM3VS5pny9yKDdXcmJ5C1vrcLEFd';

const account = await Account.fromEnv();
const provider = JsonRpcProvider.buildnet(account);

console.log('Deploying contract...');

const byteCode = getScByteCode('build', 'testSwap.wasm');

const constructorArgs = new Args();

const contract = await SmartContract.deploy(
  provider,
  byteCode,
  constructorArgs,
  { coins: Mas.fromString('0.01') },
);

console.log('Contract deployed at:', contract.address);

const events = await provider.getEvents({
  smartContractAddress: contract.address,
});

for (const event of events) {
  console.log('Event message:', event.data);
}

// Increase the dusa router allownace to this contract to max amount
const usdc = new MRC20(provider, usdcAddress);

const allownaceOperation = await contract.call(
  'increaseAllowanceForDusa',
  new Args().addString(usdcAddress),
  { coins: Mas.fromString('0.01') },
);

console.log('allownaceOperation sent:', allownaceOperation.id);

const allownaceStatus = await allownaceOperation.waitSpeculativeExecution();

console.log('allownaceOperation status:', allownaceStatus);

const eventsAfterAllownace = await allownaceOperation.getSpeculativeEvents();

console.log('Events after allownace:', eventsAfterAllownace);

if (allownaceStatus == OperationStatus.SpeculativeSuccess) {
  console.log('allownaceOperation succeeded');
} else {
  console.log('allownaceOperation failed');
  throw new Error('Failed to increase allowance');
}

// send the amount of USDC to the contract

const usdcAmount = parseUnits('1', usdcDecimals);

const transferOperation = await usdc.transfer(contract.address, usdcAmount);

console.log('transferOperation sent:', transferOperation.id);

const transferStatus = await transferOperation.waitSpeculativeExecution();

console.log('transferOperation status:', transferStatus);

const eventsAfterTransfer = await transferOperation.getSpeculativeEvents();

console.log('Events after transfer:', eventsAfterTransfer);

if (transferStatus == OperationStatus.SpeculativeSuccess) {
  console.log('transferOperation succeeded');
} else {
  console.log('transferOperation failed');
  throw new Error('Failed to transfer USDC');
}

const wmasContract = new MRC20(provider, wmasAddress);

// Get the contract wmas balance before the swap
const wmasBalanceBefore = await wmasContract.balanceOf(account.address.toString());
console.log('wmas balance before swap:', wmasBalanceBefore.toString());

const amountIn = parseUnits('1', usdcDecimals);
const amountOutMin = parseUnits('0.01', wmasDecimals);
const path = [usdcAddress, wmasAddress];
const to = account.address.toString();
const binStep = 20n;

const deadline = BigInt(Math.floor(new Date().getTime()) + 1000 * 60 * 10); // 10 minutes from now
const masToSend = Mas.fromString('0.5'); // 1 Massa

const swapArgs = new Args()
  .addU256(amountIn)
  .addString(to)
  .addU64(binStep)
  .addArray(path, ArrayTypes.STRING)
  .addU64(deadline)
  .addU64(masToSend);

const swapOperation = await contract.call('swapDusa', swapArgs, {
  coins: Mas.fromString('1'),
});

console.log('swapOperation sent:', swapOperation.id);

const status = await swapOperation.waitSpeculativeExecution();

console.log('swapOperation status:', status);

const eventsAfterSwap = await swapOperation.getSpeculativeEvents();

console.log('Events after swap:', eventsAfterSwap);

if (status == OperationStatus.SpeculativeSuccess) {
  console.log('swapOperation succeeded');
} else {
  console.log('swapOperation failed');
  throw new Error('Failed to swap');
}

// Get the contract wmas balance after the swap
const wmasBalanceAfter = await wmasContract.balanceOf(account.address.toString());
console.log('wmas balance after swap:', wmasBalanceAfter.toString());

const difference = wmasBalanceAfter - wmasBalanceBefore;
console.log('wmas balance difference:', difference.toString());
