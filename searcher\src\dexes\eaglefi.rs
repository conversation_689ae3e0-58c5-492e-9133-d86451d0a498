use std::collections::BTreeMap;

use anyhow::{Context, Result};
use rust_massa_web3::alloy_primitives::utils::format_units;
use rust_massa_web3::{
    basic_elements::serializers::{bytes_to_string, bytes_to_u8, bytes_to_u64, bytes_to_u256},
    client::grpc_client::PublicGrpcClient,
    types::ReadStorageKey,
};
use tracing::debug;

use crate::{
    config,
    types::{DEX, Pool, Token},
};

pub async fn get_pool_data(pool_address: &str, grpc_client: &mut PublicGrpcClient) -> Result<Pool> {
    // get the aToken and bToken addresses and decimals address from the pool address
    let a_token_storage_key = ReadStorageKey {
        smart_contract_address: pool_address.to_string(),
        key: "tokenA".to_string(),
    };

    let b_token_storage_key = ReadStorageKey {
        smart_contract_address: pool_address.to_string(),
        key: "tokenB".to_string(),
    };

    let a_token_decimals_storage_key = ReadStorageKey {
        smart_contract_address: pool_address.to_string(),
        key: "aTokenDecimals".to_string(),
    };

    let b_token_decimals_storage_key = ReadStorageKey {
        smart_contract_address: pool_address.to_string(),
        key: "bTokenDecimals".to_string(),
    };

    let a_reserve_storage_key = ReadStorageKey {
        smart_contract_address: pool_address.to_string(),
        key: "aTokenReserve".to_string(),
    };

    let b_reserve_storage_key = ReadStorageKey {
        smart_contract_address: pool_address.to_string(),
        key: "bTokenReserve".to_string(),
    };

    let swap_fee_storage_key = ReadStorageKey {
        smart_contract_address: pool_address.to_string(),
        key: "feeRate".to_string(),
    };

    let storage_keys = vec![
        a_token_storage_key,
        b_token_storage_key,
        a_token_decimals_storage_key,
        b_token_decimals_storage_key,
        a_reserve_storage_key,
        b_reserve_storage_key,
        swap_fee_storage_key,
    ];

    // Read the storage keys from the massa client
    let result = grpc_client
        .read_storage_key(storage_keys)
        .await
        .context("Failed to read storage keys from eaglefi pool")?;

    let a_token_address = bytes_to_string(&result[0].candidate_value);
    let b_token_address = bytes_to_string(&result[1].candidate_value);
    let a_token_decimals = bytes_to_u8(&result[2].candidate_value);
    let b_token_decimals = bytes_to_u8(&result[3].candidate_value);
    let a_reserve_u256 = bytes_to_u256(&result[4].candidate_value);
    let b_reserve_u256 = bytes_to_u256(&result[5].candidate_value);
    let swap_fee_scaled = bytes_to_u64(&result[6].candidate_value);

    let a_reserve: f64 = format_units(a_reserve_u256, a_token_decimals)
        .context("Failed to format a reserve")?
        .parse()
        .context("Failed to parse a reserve")?;

    let b_reserve: f64 = format_units(b_reserve_u256, b_token_decimals)
        .context("Failed to format b reserve")?
        .parse()
        .context("Failed to parse b reserve")?;

    let swap_fee = swap_fee_scaled as f64 / config::current::FEE_SCALING_FACTOR;

    let a_price = b_reserve / a_reserve;
    let b_price = a_reserve / b_reserve;

    // Read storage keys fo tokens names and symbols
    let a_token_name_storage_key = ReadStorageKey {
        smart_contract_address: a_token_address.clone(),
        key: "NAME".to_string(),
    };

    let a_token_symbol_storage_key = ReadStorageKey {
        smart_contract_address: a_token_address.clone(),
        key: "SYMBOL".to_string(),
    };

    let b_token_name_storage_key = ReadStorageKey {
        smart_contract_address: b_token_address.clone(),
        key: "NAME".to_string(),
    };

    let b_token_symbol_storage_key = ReadStorageKey {
        smart_contract_address: b_token_address.clone(),
        key: "SYMBOL".to_string(),
    };

    let storage_keys = vec![
        a_token_name_storage_key,
        a_token_symbol_storage_key,
        b_token_name_storage_key,
        b_token_symbol_storage_key,
    ];

    let result = grpc_client
        .read_storage_key(storage_keys)
        .await
        .context("Failed to read storage keys from eaglefi pool")?;

    let a_token_name = bytes_to_string(&result[0].candidate_value);
    let a_token_symbol = bytes_to_string(&result[1].candidate_value);
    let b_token_name = bytes_to_string(&result[2].candidate_value);
    let b_token_symbol = bytes_to_string(&result[3].candidate_value);

    Ok(Pool {
        address: pool_address.to_string(),
        a_token: Token {
            address: a_token_address,
            name: a_token_name,
            symbol: a_token_symbol,
            decimals: a_token_decimals,
            dex: DEX::EagleFi,
        },
        b_token: Token {
            address: b_token_address,
            name: b_token_name,
            symbol: b_token_symbol,
            decimals: b_token_decimals,
            dex: DEX::EagleFi,
        },
        a_reserve,
        b_reserve,
        dex: DEX::EagleFi,
        swap_fee,
        a_price,
        b_price,
        active_bin_id: 0,
        bin_step: 0,
        bins: BTreeMap::new(),
    })
}

pub async fn update_pool_data(pool: &Pool, grpc_client: &mut PublicGrpcClient) -> Result<Pool> {
    let pool_address = pool.address.clone();

    let a_reserve_storage_key = ReadStorageKey {
        smart_contract_address: pool_address.to_string(),
        key: "aTokenReserve".to_string(),
    };

    let b_reserve_storage_key = ReadStorageKey {
        smart_contract_address: pool_address.to_string(),
        key: "bTokenReserve".to_string(),
    };

    let storage_keys = vec![a_reserve_storage_key, b_reserve_storage_key];

    // Read the storage keys from the massa client
    let result = grpc_client
        .read_storage_key(storage_keys)
        .await
        .context("Failed to read storage keys from eaglefi pool")?;

    let a_reserve_u256 = bytes_to_u256(&result[0].candidate_value);
    let b_reserve_u256 = bytes_to_u256(&result[1].candidate_value);

    let a_reserve: f64 = format_units(a_reserve_u256, pool.a_token.decimals)
        .context("Failed to format a reserve")?
        .parse()
        .context("Failed to parse a reserve")?;

    let b_reserve: f64 = format_units(b_reserve_u256, pool.b_token.decimals)
        .context("Failed to format b reserve")?
        .parse()
        .context("Failed to parse b reserve")?;

    let a_price = b_reserve / a_reserve;

    let b_price = a_reserve / b_reserve;

    let mut updated_pool = pool.clone();

    updated_pool.a_reserve = a_reserve;
    updated_pool.b_reserve = b_reserve;
    updated_pool.a_price = a_price;
    updated_pool.b_price = b_price;

    Ok(updated_pool)
}

/// Estimate the amount of tokens that will be received when swapping a given amount of tokens
pub fn get_swap_out_estimation(pool: &Pool, amount_in: f64, token_in_address: &str) -> f64 {
    let input_fee = pool.swap_fee;
    let total_fee = input_fee * amount_in / 100.0;
    let amount_in_after_fee = amount_in - total_fee;

    let a_token = &pool.a_token;

    let (reserve_in, reserve_out) = if token_in_address == a_token.address {
        (pool.a_reserve, pool.b_reserve)
    } else {
        (pool.b_reserve, pool.a_reserve)
    };

    let amount_out = (reserve_out * amount_in_after_fee) / (reserve_in + amount_in_after_fee);

    // debug!(
    //     "EagleFi Get swap out estimation for pool: {} , token_in_address: {}, amount_in: {}, amount_out: {}",
    //     pool.address, token_in_address, amount_in, amount_out
    // );

    amount_out
}
