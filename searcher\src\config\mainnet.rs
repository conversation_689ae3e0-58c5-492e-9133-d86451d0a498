pub const EAGLEFI_POOLS_ADDRESSES: &[&str] = &[
    "AS1fKChJfxdAVBUUSHya45cDrdfTXjPewjENszqeBbrW2tUS77QF", // USDC.e-WMAS MAINNET
    "AS1TYA6pT2KJ7EtN1XCxYuhKtau8Zfuk9p7zq7kzfjqXmxd7r5FQ", // USDT.b-WMAS MAINNET
    "AS12GHDPYmo1tkPeqFYqxhn18JpS2zEdCZHvDFZkeZXKovjJ2Daku", // DAI.e-WMAS MAINNET
    "AS126PmvRK8NuSmVW3LRTgUuUw4csPW76bS9DZzn8bsGJCkhqySiz", // WETH.e-WMAS MAINNET
    "AS12gcV6XBDFdfp7h4v7iditb4GrwYjoUqE8NoVHToQmiGNxLst8y", // WETH.b-WMAS MAINNET
    "AS12jqHXc2mEK9Y3MQFkAiLDW2SycmqGB3fSTHbBkk3MvthEnjcjH", // WBTC.e-WMAS MAINNET
    "AS1rqoFPBh9brRpDGqt2dsG4hPimfqFhU2VdaFhEqGnd7Pn1jWYM", // PUR-WMAS MAINNET
    "AS12jUaG9pZbv47nfeeSm5Z8u4nxUzaTiCvBSXB2hGspwiQN7WrNU", // POM-WMAS MAINNET
];
pub const DUSA_POOLS_ADDRESSES: &[&str] = &[
    "AS12Q5NyCQUtEBTnnqqBwcGyYb18szbbKv5GArcdk9tm2HitTupHw", // WMAS-USDC MAIN
    "AS12dLJdfrFaTv8yz4v7YEnbUZY386joSi4kcSZseiiFFJt6E7kn",  // POM-WMAS MAINNET
    "AS12FWAhf84FELsvBXe8LJEJHTKzfK7PwopsaJRwJshTfcRjQBy1o", // PUR-USDC MAIN
    "AS127CXxgeuu6A8UitxJwdnZ3SoXJFzTzEiKTZtaxzmEXXeoE1Wxc", // PUR-WMAS MAINNET
    "AS1F2SAbhvknGLQKzfjaizKiZhEEd6mS1HLpZqPc2atzKE4VTNv9",  // USDT-USDC MAINNET
    "AS12QDpZFi8szHCfyNsvB3JWT9dmHbvDx9uSjMLQ8rHVPN67rrG9n", // DAI-USDC MAINNET
    "AS1vQsWBLPdJFdAu75Cgh2eAqYp2rvcaurRjSfHjcfirxboLT3mG",  // weth.b-weth.e
    "AS1Ba1T2mMpHvLEhTvsNkSDqm4djno2ezVojGrtLf1wRd8ThfZD3",  // weth.e-wmas
    "AS12AsqyNwL3WV8oaX6aiaPebZiZKncFg1xunPGRMo4GmY9vVz54J", // wbtc.e-usdc.e
];
pub const WMAS_ADDRRESS: &str = "AS12U4TZfNK7qoLyEERBBRDMu8nm5MKoRzPXDXans4v9wdATZedz9";
pub const USDC_ADDRESS: &str = "AS1hCJXjndR4c9vekLWsXGnrdigp4AaZ7uYG3UKFzzKnWVsrNLPJ";
pub const FEE_SCALING_FACTOR: f64 = 10_000.0;
pub const EAGLEFI_SWAP_ROUTER_ADDRESS: &str =
    "AS1xoKcDq46fwKccuoit46KPzbXyKokPnxi6GWq8y4Xxk9ACsJ2g";
pub const DUSA_SWAP_ROUTER_ADDRESS: &str = "AS12UMSUxgpRBB6ArZDJ19arHoxNkkpdfofQGekAiAJqsuE6PEFJy";
// pub const ARB_CONTRACT_ADDRESS: &str = "AS16B7fJejvRnRyZVMZvHJh55Mg2mtVhXo7GdA5UgY78bmJ3r5Z4"; // Mainnet
pub const ARB_CONTRACT_ADDRESS: &str = "AS15LEHwa6EYNopyToFLHUcHWSmY4YecmnsQJVkCnS9zDrU5KsAS"; // Mainnet Secondary
pub const MAX_DETECTED_CYCLES: usize = 50;
pub const BALANCE_NOTIFIER_INTERVAL: u64 = 60 * 60; // 1 hour
pub const BALANCE_NOTIFIER_THRESHOLD: f64 = 1.0;
pub const MAX_BIN_ID: u32 = 8_388_608;
pub const MAX_RETRIES: usize = 10;
pub const MARGIN: f64 = 0.06;
pub const MIN_PROFIT_PERCENT: f64 = 0.05;
pub const DEFAULT_START_AMOUNT: f64 = 100.0;
pub const DEFAULT_WALLET_ADDRESS: &str = "AU16raXifo3FExijWrhUBLBneaHnWGicWbShgyXgAKB1bnj1yLvH";
