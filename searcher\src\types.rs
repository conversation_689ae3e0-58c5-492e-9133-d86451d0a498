use petgraph::prelude::*;
use sprs::TriMat;

use std::collections::{BTreeMap, HashMap, HashSet};

use crate::state::SharedPool;

#[derive(<PERSON>lone, Debug, Eq, PartialEq, Hash)]
pub struct Token {
    pub address: String,
    pub name: String,
    pub symbol: String,
    pub decimals: u8,
    pub dex: DEX,
}

#[derive(Debug, Clone, PartialEq)]
pub struct Pool {
    pub address: String,
    pub a_token: Token,
    pub b_token: Token,
    pub a_reserve: f64,
    pub b_reserve: f64,
    pub dex: DEX,
    pub swap_fee: f64,
    pub a_price: f64, // price of token A in terms of token B
    pub b_price: f64, // price of token B in terms of token A
    pub active_bin_id: u32,
    pub bin_step: u32,
    pub bins: BTreeMap<u32, Bin>,
}

#[derive(Debug, Clone, PartialEq)]
pub struct Bin {
    pub id: u32,
    pub a_reserve: f64,
    pub b_reserve: f64,
}

#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>)]
pub struct PoolArb {
    pub pool: Pool,
    pub is_a_in: bool,
}

#[derive(Debu<PERSON>, <PERSON><PERSON>, PartialEq, Eq, Hash)]
pub enum DEX {
    EagleFi,
    Dusa,
    Both,
}

impl Default for Token {
    fn default() -> Self {
        Self {
            address: String::new(),
            name: String::new(),
            symbol: String::new(),
            decimals: 0,
            dex: DEX::Both,
        }
    }
}

impl Default for Pool {
    fn default() -> Self {
        Self {
            address: String::new(),
            a_token: Token::default(),
            b_token: Token::default(),
            a_reserve: 0.0,
            b_reserve: 0.0,
            dex: DEX::Both,
            swap_fee: 0.0,
            a_price: 0.0,
            b_price: 0.0,
            active_bin_id: 0,
            bin_step: 0,
            bins: BTreeMap::new(),
        }
    }
}

/// Helper struct for mapping tokens to matrix row/column indices
#[derive(Debug)]
pub struct TokenMatrix {
    pub token_indices: HashMap<String, usize>, // token address -> index
    pub index_tokens: Vec<String>,             // index -> token address
    pub sparse_matrix: TriMat<f64>,            // sparse matrix in triplet form
}

impl Clone for TokenMatrix {
    fn clone(&self) -> Self {
        let mut new_matrix = TriMat::new((self.sparse_matrix.rows(), self.sparse_matrix.cols()));

        // Copy all triplets from the original matrix
        for (val, (i, j)) in self.sparse_matrix.triplet_iter() {
            new_matrix.add_triplet(i, j, *val);
        }

        TokenMatrix {
            token_indices: self.token_indices.clone(),
            index_tokens: self.index_tokens.clone(),
            sparse_matrix: new_matrix,
        }
    }
}

impl Default for TokenMatrix {
    fn default() -> Self {
        TokenMatrix {
            token_indices: HashMap::new(),
            index_tokens: Vec::new(),
            sparse_matrix: TriMat::new((0, 0)),
        }
    }
}

impl TokenMatrix {
    pub fn add_token(&mut self, token: Token) {
        let key = format!("{}-{:?}", token.address, token.dex); // Unique key per token + dex
        if !self.token_indices.contains_key(&key) {
            let index = self.token_indices.len();
            self.token_indices.insert(key.clone(), index);
            self.index_tokens.push(key);
        }
    }
}

#[derive(Debug, Clone)]
pub struct EventDetails {
    pub event_id: String,
    pub event_period: u64,
    pub event_thread: u32,
    pub event_index: u64,
    pub operation_id: Option<String>,
    pub event_data: String,
    pub is_failure: bool,
    pub call_stack: Vec<String>,
    pub status: i32,
}

// imlements default for EventDetails
impl Default for EventDetails {
    fn default() -> Self {
        EventDetails {
            event_id: String::new(),
            event_period: 0,
            event_thread: 0,
            event_index: 0,
            operation_id: None,
            event_data: String::new(),
            is_failure: false,
            call_stack: Vec::new(),
            status: 0,
        }
    }
}

#[derive(Debug, Clone)]
pub struct TokenGraph {
    pub graph: DiGraph<Token, f64>,
    pub token_indices: HashMap<Token, NodeIndex>,
}

impl Default for TokenGraph {
    fn default() -> Self {
        TokenGraph {
            graph: DiGraph::new(),
            token_indices: HashMap::new(),
        }
    }
}

pub trait LnEpsilon {
    fn ln_epsilon(self) -> f64;
}

impl LnEpsilon for f64 {
    fn ln_epsilon(self) -> f64 {
        match self {
            1.0 => 0.0,
            _ => self.ln(),
        }
    }
}

pub struct InitArbPoolsResult {
    pub pools_hash_map: HashMap<String, SharedPool>,
    pub all_dusa_tokens: HashMap<String, Token>,
    pub all_eaglefi_tokens: HashMap<String, Token>,
    pub all_tokens: HashSet<Token>,
}
