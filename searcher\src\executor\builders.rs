use std::collections::{HashMap, HashSet};

use anyhow::{Context, Result};
use rust_massa_web3::alloy_primitives::{
    U256,
    utils::{format_units, parse_units},
};
use tracing::{debug, error, info, warn};

use crate::{
    config::current::{
        ARB_CONTRACT_ADDRESS, DUSA_SWAP_ROUTER_ADDRESS, EAGLEFI_SWAP_ROUTER_ADDRESS,
    },
    serializable::arb_route::ArbRoute,
    state::SharedPool,
    types::{DEX, Pool, PoolArb, Token},
};

pub async fn build_execution_path(
    cycle_path: &[Token],
    pools: &HashMap<String, SharedPool>,
) -> Vec<PoolArb> {
    let mut execution_path = Vec::new();

    // Iterate over the cycle path and build the execution path
    for window in cycle_path.windows(2) {
        let from_token = &window[0];
        let to_token = &window[1];

        if from_token.address == to_token.address {
            // Skip if the tokens are the same but on different DEXes
            continue;
        }

        // Find the pool that contains the pair
        for pool_mut in pools.values() {
            let pool = pool_mut.lock().await;

            if (pool.a_token.address == from_token.address
                && pool.a_token.dex == from_token.dex
                && pool.b_token.address == to_token.address
                && pool.b_token.dex == to_token.dex)
                || (pool.b_token.address == from_token.address
                    && pool.b_token.dex == from_token.dex
                    && pool.a_token.address == to_token.address
                    && pool.a_token.dex == to_token.dex)
            {
                let is_a_in = from_token.address == pool.a_token.address;

                execution_path.push(PoolArb {
                    pool: pool.clone(),
                    is_a_in,
                });
            }

            // Drop the pool mutex
            drop(pool);
        }
    }

    execution_path
}

pub async fn build_execution_arb_route(
    cycle_path: &[Token],
    pools: &HashMap<String, SharedPool>,
    start_amount_f64: f64,
    _slippage_percentage: f64,
) -> Result<Vec<ArbRoute>> {
    let mut execution_path: Vec<ArbRoute> = Vec::new();

    let first_token_in_decimals = cycle_path[0].decimals;

    let start_amount = parse_units(&start_amount_f64.to_string(), first_token_in_decimals)
        .context("Failed to parse units of start amount")?
        .try_into()
        .context("Failed to convert parsed units to U256")?;
    let min_amount_out = U256::ONE;

    let current_time = std::time::SystemTime::now()
        .duration_since(std::time::UNIX_EPOCH)
        .expect("Time went backwards")
        .as_millis() as u64;

    let deadline = current_time + 60 * 10 * 1000; // 10 minutes from now

    // Iterate over the cycle path and build the execution path
    for window in cycle_path.windows(2) {
        let from_token = &window[0];
        let to_token = &window[1];

        if from_token.address == to_token.address {
            // Skip if the tokens are the same but on different DEXes
            continue;
        }

        // Find the pool that contains the pair
        for pool_mut in pools.values() {
            let pool = pool_mut.lock().await;

            if (pool.a_token.address == from_token.address
                && pool.a_token.dex == from_token.dex
                && pool.b_token.address == to_token.address
                && pool.b_token.dex == to_token.dex)
                || (pool.b_token.address == from_token.address
                    && pool.b_token.dex == from_token.dex
                    && pool.a_token.address == to_token.address
                    && pool.a_token.dex == to_token.dex)
            {
                let router_address = match pool.dex {
                    DEX::EagleFi => EAGLEFI_SWAP_ROUTER_ADDRESS,
                    DEX::Dusa => DUSA_SWAP_ROUTER_ADDRESS,
                    DEX::Both => panic!("Pool should not be on both DEXes"),
                };
                let coin_to_use = match pool.dex {
                    DEX::EagleFi => (0.01 * 10.0_f64.powi(9)) as u64,
                    DEX::Dusa => (0.001 * 10.0_f64.powi(9)) as u64,
                    DEX::Both => panic!("Pool should not be on both DEXes"),
                };

                if pool.dex == DEX::Dusa {
                    let active_bin_details = pool.bins.get(&pool.active_bin_id);

                    if active_bin_details.is_none() {
                        warn!("Invalid cycle path because active bin details are not found");
                        return Ok(vec![]);
                    }

                    let active_bin_details =
                        active_bin_details.context("Failed to get active bin details")?;

                    let active_a_reserve = active_bin_details.a_reserve;
                    let active_b_reserve = active_bin_details.b_reserve;

                    let is_a_in = from_token.address == pool.a_token.address;

                    // Get the active reserve out based on the token in and out
                    let (active_reserve_out, next_reserve_out) = if is_a_in {
                        let next_bin_id = pool.active_bin_id - 1;

                        debug!(
                            "Active bin id Inside build_execution_path is {}. The next bin id is {}",
                            &pool.active_bin_id, &next_bin_id
                        );

                        let next_bin_details = pool.bins.get(&next_bin_id);

                        debug!(
                            "Next bin details  inside build_execution_path is {:?}",
                            next_bin_details
                        );

                        // If the next bin details are not found, we force the next reserve out to 0
                        let next_reserve_out = next_bin_details.map_or(0.0, |bin| bin.b_reserve);

                        (active_a_reserve, next_reserve_out)
                    } else {
                        let next_bin_id = pool.active_bin_id + 1;

                        let next_bin_details = pool.bins.get(&next_bin_id);

                        // If the next bin details are not found, we force the next reserve out to 0
                        let next_reserve_out = next_bin_details.map_or(0.0, |bin| bin.a_reserve);

                        (active_b_reserve, next_reserve_out)
                    };

                    if active_reserve_out + next_reserve_out == 0.0 {
                        warn!(
                            "Invalid cycle path because Active Out Reserve + Next Out Reserve are 0 for pool {}. The Path of the Cycle is from token ' {} ' to ' {} '. Is A Token In {}. The Full Pool is {:?}",
                            pool.address, from_token.symbol, to_token.symbol, is_a_in, pool
                        );
                        return Ok(vec![]);
                    }
                }

                let arb_route = ArbRoute {
                    router_address: router_address.to_string(),
                    pool_address: pool.address.clone(),
                    token_in_address: from_token.address.clone(),
                    token_out_address: to_token.address.clone(),
                    amount_in: start_amount,
                    min_amount_out,
                    bin_step: pool.bin_step as u64,
                    to_address: ARB_CONTRACT_ADDRESS.to_string(),
                    is_dusa: pool.dex == DEX::Dusa,
                    coin_to_use,
                    deadline,
                    swap_params: vec![],
                };

                execution_path.push(arb_route);
            }

            // Drop the pool mutex
            drop(pool);
        }
    }

    Ok(execution_path)
}
