{"name": "my-massa-sc", "version": "0.0.2", "lockfileVersion": 3, "requires": true, "packages": {"": {"name": "my-massa-sc", "version": "0.0.2", "license": "ISC", "devDependencies": {"@as-pect/cli": "^8.1.0", "@assemblyscript/loader": "^0.27.29", "@massalabs/as-types": "^2.0.0", "@massalabs/eslint-config": "^0.0.11", "@massalabs/massa-as-sdk": "^3.0.0", "@massalabs/massa-sc-compiler": "^0.2.1-dev", "@massalabs/massa-web3": "^5.2.0", "@massalabs/prettier-config-as": "^0.0.2", "@massalabs/sc-standards": "^1.3.0", "@types/node": "^18.11.10", "as-bignum": "github:massalabs/as-bignum#0105eb596b2fa707c00712e811a2efdfcb8a9848", "assemblyscript": "^0.27.29", "assemblyscript-prettier": "^1.0.7", "dotenv": "^16.0.3", "prettier": "^2.8.1", "tslib": "^2.4.0", "tsx": "^4.7.0", "typescript": "^4.8.4"}, "engines": {"node": ">=16"}}, "node_modules/@aashutoshrathi/word-wrap": {"version": "1.2.6", "dev": true, "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/@adraffy/ens-normalize": {"version": "1.10.0", "dev": true, "license": "MIT"}, "node_modules/@as-covers/assembly": {"version": "0.4.1", "dev": true, "license": "MIT"}, "node_modules/@as-covers/core": {"version": "0.4.2", "dev": true, "license": "MIT", "dependencies": {"@as-covers/assembly": "^0.4.1", "@as-covers/glue": "^0.4.2", "@as-covers/transform": "^0.4.2"}}, "node_modules/@as-covers/glue": {"version": "0.4.2", "dev": true, "license": "MIT", "dependencies": {"csv-stringify": "^6.2.3", "table": "^6.8.1"}}, "node_modules/@as-covers/transform": {"version": "0.4.2", "dev": true, "license": "MIT", "dependencies": {"line-column": "^1.0.2", "visitor-as": "^0.11.4"}}, "node_modules/@as-pect/assembly": {"version": "8.1.0", "dev": true, "license": "MIT"}, "node_modules/@as-pect/cli": {"version": "8.1.0", "dev": true, "license": "MIT", "dependencies": {"@as-covers/core": "0.4.2", "@as-covers/transform": "0.4.2", "@as-pect/assembly": "^8.1.0", "@as-pect/core": "^8.1.0", "@as-pect/transform": "^8.1.0", "chalk": "^5.2.0", "chalk-template": "^0.4.0", "commander": "^9.4.1", "glob": "^8.0.3", "glob-promise": "^5.0.0"}, "bin": {"asp": "bin/asp.js", "aspect": "bin/asp.js"}, "optionalDependencies": {"@as-pect/csv-reporter": "^8.1.0", "@as-pect/json-reporter": "^8.1.0"}, "peerDependencies": {"assemblyscript": "^0.27.2"}}, "node_modules/@as-pect/core": {"version": "8.1.0", "dev": true, "license": "MIT", "dependencies": {"@as-pect/assembly": "^8.1.0", "@as-pect/snapshots": "^8.1.0", "@assemblyscript/loader": "^0.25.0", "assemblyscript": "^0.27.2", "chalk": "^5.2.0", "chalk-template": "^0.4.0", "long": "^5.2.1"}}, "node_modules/@as-pect/core/node_modules/@assemblyscript/loader": {"version": "0.25.2", "dev": true, "license": "Apache-2.0"}, "node_modules/@as-pect/csv-reporter": {"version": "8.1.0", "dev": true, "license": "MIT", "optional": true, "dependencies": {"@as-pect/core": "^8.1.0"}}, "node_modules/@as-pect/json-reporter": {"version": "8.1.0", "dev": true, "license": "MIT", "optional": true, "dependencies": {"@as-pect/core": "^8.1.0"}}, "node_modules/@as-pect/snapshots": {"version": "8.1.0", "dev": true, "license": "MIT", "dependencies": {"chevrotain": "^10.4.2", "diff": "^5.1.0", "nearley": "^2.20.1"}}, "node_modules/@as-pect/transform": {"version": "8.1.0", "dev": true, "license": "MIT", "dependencies": {"assemblyscript": "^0.27.2"}}, "node_modules/@assemblyscript/loader": {"version": "0.27.29", "dev": true, "license": "Apache-2.0"}, "node_modules/@babel/code-frame": {"version": "7.24.7", "dev": true, "license": "MIT", "dependencies": {"@babel/highlight": "^7.24.7", "picocolors": "^1.0.0"}, "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/helper-validator-identifier": {"version": "7.24.7", "dev": true, "license": "MIT", "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/highlight": {"version": "7.24.7", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-validator-identifier": "^7.24.7", "chalk": "^2.4.2", "js-tokens": "^4.0.0", "picocolors": "^1.0.0"}, "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/highlight/node_modules/chalk": {"version": "2.4.2", "dev": true, "license": "MIT", "dependencies": {"ansi-styles": "^3.2.1", "escape-string-regexp": "^1.0.5", "supports-color": "^5.3.0"}, "engines": {"node": ">=4"}}, "node_modules/@babel/highlight/node_modules/escape-string-regexp": {"version": "1.0.5", "dev": true, "license": "MIT", "engines": {"node": ">=0.8.0"}}, "node_modules/@chevrotain/cst-dts-gen": {"version": "10.5.0", "dev": true, "license": "Apache-2.0", "dependencies": {"@chevrotain/gast": "10.5.0", "@chevrotain/types": "10.5.0", "lodash": "4.17.21"}}, "node_modules/@chevrotain/gast": {"version": "10.5.0", "dev": true, "license": "Apache-2.0", "dependencies": {"@chevrotain/types": "10.5.0", "lodash": "4.17.21"}}, "node_modules/@chevrotain/types": {"version": "10.5.0", "dev": true, "license": "Apache-2.0"}, "node_modules/@chevrotain/utils": {"version": "10.5.0", "dev": true, "license": "Apache-2.0"}, "node_modules/@es-joy/jsdoccomment": {"version": "0.41.0", "dev": true, "license": "MIT", "dependencies": {"comment-parser": "1.4.1", "esquery": "^1.5.0", "jsdoc-type-pratt-parser": "~4.0.0"}, "engines": {"node": ">=16"}}, "node_modules/@eslint-community/eslint-utils": {"version": "4.4.0", "dev": true, "license": "MIT", "dependencies": {"eslint-visitor-keys": "^3.3.0"}, "engines": {"node": "^12.22.0 || ^14.17.0 || >=16.0.0"}, "peerDependencies": {"eslint": "^6.0.0 || ^7.0.0 || >=8.0.0"}}, "node_modules/@eslint-community/regexpp": {"version": "4.10.0", "dev": true, "license": "MIT", "engines": {"node": "^12.0.0 || ^14.0.0 || >=16.0.0"}}, "node_modules/@eslint/eslintrc": {"version": "2.1.4", "dev": true, "license": "MIT", "dependencies": {"ajv": "^6.12.4", "debug": "^4.3.2", "espree": "^9.6.0", "globals": "^13.19.0", "ignore": "^5.2.0", "import-fresh": "^3.2.1", "js-yaml": "^4.1.0", "minimatch": "^3.1.2", "strip-json-comments": "^3.1.1"}, "engines": {"node": "^12.22.0 || ^14.17.0 || >=16.0.0"}, "funding": {"url": "https://opencollective.com/eslint"}}, "node_modules/@eslint/js": {"version": "8.56.0", "dev": true, "license": "MIT", "engines": {"node": "^12.22.0 || ^14.17.0 || >=16.0.0"}}, "node_modules/@humanwhocodes/config-array": {"version": "0.11.14", "dev": true, "license": "Apache-2.0", "dependencies": {"@humanwhocodes/object-schema": "^2.0.2", "debug": "^4.3.1", "minimatch": "^3.0.5"}, "engines": {"node": ">=10.10.0"}}, "node_modules/@humanwhocodes/module-importer": {"version": "1.0.1", "dev": true, "license": "Apache-2.0", "engines": {"node": ">=12.22"}, "funding": {"type": "github", "url": "https://github.com/sponsors/nzakas"}}, "node_modules/@humanwhocodes/object-schema": {"version": "2.0.2", "dev": true, "license": "BSD-3-<PERSON><PERSON>"}, "node_modules/@isaacs/cliui": {"version": "8.0.2", "dev": true, "license": "ISC", "dependencies": {"string-width": "^5.1.2", "string-width-cjs": "npm:string-width@^4.2.0", "strip-ansi": "^7.0.1", "strip-ansi-cjs": "npm:strip-ansi@^6.0.1", "wrap-ansi": "^8.1.0", "wrap-ansi-cjs": "npm:wrap-ansi@^7.0.0"}, "engines": {"node": ">=12"}}, "node_modules/@isaacs/cliui/node_modules/ansi-regex": {"version": "6.0.1", "dev": true, "license": "MIT", "engines": {"node": ">=12"}, "funding": {"url": "https://github.com/chalk/ansi-regex?sponsor=1"}}, "node_modules/@isaacs/cliui/node_modules/ansi-styles": {"version": "6.2.1", "dev": true, "license": "MIT", "engines": {"node": ">=12"}, "funding": {"url": "https://github.com/chalk/ansi-styles?sponsor=1"}}, "node_modules/@isaacs/cliui/node_modules/emoji-regex": {"version": "9.2.2", "dev": true, "license": "MIT"}, "node_modules/@isaacs/cliui/node_modules/string-width": {"version": "5.1.2", "dev": true, "license": "MIT", "dependencies": {"eastasianwidth": "^0.2.0", "emoji-regex": "^9.2.2", "strip-ansi": "^7.0.1"}, "engines": {"node": ">=12"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/@isaacs/cliui/node_modules/strip-ansi": {"version": "7.1.0", "dev": true, "license": "MIT", "dependencies": {"ansi-regex": "^6.0.1"}, "engines": {"node": ">=12"}, "funding": {"url": "https://github.com/chalk/strip-ansi?sponsor=1"}}, "node_modules/@isaacs/cliui/node_modules/wrap-ansi": {"version": "8.1.0", "dev": true, "license": "MIT", "dependencies": {"ansi-styles": "^6.1.0", "string-width": "^5.0.1", "strip-ansi": "^7.0.1"}, "engines": {"node": ">=12"}, "funding": {"url": "https://github.com/chalk/wrap-ansi?sponsor=1"}}, "node_modules/@jest/expect-utils": {"version": "29.7.0", "dev": true, "license": "MIT", "dependencies": {"jest-get-type": "^29.6.3"}, "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}}, "node_modules/@jest/schemas": {"version": "29.6.3", "dev": true, "license": "MIT", "dependencies": {"@sinclair/typebox": "^0.27.8"}, "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}}, "node_modules/@jest/types": {"version": "29.6.3", "dev": true, "license": "MIT", "dependencies": {"@jest/schemas": "^29.6.3", "@types/istanbul-lib-coverage": "^2.0.0", "@types/istanbul-reports": "^3.0.0", "@types/node": "*", "@types/yargs": "^17.0.8", "chalk": "^4.0.0"}, "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}}, "node_modules/@jest/types/node_modules/ansi-styles": {"version": "4.3.0", "dev": true, "license": "MIT", "dependencies": {"color-convert": "^2.0.1"}, "engines": {"node": ">=8"}, "funding": {"url": "https://github.com/chalk/ansi-styles?sponsor=1"}}, "node_modules/@jest/types/node_modules/chalk": {"version": "4.1.2", "dev": true, "license": "MIT", "dependencies": {"ansi-styles": "^4.1.0", "supports-color": "^7.1.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/chalk/chalk?sponsor=1"}}, "node_modules/@jest/types/node_modules/color-convert": {"version": "2.0.1", "dev": true, "license": "MIT", "dependencies": {"color-name": "~1.1.4"}, "engines": {"node": ">=7.0.0"}}, "node_modules/@jest/types/node_modules/color-name": {"version": "1.1.4", "dev": true, "license": "MIT"}, "node_modules/@jest/types/node_modules/has-flag": {"version": "4.0.0", "dev": true, "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/@jest/types/node_modules/supports-color": {"version": "7.2.0", "dev": true, "license": "MIT", "dependencies": {"has-flag": "^4.0.0"}, "engines": {"node": ">=8"}}, "node_modules/@massalabs/as-transformer": {"version": "0.4.0", "dev": true, "license": "ISC", "dependencies": {"@massalabs/as-types": "^2.1.0", "@types/jest": "^29.5.1", "assemblyscript": "^0.27.18", "husky": "^8.0.3", "rimraf": "^5.0.5", "ts-morph": "^19.0.0", "visitor-as": "^0.11.4", "yaml": "^2.3.1"}}, "node_modules/@massalabs/as-types": {"version": "2.1.0", "dev": true, "license": "ISC", "dependencies": {"as-bignum": "^0.3.0", "assemblyscript": "^0.27.18"}}, "node_modules/@massalabs/eslint-config": {"version": "0.0.11", "dev": true, "license": "ISC", "dependencies": {"@typescript-eslint/eslint-plugin": "^5.47.1", "@typescript-eslint/parser": "^5.29.0", "eslint": ">= 8", "eslint-config-google": "^0.14.0", "eslint-config-prettier": "^8.5.0", "eslint-plugin-jsdoc": "^48.0.4", "eslint-plugin-json": "^3.1.0", "eslint-plugin-tsdoc": "^0.2.17"}}, "node_modules/@massalabs/massa-as-sdk": {"version": "3.0.0", "resolved": "https://registry.npmjs.org/@massalabs/massa-as-sdk/-/massa-as-sdk-3.0.0.tgz", "integrity": "sha512-VaTQG0BnCyQLmfhJsouvGdVboqoV1ygWsfGrtb6VbQiLX6jNMwV2RvMmP177CS93JoHP+Bile0sq4ZGSiN/7Gw==", "dev": true, "license": "(MIT AND Apache-2.0)", "dependencies": {"as-base64": "^0.2.0", "ethers": "^6.8.1", "js-sha3": "^0.9.2"}}, "node_modules/@massalabs/massa-sc-compiler": {"version": "0.2.1-dev.20240725151426", "dev": true, "license": "ISC", "dependencies": {"@massalabs/as-transformer": "^0.4.0", "assemblyscript": "^0.27.29", "yargs": "^17.7.1"}, "bin": {"massa-as-compile": "dist/compiler.js"}}, "node_modules/@massalabs/massa-web3": {"version": "5.2.0", "resolved": "https://registry.npmjs.org/@massalabs/massa-web3/-/massa-web3-5.2.0.tgz", "integrity": "sha512-t60PlZXMPWn7h44Koe9XVfHSaQKdbzvm+K21VXzqiUKP5VMvdvf1AEkq8HpTLcq81cy5RV+UfdSoISw3Hn7pHQ==", "dev": true, "license": "MIT", "dependencies": {"@noble/ed25519": "^1.7.3", "@noble/hashes": "^1.2.0", "bs58check": "^4.0.0", "decimal.js": "^10.4.3", "dotenv": "^16.0.3", "eventemitter3": "^5.0.1", "lodash.isequal": "^4.5.0", "secure-random": "^1.1.2", "tslib": "^2.8.0", "varint": "^6.0.0"}, "optionalDependencies": {"bufferutil": "^4.0.7", "utf-8-validate": "^6.0.2"}}, "node_modules/@massalabs/prettier-config-as": {"version": "0.0.2", "dev": true, "license": "MIT", "dependencies": {"assemblyscript-prettier": "^1.0.6"}}, "node_modules/@massalabs/sc-standards": {"version": "1.3.0", "resolved": "https://registry.npmjs.org/@massalabs/sc-standards/-/sc-standards-1.3.0.tgz", "integrity": "sha512-6vyvXsQMt/XakmjagH2cFG5ayOmuxtQsGIKApbAC7gA6AzsgavuCvWvnuxo0DP0cH0zH4EgFwhb7L1iDTMiSyQ==", "dev": true, "license": "ISC", "dependencies": {"@massalabs/as-types": "^2.1.0", "@massalabs/massa-as-sdk": "^3.0.1-dev", "as-bignum": "^0.3.1"}}, "node_modules/@massalabs/sc-standards/node_modules/@massalabs/massa-as-sdk": {"version": "3.0.1-dev.20250422103107", "resolved": "https://registry.npmjs.org/@massalabs/massa-as-sdk/-/massa-as-sdk-3.0.1-dev.20250422103107.tgz", "integrity": "sha512-EAnG5IIHcYTJc6MPATWHvWEYbSu78A0mYywq01UcSmr9YRiVKnryHQNa3pJ6SCyKkmfTBlWvnLhnxjd/wy0e3A==", "dev": true, "license": "(MIT AND Apache-2.0)", "dependencies": {"as-base64": "^0.2.0", "bs58check": "^4.0.0", "ethers": "^6.8.1", "js-sha3": "^0.9.2"}}, "node_modules/@microsoft/tsdoc": {"version": "0.14.2", "dev": true, "license": "MIT"}, "node_modules/@microsoft/tsdoc-config": {"version": "0.16.2", "dev": true, "license": "MIT", "dependencies": {"@microsoft/tsdoc": "0.14.2", "ajv": "~6.12.6", "jju": "~1.4.0", "resolve": "~1.19.0"}}, "node_modules/@noble/curves": {"version": "1.2.0", "dev": true, "license": "MIT", "dependencies": {"@noble/hashes": "1.3.2"}, "funding": {"url": "https://paulmillr.com/funding/"}}, "node_modules/@noble/curves/node_modules/@noble/hashes": {"version": "1.3.2", "dev": true, "license": "MIT", "engines": {"node": ">= 16"}, "funding": {"url": "https://paulmillr.com/funding/"}}, "node_modules/@noble/ed25519": {"version": "1.7.3", "dev": true, "funding": [{"type": "individual", "url": "https://paulmillr.com/funding/"}], "license": "MIT"}, "node_modules/@noble/hashes": {"version": "1.3.3", "dev": true, "license": "MIT", "engines": {"node": ">= 16"}, "funding": {"url": "https://paulmillr.com/funding/"}}, "node_modules/@nodelib/fs.scandir": {"version": "2.1.5", "dev": true, "license": "MIT", "dependencies": {"@nodelib/fs.stat": "2.0.5", "run-parallel": "^1.1.9"}, "engines": {"node": ">= 8"}}, "node_modules/@nodelib/fs.stat": {"version": "2.0.5", "dev": true, "license": "MIT", "engines": {"node": ">= 8"}}, "node_modules/@nodelib/fs.walk": {"version": "1.2.8", "dev": true, "license": "MIT", "dependencies": {"@nodelib/fs.scandir": "2.1.5", "fastq": "^1.6.0"}, "engines": {"node": ">= 8"}}, "node_modules/@pkgjs/parseargs": {"version": "0.11.0", "dev": true, "license": "MIT", "optional": true, "engines": {"node": ">=14"}}, "node_modules/@sinclair/typebox": {"version": "0.27.8", "dev": true, "license": "MIT"}, "node_modules/@ts-morph/common": {"version": "0.20.0", "dev": true, "license": "MIT", "dependencies": {"fast-glob": "^3.2.12", "minimatch": "^7.4.3", "mkdirp": "^2.1.6", "path-browserify": "^1.0.1"}}, "node_modules/@ts-morph/common/node_modules/brace-expansion": {"version": "2.0.1", "dev": true, "license": "MIT", "dependencies": {"balanced-match": "^1.0.0"}}, "node_modules/@ts-morph/common/node_modules/minimatch": {"version": "7.4.6", "dev": true, "license": "ISC", "dependencies": {"brace-expansion": "^2.0.1"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/isaacs"}}, "node_modules/@types/glob": {"version": "8.1.0", "dev": true, "license": "MIT", "dependencies": {"@types/minimatch": "^5.1.2", "@types/node": "*"}}, "node_modules/@types/istanbul-lib-coverage": {"version": "2.0.6", "dev": true, "license": "MIT"}, "node_modules/@types/istanbul-lib-report": {"version": "3.0.3", "dev": true, "license": "MIT", "dependencies": {"@types/istanbul-lib-coverage": "*"}}, "node_modules/@types/istanbul-reports": {"version": "3.0.4", "dev": true, "license": "MIT", "dependencies": {"@types/istanbul-lib-report": "*"}}, "node_modules/@types/jest": {"version": "29.5.12", "dev": true, "license": "MIT", "dependencies": {"expect": "^29.0.0", "pretty-format": "^29.0.0"}}, "node_modules/@types/json-schema": {"version": "7.0.15", "dev": true, "license": "MIT"}, "node_modules/@types/minimatch": {"version": "5.1.2", "dev": true, "license": "MIT"}, "node_modules/@types/node": {"version": "18.19.10", "dev": true, "license": "MIT", "dependencies": {"undici-types": "~5.26.4"}}, "node_modules/@types/semver": {"version": "7.5.6", "dev": true, "license": "MIT"}, "node_modules/@types/stack-utils": {"version": "2.0.3", "dev": true, "license": "MIT"}, "node_modules/@types/yargs": {"version": "17.0.32", "dev": true, "license": "MIT", "dependencies": {"@types/yargs-parser": "*"}}, "node_modules/@types/yargs-parser": {"version": "21.0.3", "dev": true, "license": "MIT"}, "node_modules/@typescript-eslint/eslint-plugin": {"version": "5.62.0", "dev": true, "license": "MIT", "dependencies": {"@eslint-community/regexpp": "^4.4.0", "@typescript-eslint/scope-manager": "5.62.0", "@typescript-eslint/type-utils": "5.62.0", "@typescript-eslint/utils": "5.62.0", "debug": "^4.3.4", "graphemer": "^1.4.0", "ignore": "^5.2.0", "natural-compare-lite": "^1.4.0", "semver": "^7.3.7", "tsutils": "^3.21.0"}, "engines": {"node": "^12.22.0 || ^14.17.0 || >=16.0.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/typescript-eslint"}, "peerDependencies": {"@typescript-eslint/parser": "^5.0.0", "eslint": "^6.0.0 || ^7.0.0 || ^8.0.0"}, "peerDependenciesMeta": {"typescript": {"optional": true}}}, "node_modules/@typescript-eslint/parser": {"version": "5.62.0", "dev": true, "license": "BSD-2-<PERSON><PERSON>", "dependencies": {"@typescript-eslint/scope-manager": "5.62.0", "@typescript-eslint/types": "5.62.0", "@typescript-eslint/typescript-estree": "5.62.0", "debug": "^4.3.4"}, "engines": {"node": "^12.22.0 || ^14.17.0 || >=16.0.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/typescript-eslint"}, "peerDependencies": {"eslint": "^6.0.0 || ^7.0.0 || ^8.0.0"}, "peerDependenciesMeta": {"typescript": {"optional": true}}}, "node_modules/@typescript-eslint/scope-manager": {"version": "5.62.0", "dev": true, "license": "MIT", "dependencies": {"@typescript-eslint/types": "5.62.0", "@typescript-eslint/visitor-keys": "5.62.0"}, "engines": {"node": "^12.22.0 || ^14.17.0 || >=16.0.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/typescript-eslint"}}, "node_modules/@typescript-eslint/type-utils": {"version": "5.62.0", "dev": true, "license": "MIT", "dependencies": {"@typescript-eslint/typescript-estree": "5.62.0", "@typescript-eslint/utils": "5.62.0", "debug": "^4.3.4", "tsutils": "^3.21.0"}, "engines": {"node": "^12.22.0 || ^14.17.0 || >=16.0.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/typescript-eslint"}, "peerDependencies": {"eslint": "*"}, "peerDependenciesMeta": {"typescript": {"optional": true}}}, "node_modules/@typescript-eslint/types": {"version": "5.62.0", "dev": true, "license": "MIT", "engines": {"node": "^12.22.0 || ^14.17.0 || >=16.0.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/typescript-eslint"}}, "node_modules/@typescript-eslint/typescript-estree": {"version": "5.62.0", "dev": true, "license": "BSD-2-<PERSON><PERSON>", "dependencies": {"@typescript-eslint/types": "5.62.0", "@typescript-eslint/visitor-keys": "5.62.0", "debug": "^4.3.4", "globby": "^11.1.0", "is-glob": "^4.0.3", "semver": "^7.3.7", "tsutils": "^3.21.0"}, "engines": {"node": "^12.22.0 || ^14.17.0 || >=16.0.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/typescript-eslint"}, "peerDependenciesMeta": {"typescript": {"optional": true}}}, "node_modules/@typescript-eslint/utils": {"version": "5.62.0", "dev": true, "license": "MIT", "dependencies": {"@eslint-community/eslint-utils": "^4.2.0", "@types/json-schema": "^7.0.9", "@types/semver": "^7.3.12", "@typescript-eslint/scope-manager": "5.62.0", "@typescript-eslint/types": "5.62.0", "@typescript-eslint/typescript-estree": "5.62.0", "eslint-scope": "^5.1.1", "semver": "^7.3.7"}, "engines": {"node": "^12.22.0 || ^14.17.0 || >=16.0.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/typescript-eslint"}, "peerDependencies": {"eslint": "^6.0.0 || ^7.0.0 || ^8.0.0"}}, "node_modules/@typescript-eslint/visitor-keys": {"version": "5.62.0", "dev": true, "license": "MIT", "dependencies": {"@typescript-eslint/types": "5.62.0", "eslint-visitor-keys": "^3.3.0"}, "engines": {"node": "^12.22.0 || ^14.17.0 || >=16.0.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/typescript-eslint"}}, "node_modules/@ungap/structured-clone": {"version": "1.2.0", "dev": true, "license": "ISC"}, "node_modules/acorn": {"version": "8.11.3", "dev": true, "license": "MIT", "bin": {"acorn": "bin/acorn"}, "engines": {"node": ">=0.4.0"}}, "node_modules/acorn-jsx": {"version": "5.3.2", "dev": true, "license": "MIT", "peerDependencies": {"acorn": "^6.0.0 || ^7.0.0 || ^8.0.0"}}, "node_modules/aes-js": {"version": "4.0.0-beta.5", "dev": true, "license": "MIT"}, "node_modules/ajv": {"version": "6.12.6", "dev": true, "license": "MIT", "dependencies": {"fast-deep-equal": "^3.1.1", "fast-json-stable-stringify": "^2.0.0", "json-schema-traverse": "^0.4.1", "uri-js": "^4.2.2"}, "funding": {"type": "github", "url": "https://github.com/sponsors/epoberezkin"}}, "node_modules/ansi-regex": {"version": "5.0.1", "dev": true, "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/ansi-styles": {"version": "3.2.1", "dev": true, "license": "MIT", "dependencies": {"color-convert": "^1.9.0"}, "engines": {"node": ">=4"}}, "node_modules/are-docs-informative": {"version": "0.0.2", "dev": true, "license": "MIT", "engines": {"node": ">=14"}}, "node_modules/argparse": {"version": "2.0.1", "dev": true, "license": "Python-2.0"}, "node_modules/array-union": {"version": "2.1.0", "dev": true, "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/as-base64": {"version": "0.2.0", "resolved": "https://registry.npmjs.org/as-base64/-/as-base64-0.2.0.tgz", "integrity": "sha512-j6JxprAVN4SXUcZiSChuMBf0JJOCdh5OfrHAepluxmFWuDZZm+YjmfNSk8djUHRPEB+Ui5HGvrz46GLvTJf3ig==", "dev": true, "license": "MIT"}, "node_modules/as-bignum": {"version": "0.2.40", "resolved": "git+ssh://**************/massalabs/as-bignum.git#0105eb596b2fa707c00712e811a2efdfcb8a9848", "integrity": "sha512-l/5Lb2Jsy4SkZqbpuhlibNb5HkLIDcPdQK07qU0drShKIM4yOYNnO88na4LZjt4es+FuUUk0eFKQ51Q/iQOhmQ==", "dev": true, "license": "Apache-2.0"}, "node_modules/assemblyscript": {"version": "0.27.29", "dev": true, "license": "Apache-2.0", "dependencies": {"binaryen": "116.0.0-nightly.20240114", "long": "^5.2.1"}, "bin": {"asc": "bin/asc.js", "asinit": "bin/asinit.js"}, "engines": {"node": ">=16", "npm": ">=7"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/assemblyscript"}}, "node_modules/assemblyscript-prettier": {"version": "1.0.7", "dev": true, "license": "MIT", "dependencies": {"chalk": "^5.0.1", "cli-progress": "^3.11.2", "commander": "^9.4.0", "fast-glob": "^3.2.11", "ignore": "^5.2.0"}, "bin": {"as-prettier": "index.mjs"}, "peerDependencies": {"assemblyscript": ">=0.20.0", "prettier": "^2.7.1"}}, "node_modules/astral-regex": {"version": "2.0.0", "dev": true, "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/balanced-match": {"version": "1.0.2", "dev": true, "license": "MIT"}, "node_modules/base-x": {"version": "5.0.1", "resolved": "https://registry.npmjs.org/base-x/-/base-x-5.0.1.tgz", "integrity": "sha512-M7uio8Zt++eg3jPj+rHMfCC+IuygQHHCOU+IYsVtik6FWjuYpVt/+MRKcgsAMHh8mMFAwnB+Bs+mTrFiXjMzKg==", "dev": true, "license": "MIT"}, "node_modules/binaryen": {"version": "116.0.0-nightly.20240114", "dev": true, "license": "Apache-2.0", "bin": {"wasm-opt": "bin/wasm-opt", "wasm2js": "bin/wasm2js"}}, "node_modules/brace-expansion": {"version": "1.1.11", "dev": true, "license": "MIT", "dependencies": {"balanced-match": "^1.0.0", "concat-map": "0.0.1"}}, "node_modules/braces": {"version": "3.0.2", "dev": true, "license": "MIT", "dependencies": {"fill-range": "^7.0.1"}, "engines": {"node": ">=8"}}, "node_modules/bs58": {"version": "6.0.0", "resolved": "https://registry.npmjs.org/bs58/-/bs58-6.0.0.tgz", "integrity": "sha512-PD0wEnEYg6ijszw/u8s+iI3H17cTymlrwkKhDhPZq+Sokl3AU4htyBFTjAeNAlCCmg0f53g6ih3jATyCKftTfw==", "dev": true, "license": "MIT", "dependencies": {"base-x": "^5.0.0"}}, "node_modules/bs58check": {"version": "4.0.0", "resolved": "https://registry.npmjs.org/bs58check/-/bs58check-4.0.0.tgz", "integrity": "sha512-FsGDOnFg9aVI9erdriULkd/JjEWONV/lQE5aYziB5PoBsXRind56lh8doIZIc9X4HoxT5x4bLjMWN1/NB8Zp5g==", "dev": true, "license": "MIT", "dependencies": {"@noble/hashes": "^1.2.0", "bs58": "^6.0.0"}}, "node_modules/bufferutil": {"version": "4.0.8", "dev": true, "hasInstallScript": true, "license": "MIT", "optional": true, "dependencies": {"node-gyp-build": "^4.3.0"}, "engines": {"node": ">=6.14.2"}}, "node_modules/builtin-modules": {"version": "3.3.0", "dev": true, "license": "MIT", "engines": {"node": ">=6"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/callsites": {"version": "3.1.0", "dev": true, "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/chalk": {"version": "5.3.0", "dev": true, "license": "MIT", "engines": {"node": "^12.17.0 || ^14.13 || >=16.0.0"}, "funding": {"url": "https://github.com/chalk/chalk?sponsor=1"}}, "node_modules/chalk-template": {"version": "0.4.0", "dev": true, "license": "MIT", "dependencies": {"chalk": "^4.1.2"}, "engines": {"node": ">=12"}, "funding": {"url": "https://github.com/chalk/chalk-template?sponsor=1"}}, "node_modules/chalk-template/node_modules/ansi-styles": {"version": "4.3.0", "dev": true, "license": "MIT", "dependencies": {"color-convert": "^2.0.1"}, "engines": {"node": ">=8"}, "funding": {"url": "https://github.com/chalk/ansi-styles?sponsor=1"}}, "node_modules/chalk-template/node_modules/chalk": {"version": "4.1.2", "dev": true, "license": "MIT", "dependencies": {"ansi-styles": "^4.1.0", "supports-color": "^7.1.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/chalk/chalk?sponsor=1"}}, "node_modules/chalk-template/node_modules/color-convert": {"version": "2.0.1", "dev": true, "license": "MIT", "dependencies": {"color-name": "~1.1.4"}, "engines": {"node": ">=7.0.0"}}, "node_modules/chalk-template/node_modules/color-name": {"version": "1.1.4", "dev": true, "license": "MIT"}, "node_modules/chalk-template/node_modules/has-flag": {"version": "4.0.0", "dev": true, "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/chalk-template/node_modules/supports-color": {"version": "7.2.0", "dev": true, "license": "MIT", "dependencies": {"has-flag": "^4.0.0"}, "engines": {"node": ">=8"}}, "node_modules/chevrotain": {"version": "10.5.0", "dev": true, "license": "Apache-2.0", "dependencies": {"@chevrotain/cst-dts-gen": "10.5.0", "@chevrotain/gast": "10.5.0", "@chevrotain/types": "10.5.0", "@chevrotain/utils": "10.5.0", "lodash": "4.17.21", "regexp-to-ast": "0.5.0"}}, "node_modules/ci-info": {"version": "3.9.0", "dev": true, "funding": [{"type": "github", "url": "https://github.com/sponsors/sibir<PERSON>-s"}], "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/cli-progress": {"version": "3.12.0", "dev": true, "license": "MIT", "dependencies": {"string-width": "^4.2.3"}, "engines": {"node": ">=4"}}, "node_modules/cliui": {"version": "8.0.1", "dev": true, "license": "ISC", "dependencies": {"string-width": "^4.2.0", "strip-ansi": "^6.0.1", "wrap-ansi": "^7.0.0"}, "engines": {"node": ">=12"}}, "node_modules/code-block-writer": {"version": "12.0.0", "dev": true, "license": "MIT"}, "node_modules/color-convert": {"version": "1.9.3", "dev": true, "license": "MIT", "dependencies": {"color-name": "1.1.3"}}, "node_modules/color-name": {"version": "1.1.3", "dev": true, "license": "MIT"}, "node_modules/commander": {"version": "9.5.0", "dev": true, "license": "MIT", "engines": {"node": "^12.20.0 || >=14"}}, "node_modules/comment-parser": {"version": "1.4.1", "dev": true, "license": "MIT", "engines": {"node": ">= 12.0.0"}}, "node_modules/concat-map": {"version": "0.0.1", "dev": true, "license": "MIT"}, "node_modules/cross-spawn": {"version": "7.0.3", "dev": true, "license": "MIT", "dependencies": {"path-key": "^3.1.0", "shebang-command": "^2.0.0", "which": "^2.0.1"}, "engines": {"node": ">= 8"}}, "node_modules/csv-stringify": {"version": "6.4.5", "dev": true, "license": "MIT"}, "node_modules/debug": {"version": "4.3.4", "dev": true, "license": "MIT", "dependencies": {"ms": "2.1.2"}, "engines": {"node": ">=6.0"}, "peerDependenciesMeta": {"supports-color": {"optional": true}}}, "node_modules/decimal.js": {"version": "10.4.3", "dev": true, "license": "MIT"}, "node_modules/deep-is": {"version": "0.1.4", "dev": true, "license": "MIT"}, "node_modules/diff": {"version": "5.1.0", "dev": true, "license": "BSD-3-<PERSON><PERSON>", "engines": {"node": ">=0.3.1"}}, "node_modules/diff-sequences": {"version": "29.6.3", "dev": true, "license": "MIT", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}}, "node_modules/dir-glob": {"version": "3.0.1", "dev": true, "license": "MIT", "dependencies": {"path-type": "^4.0.0"}, "engines": {"node": ">=8"}}, "node_modules/discontinuous-range": {"version": "1.0.0", "dev": true, "license": "MIT"}, "node_modules/doctrine": {"version": "3.0.0", "dev": true, "license": "Apache-2.0", "dependencies": {"esutils": "^2.0.2"}, "engines": {"node": ">=6.0.0"}}, "node_modules/dotenv": {"version": "16.4.1", "dev": true, "license": "BSD-2-<PERSON><PERSON>", "engines": {"node": ">=12"}, "funding": {"url": "https://github.com/motdotla/dotenv?sponsor=1"}}, "node_modules/eastasianwidth": {"version": "0.2.0", "dev": true, "license": "MIT"}, "node_modules/emoji-regex": {"version": "8.0.0", "dev": true, "license": "MIT"}, "node_modules/esbuild": {"version": "0.19.12", "dev": true, "hasInstallScript": true, "license": "MIT", "bin": {"esbuild": "bin/esbuild"}, "engines": {"node": ">=12"}, "optionalDependencies": {"@esbuild/aix-ppc64": "0.19.12", "@esbuild/android-arm": "0.19.12", "@esbuild/android-arm64": "0.19.12", "@esbuild/android-x64": "0.19.12", "@esbuild/darwin-arm64": "0.19.12", "@esbuild/darwin-x64": "0.19.12", "@esbuild/freebsd-arm64": "0.19.12", "@esbuild/freebsd-x64": "0.19.12", "@esbuild/linux-arm": "0.19.12", "@esbuild/linux-arm64": "0.19.12", "@esbuild/linux-ia32": "0.19.12", "@esbuild/linux-loong64": "0.19.12", "@esbuild/linux-mips64el": "0.19.12", "@esbuild/linux-ppc64": "0.19.12", "@esbuild/linux-riscv64": "0.19.12", "@esbuild/linux-s390x": "0.19.12", "@esbuild/linux-x64": "0.19.12", "@esbuild/netbsd-x64": "0.19.12", "@esbuild/openbsd-x64": "0.19.12", "@esbuild/sunos-x64": "0.19.12", "@esbuild/win32-arm64": "0.19.12", "@esbuild/win32-ia32": "0.19.12", "@esbuild/win32-x64": "0.19.12"}}, "node_modules/escalade": {"version": "3.1.1", "dev": true, "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/escape-string-regexp": {"version": "4.0.0", "dev": true, "license": "MIT", "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/eslint": {"version": "8.56.0", "dev": true, "license": "MIT", "dependencies": {"@eslint-community/eslint-utils": "^4.2.0", "@eslint-community/regexpp": "^4.6.1", "@eslint/eslintrc": "^2.1.4", "@eslint/js": "8.56.0", "@humanwhocodes/config-array": "^0.11.13", "@humanwhocodes/module-importer": "^1.0.1", "@nodelib/fs.walk": "^1.2.8", "@ungap/structured-clone": "^1.2.0", "ajv": "^6.12.4", "chalk": "^4.0.0", "cross-spawn": "^7.0.2", "debug": "^4.3.2", "doctrine": "^3.0.0", "escape-string-regexp": "^4.0.0", "eslint-scope": "^7.2.2", "eslint-visitor-keys": "^3.4.3", "espree": "^9.6.1", "esquery": "^1.4.2", "esutils": "^2.0.2", "fast-deep-equal": "^3.1.3", "file-entry-cache": "^6.0.1", "find-up": "^5.0.0", "glob-parent": "^6.0.2", "globals": "^13.19.0", "graphemer": "^1.4.0", "ignore": "^5.2.0", "imurmurhash": "^0.1.4", "is-glob": "^4.0.0", "is-path-inside": "^3.0.3", "js-yaml": "^4.1.0", "json-stable-stringify-without-jsonify": "^1.0.1", "levn": "^0.4.1", "lodash.merge": "^4.6.2", "minimatch": "^3.1.2", "natural-compare": "^1.4.0", "optionator": "^0.9.3", "strip-ansi": "^6.0.1", "text-table": "^0.2.0"}, "bin": {"eslint": "bin/eslint.js"}, "engines": {"node": "^12.22.0 || ^14.17.0 || >=16.0.0"}, "funding": {"url": "https://opencollective.com/eslint"}}, "node_modules/eslint-config-google": {"version": "0.14.0", "dev": true, "license": "Apache-2.0", "engines": {"node": ">=0.10.0"}, "peerDependencies": {"eslint": ">=5.16.0"}}, "node_modules/eslint-config-prettier": {"version": "8.10.0", "dev": true, "license": "MIT", "bin": {"eslint-config-prettier": "bin/cli.js"}, "peerDependencies": {"eslint": ">=7.0.0"}}, "node_modules/eslint-plugin-jsdoc": {"version": "48.0.5", "dev": true, "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"@es-joy/jsdoccomment": "~0.41.0", "are-docs-informative": "^0.0.2", "comment-parser": "1.4.1", "debug": "^4.3.4", "escape-string-regexp": "^4.0.0", "esquery": "^1.5.0", "is-builtin-module": "^3.2.1", "semver": "^7.5.4", "spdx-expression-parse": "^4.0.0"}, "engines": {"node": ">=18"}, "peerDependencies": {"eslint": "^7.0.0 || ^8.0.0 || ^9.0.0"}}, "node_modules/eslint-plugin-jsdoc/node_modules/spdx-expression-parse": {"version": "4.0.0", "dev": true, "license": "MIT", "dependencies": {"spdx-exceptions": "^2.1.0", "spdx-license-ids": "^3.0.0"}}, "node_modules/eslint-plugin-json": {"version": "3.1.0", "dev": true, "license": "MIT", "dependencies": {"lodash": "^4.17.21", "vscode-json-languageservice": "^4.1.6"}, "engines": {"node": ">=12.0"}}, "node_modules/eslint-plugin-tsdoc": {"version": "0.2.17", "dev": true, "license": "MIT", "dependencies": {"@microsoft/tsdoc": "0.14.2", "@microsoft/tsdoc-config": "0.16.2"}}, "node_modules/eslint-scope": {"version": "5.1.1", "dev": true, "license": "BSD-2-<PERSON><PERSON>", "dependencies": {"esrecurse": "^4.3.0", "estraverse": "^4.1.1"}, "engines": {"node": ">=8.0.0"}}, "node_modules/eslint-visitor-keys": {"version": "3.4.3", "dev": true, "license": "Apache-2.0", "engines": {"node": "^12.22.0 || ^14.17.0 || >=16.0.0"}, "funding": {"url": "https://opencollective.com/eslint"}}, "node_modules/eslint/node_modules/ansi-styles": {"version": "4.3.0", "dev": true, "license": "MIT", "dependencies": {"color-convert": "^2.0.1"}, "engines": {"node": ">=8"}, "funding": {"url": "https://github.com/chalk/ansi-styles?sponsor=1"}}, "node_modules/eslint/node_modules/chalk": {"version": "4.1.2", "dev": true, "license": "MIT", "dependencies": {"ansi-styles": "^4.1.0", "supports-color": "^7.1.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/chalk/chalk?sponsor=1"}}, "node_modules/eslint/node_modules/color-convert": {"version": "2.0.1", "dev": true, "license": "MIT", "dependencies": {"color-name": "~1.1.4"}, "engines": {"node": ">=7.0.0"}}, "node_modules/eslint/node_modules/color-name": {"version": "1.1.4", "dev": true, "license": "MIT"}, "node_modules/eslint/node_modules/eslint-scope": {"version": "7.2.2", "dev": true, "license": "BSD-2-<PERSON><PERSON>", "dependencies": {"esrecurse": "^4.3.0", "estraverse": "^5.2.0"}, "engines": {"node": "^12.22.0 || ^14.17.0 || >=16.0.0"}, "funding": {"url": "https://opencollective.com/eslint"}}, "node_modules/eslint/node_modules/estraverse": {"version": "5.3.0", "dev": true, "license": "BSD-2-<PERSON><PERSON>", "engines": {"node": ">=4.0"}}, "node_modules/eslint/node_modules/has-flag": {"version": "4.0.0", "dev": true, "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/eslint/node_modules/supports-color": {"version": "7.2.0", "dev": true, "license": "MIT", "dependencies": {"has-flag": "^4.0.0"}, "engines": {"node": ">=8"}}, "node_modules/espree": {"version": "9.6.1", "dev": true, "license": "BSD-2-<PERSON><PERSON>", "dependencies": {"acorn": "^8.9.0", "acorn-jsx": "^5.3.2", "eslint-visitor-keys": "^3.4.1"}, "engines": {"node": "^12.22.0 || ^14.17.0 || >=16.0.0"}, "funding": {"url": "https://opencollective.com/eslint"}}, "node_modules/esquery": {"version": "1.5.0", "dev": true, "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"estraverse": "^5.1.0"}, "engines": {"node": ">=0.10"}}, "node_modules/esquery/node_modules/estraverse": {"version": "5.3.0", "dev": true, "license": "BSD-2-<PERSON><PERSON>", "engines": {"node": ">=4.0"}}, "node_modules/esrecurse": {"version": "4.3.0", "dev": true, "license": "BSD-2-<PERSON><PERSON>", "dependencies": {"estraverse": "^5.2.0"}, "engines": {"node": ">=4.0"}}, "node_modules/esrecurse/node_modules/estraverse": {"version": "5.3.0", "dev": true, "license": "BSD-2-<PERSON><PERSON>", "engines": {"node": ">=4.0"}}, "node_modules/estraverse": {"version": "4.3.0", "dev": true, "license": "BSD-2-<PERSON><PERSON>", "engines": {"node": ">=4.0"}}, "node_modules/esutils": {"version": "2.0.3", "dev": true, "license": "BSD-2-<PERSON><PERSON>", "engines": {"node": ">=0.10.0"}}, "node_modules/ethers": {"version": "6.10.0", "dev": true, "funding": [{"type": "individual", "url": "https://github.com/sponsors/ethers-io/"}, {"type": "individual", "url": "https://www.buymeacoffee.com/ricmoo"}], "license": "MIT", "dependencies": {"@adraffy/ens-normalize": "1.10.0", "@noble/curves": "1.2.0", "@noble/hashes": "1.3.2", "@types/node": "18.15.13", "aes-js": "4.0.0-beta.5", "tslib": "2.4.0", "ws": "8.5.0"}, "engines": {"node": ">=14.0.0"}}, "node_modules/ethers/node_modules/@noble/hashes": {"version": "1.3.2", "dev": true, "license": "MIT", "engines": {"node": ">= 16"}, "funding": {"url": "https://paulmillr.com/funding/"}}, "node_modules/ethers/node_modules/@types/node": {"version": "18.15.13", "dev": true, "license": "MIT"}, "node_modules/ethers/node_modules/tslib": {"version": "2.4.0", "dev": true, "license": "0BSD"}, "node_modules/ethers/node_modules/utf-8-validate": {"version": "5.0.10", "dev": true, "hasInstallScript": true, "license": "MIT", "optional": true, "peer": true, "dependencies": {"node-gyp-build": "^4.3.0"}, "engines": {"node": ">=6.14.2"}}, "node_modules/ethers/node_modules/ws": {"version": "8.5.0", "dev": true, "license": "MIT", "engines": {"node": ">=10.0.0"}, "peerDependencies": {"bufferutil": "^4.0.1", "utf-8-validate": "^5.0.2"}, "peerDependenciesMeta": {"bufferutil": {"optional": true}, "utf-8-validate": {"optional": true}}}, "node_modules/eventemitter3": {"version": "5.0.1", "resolved": "https://registry.npmjs.org/eventemitter3/-/eventemitter3-5.0.1.tgz", "integrity": "sha512-GWkBvjiSZK87ELrYOSESUYeVIc9mvLLf/nXalMOS5dYrgZq9o5OVkbZAVM06CVxYsCwH9BDZFPlQTlPA1j4ahA==", "dev": true}, "node_modules/expect": {"version": "29.7.0", "dev": true, "license": "MIT", "dependencies": {"@jest/expect-utils": "^29.7.0", "jest-get-type": "^29.6.3", "jest-matcher-utils": "^29.7.0", "jest-message-util": "^29.7.0", "jest-util": "^29.7.0"}, "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}}, "node_modules/fast-deep-equal": {"version": "3.1.3", "dev": true, "license": "MIT"}, "node_modules/fast-glob": {"version": "3.3.2", "dev": true, "license": "MIT", "dependencies": {"@nodelib/fs.stat": "^2.0.2", "@nodelib/fs.walk": "^1.2.3", "glob-parent": "^5.1.2", "merge2": "^1.3.0", "micromatch": "^4.0.4"}, "engines": {"node": ">=8.6.0"}}, "node_modules/fast-glob/node_modules/glob-parent": {"version": "5.1.2", "dev": true, "license": "ISC", "dependencies": {"is-glob": "^4.0.1"}, "engines": {"node": ">= 6"}}, "node_modules/fast-json-stable-stringify": {"version": "2.1.0", "dev": true, "license": "MIT"}, "node_modules/fast-levenshtein": {"version": "2.0.6", "dev": true, "license": "MIT"}, "node_modules/fastq": {"version": "1.17.0", "dev": true, "license": "ISC", "dependencies": {"reusify": "^1.0.4"}}, "node_modules/file-entry-cache": {"version": "6.0.1", "dev": true, "license": "MIT", "dependencies": {"flat-cache": "^3.0.4"}, "engines": {"node": "^10.12.0 || >=12.0.0"}}, "node_modules/fill-range": {"version": "7.0.1", "dev": true, "license": "MIT", "dependencies": {"to-regex-range": "^5.0.1"}, "engines": {"node": ">=8"}}, "node_modules/find-up": {"version": "5.0.0", "dev": true, "license": "MIT", "dependencies": {"locate-path": "^6.0.0", "path-exists": "^4.0.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/flat-cache": {"version": "3.2.0", "dev": true, "license": "MIT", "dependencies": {"flatted": "^3.2.9", "keyv": "^4.5.3", "rimraf": "^3.0.2"}, "engines": {"node": "^10.12.0 || >=12.0.0"}}, "node_modules/flat-cache/node_modules/glob": {"version": "7.2.3", "dev": true, "license": "ISC", "dependencies": {"fs.realpath": "^1.0.0", "inflight": "^1.0.4", "inherits": "2", "minimatch": "^3.1.1", "once": "^1.3.0", "path-is-absolute": "^1.0.0"}, "engines": {"node": "*"}, "funding": {"url": "https://github.com/sponsors/isaacs"}}, "node_modules/flat-cache/node_modules/rimraf": {"version": "3.0.2", "dev": true, "license": "ISC", "dependencies": {"glob": "^7.1.3"}, "bin": {"rimraf": "bin.js"}, "funding": {"url": "https://github.com/sponsors/isaacs"}}, "node_modules/flatted": {"version": "3.2.9", "dev": true, "license": "ISC"}, "node_modules/foreground-child": {"version": "3.1.1", "dev": true, "license": "ISC", "dependencies": {"cross-spawn": "^7.0.0", "signal-exit": "^4.0.1"}, "engines": {"node": ">=14"}, "funding": {"url": "https://github.com/sponsors/isaacs"}}, "node_modules/fs.realpath": {"version": "1.0.0", "dev": true, "license": "ISC"}, "node_modules/function-bind": {"version": "1.1.2", "dev": true, "license": "MIT", "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/get-caller-file": {"version": "2.0.5", "dev": true, "license": "ISC", "engines": {"node": "6.* || 8.* || >= 10.*"}}, "node_modules/get-tsconfig": {"version": "4.7.2", "dev": true, "license": "MIT", "dependencies": {"resolve-pkg-maps": "^1.0.0"}, "funding": {"url": "https://github.com/privatenumber/get-tsconfig?sponsor=1"}}, "node_modules/glob": {"version": "8.1.0", "dev": true, "license": "ISC", "dependencies": {"fs.realpath": "^1.0.0", "inflight": "^1.0.4", "inherits": "2", "minimatch": "^5.0.1", "once": "^1.3.0"}, "engines": {"node": ">=12"}, "funding": {"url": "https://github.com/sponsors/isaacs"}}, "node_modules/glob-parent": {"version": "6.0.2", "dev": true, "license": "ISC", "dependencies": {"is-glob": "^4.0.3"}, "engines": {"node": ">=10.13.0"}}, "node_modules/glob-promise": {"version": "5.0.1", "dev": true, "hasInstallScript": true, "license": "MIT", "dependencies": {"@types/glob": "^8.0.0", "npm-install-peers": "^1.2.2"}, "engines": {"node": ">=16"}, "funding": {"type": "individual", "url": "https://github.com/sponsors/ahmadnassri"}, "peerDependencies": {"glob": "^8.0.3"}}, "node_modules/glob/node_modules/brace-expansion": {"version": "2.0.1", "dev": true, "license": "MIT", "dependencies": {"balanced-match": "^1.0.0"}}, "node_modules/glob/node_modules/minimatch": {"version": "5.1.6", "dev": true, "license": "ISC", "dependencies": {"brace-expansion": "^2.0.1"}, "engines": {"node": ">=10"}}, "node_modules/globals": {"version": "13.24.0", "dev": true, "license": "MIT", "dependencies": {"type-fest": "^0.20.2"}, "engines": {"node": ">=8"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/globby": {"version": "11.1.0", "dev": true, "license": "MIT", "dependencies": {"array-union": "^2.1.0", "dir-glob": "^3.0.1", "fast-glob": "^3.2.9", "ignore": "^5.2.0", "merge2": "^1.4.1", "slash": "^3.0.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/graceful-fs": {"version": "4.2.11", "dev": true, "license": "ISC"}, "node_modules/graphemer": {"version": "1.4.0", "dev": true, "license": "MIT"}, "node_modules/has-ansi": {"version": "2.0.0", "dev": true, "license": "MIT", "dependencies": {"ansi-regex": "^2.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/has-ansi/node_modules/ansi-regex": {"version": "2.1.1", "dev": true, "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/has-flag": {"version": "3.0.0", "dev": true, "license": "MIT", "engines": {"node": ">=4"}}, "node_modules/hasown": {"version": "2.0.2", "dev": true, "license": "MIT", "dependencies": {"function-bind": "^1.1.2"}, "engines": {"node": ">= 0.4"}}, "node_modules/husky": {"version": "8.0.3", "dev": true, "license": "MIT", "bin": {"husky": "lib/bin.js"}, "engines": {"node": ">=14"}, "funding": {"url": "https://github.com/sponsors/typicode"}}, "node_modules/ignore": {"version": "5.3.0", "dev": true, "license": "MIT", "engines": {"node": ">= 4"}}, "node_modules/import-fresh": {"version": "3.3.0", "dev": true, "license": "MIT", "dependencies": {"parent-module": "^1.0.0", "resolve-from": "^4.0.0"}, "engines": {"node": ">=6"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/imurmurhash": {"version": "0.1.4", "dev": true, "license": "MIT", "engines": {"node": ">=0.8.19"}}, "node_modules/inflight": {"version": "1.0.6", "dev": true, "license": "ISC", "dependencies": {"once": "^1.3.0", "wrappy": "1"}}, "node_modules/inherits": {"version": "2.0.4", "dev": true, "license": "ISC"}, "node_modules/is-builtin-module": {"version": "3.2.1", "dev": true, "license": "MIT", "dependencies": {"builtin-modules": "^3.3.0"}, "engines": {"node": ">=6"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/is-core-module": {"version": "2.13.1", "dev": true, "license": "MIT", "dependencies": {"hasown": "^2.0.0"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/is-extglob": {"version": "2.1.1", "dev": true, "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/is-fullwidth-code-point": {"version": "3.0.0", "dev": true, "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/is-glob": {"version": "4.0.3", "dev": true, "license": "MIT", "dependencies": {"is-extglob": "^2.1.1"}, "engines": {"node": ">=0.10.0"}}, "node_modules/is-number": {"version": "7.0.0", "dev": true, "license": "MIT", "engines": {"node": ">=0.12.0"}}, "node_modules/is-path-inside": {"version": "3.0.3", "dev": true, "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/isarray": {"version": "1.0.0", "dev": true, "license": "MIT"}, "node_modules/isexe": {"version": "2.0.0", "dev": true, "license": "ISC"}, "node_modules/isobject": {"version": "2.1.0", "dev": true, "license": "MIT", "dependencies": {"isarray": "1.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/jackspeak": {"version": "2.3.6", "dev": true, "license": "BlueOak-1.0.0", "dependencies": {"@isaacs/cliui": "^8.0.2"}, "engines": {"node": ">=14"}, "funding": {"url": "https://github.com/sponsors/isaacs"}, "optionalDependencies": {"@pkgjs/parseargs": "^0.11.0"}}, "node_modules/jest-diff": {"version": "29.7.0", "dev": true, "license": "MIT", "dependencies": {"chalk": "^4.0.0", "diff-sequences": "^29.6.3", "jest-get-type": "^29.6.3", "pretty-format": "^29.7.0"}, "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}}, "node_modules/jest-diff/node_modules/ansi-styles": {"version": "4.3.0", "dev": true, "license": "MIT", "dependencies": {"color-convert": "^2.0.1"}, "engines": {"node": ">=8"}, "funding": {"url": "https://github.com/chalk/ansi-styles?sponsor=1"}}, "node_modules/jest-diff/node_modules/chalk": {"version": "4.1.2", "dev": true, "license": "MIT", "dependencies": {"ansi-styles": "^4.1.0", "supports-color": "^7.1.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/chalk/chalk?sponsor=1"}}, "node_modules/jest-diff/node_modules/color-convert": {"version": "2.0.1", "dev": true, "license": "MIT", "dependencies": {"color-name": "~1.1.4"}, "engines": {"node": ">=7.0.0"}}, "node_modules/jest-diff/node_modules/color-name": {"version": "1.1.4", "dev": true, "license": "MIT"}, "node_modules/jest-diff/node_modules/has-flag": {"version": "4.0.0", "dev": true, "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/jest-diff/node_modules/supports-color": {"version": "7.2.0", "dev": true, "license": "MIT", "dependencies": {"has-flag": "^4.0.0"}, "engines": {"node": ">=8"}}, "node_modules/jest-get-type": {"version": "29.6.3", "dev": true, "license": "MIT", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}}, "node_modules/jest-matcher-utils": {"version": "29.7.0", "dev": true, "license": "MIT", "dependencies": {"chalk": "^4.0.0", "jest-diff": "^29.7.0", "jest-get-type": "^29.6.3", "pretty-format": "^29.7.0"}, "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}}, "node_modules/jest-matcher-utils/node_modules/ansi-styles": {"version": "4.3.0", "dev": true, "license": "MIT", "dependencies": {"color-convert": "^2.0.1"}, "engines": {"node": ">=8"}, "funding": {"url": "https://github.com/chalk/ansi-styles?sponsor=1"}}, "node_modules/jest-matcher-utils/node_modules/chalk": {"version": "4.1.2", "dev": true, "license": "MIT", "dependencies": {"ansi-styles": "^4.1.0", "supports-color": "^7.1.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/chalk/chalk?sponsor=1"}}, "node_modules/jest-matcher-utils/node_modules/color-convert": {"version": "2.0.1", "dev": true, "license": "MIT", "dependencies": {"color-name": "~1.1.4"}, "engines": {"node": ">=7.0.0"}}, "node_modules/jest-matcher-utils/node_modules/color-name": {"version": "1.1.4", "dev": true, "license": "MIT"}, "node_modules/jest-matcher-utils/node_modules/has-flag": {"version": "4.0.0", "dev": true, "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/jest-matcher-utils/node_modules/supports-color": {"version": "7.2.0", "dev": true, "license": "MIT", "dependencies": {"has-flag": "^4.0.0"}, "engines": {"node": ">=8"}}, "node_modules/jest-message-util": {"version": "29.7.0", "dev": true, "license": "MIT", "dependencies": {"@babel/code-frame": "^7.12.13", "@jest/types": "^29.6.3", "@types/stack-utils": "^2.0.0", "chalk": "^4.0.0", "graceful-fs": "^4.2.9", "micromatch": "^4.0.4", "pretty-format": "^29.7.0", "slash": "^3.0.0", "stack-utils": "^2.0.3"}, "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}}, "node_modules/jest-message-util/node_modules/ansi-styles": {"version": "4.3.0", "dev": true, "license": "MIT", "dependencies": {"color-convert": "^2.0.1"}, "engines": {"node": ">=8"}, "funding": {"url": "https://github.com/chalk/ansi-styles?sponsor=1"}}, "node_modules/jest-message-util/node_modules/chalk": {"version": "4.1.2", "dev": true, "license": "MIT", "dependencies": {"ansi-styles": "^4.1.0", "supports-color": "^7.1.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/chalk/chalk?sponsor=1"}}, "node_modules/jest-message-util/node_modules/color-convert": {"version": "2.0.1", "dev": true, "license": "MIT", "dependencies": {"color-name": "~1.1.4"}, "engines": {"node": ">=7.0.0"}}, "node_modules/jest-message-util/node_modules/color-name": {"version": "1.1.4", "dev": true, "license": "MIT"}, "node_modules/jest-message-util/node_modules/has-flag": {"version": "4.0.0", "dev": true, "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/jest-message-util/node_modules/supports-color": {"version": "7.2.0", "dev": true, "license": "MIT", "dependencies": {"has-flag": "^4.0.0"}, "engines": {"node": ">=8"}}, "node_modules/jest-util": {"version": "29.7.0", "dev": true, "license": "MIT", "dependencies": {"@jest/types": "^29.6.3", "@types/node": "*", "chalk": "^4.0.0", "ci-info": "^3.2.0", "graceful-fs": "^4.2.9", "picomatch": "^2.2.3"}, "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}}, "node_modules/jest-util/node_modules/ansi-styles": {"version": "4.3.0", "dev": true, "license": "MIT", "dependencies": {"color-convert": "^2.0.1"}, "engines": {"node": ">=8"}, "funding": {"url": "https://github.com/chalk/ansi-styles?sponsor=1"}}, "node_modules/jest-util/node_modules/chalk": {"version": "4.1.2", "dev": true, "license": "MIT", "dependencies": {"ansi-styles": "^4.1.0", "supports-color": "^7.1.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/chalk/chalk?sponsor=1"}}, "node_modules/jest-util/node_modules/color-convert": {"version": "2.0.1", "dev": true, "license": "MIT", "dependencies": {"color-name": "~1.1.4"}, "engines": {"node": ">=7.0.0"}}, "node_modules/jest-util/node_modules/color-name": {"version": "1.1.4", "dev": true, "license": "MIT"}, "node_modules/jest-util/node_modules/has-flag": {"version": "4.0.0", "dev": true, "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/jest-util/node_modules/supports-color": {"version": "7.2.0", "dev": true, "license": "MIT", "dependencies": {"has-flag": "^4.0.0"}, "engines": {"node": ">=8"}}, "node_modules/jju": {"version": "1.4.0", "dev": true, "license": "MIT"}, "node_modules/js-sha3": {"version": "0.9.3", "dev": true, "license": "MIT"}, "node_modules/js-tokens": {"version": "4.0.0", "dev": true, "license": "MIT"}, "node_modules/js-yaml": {"version": "4.1.0", "dev": true, "license": "MIT", "dependencies": {"argparse": "^2.0.1"}, "bin": {"js-yaml": "bin/js-yaml.js"}}, "node_modules/jsdoc-type-pratt-parser": {"version": "4.0.0", "dev": true, "license": "MIT", "engines": {"node": ">=12.0.0"}}, "node_modules/json-buffer": {"version": "3.0.1", "dev": true, "license": "MIT"}, "node_modules/json-schema-traverse": {"version": "0.4.1", "dev": true, "license": "MIT"}, "node_modules/json-stable-stringify-without-jsonify": {"version": "1.0.1", "dev": true, "license": "MIT"}, "node_modules/jsonc-parser": {"version": "3.2.1", "dev": true, "license": "MIT"}, "node_modules/keyv": {"version": "4.5.4", "dev": true, "license": "MIT", "dependencies": {"json-buffer": "3.0.1"}}, "node_modules/levn": {"version": "0.4.1", "dev": true, "license": "MIT", "dependencies": {"prelude-ls": "^1.2.1", "type-check": "~0.4.0"}, "engines": {"node": ">= 0.8.0"}}, "node_modules/line-column": {"version": "1.0.2", "dev": true, "license": "MIT", "dependencies": {"isarray": "^1.0.0", "isobject": "^2.0.0"}}, "node_modules/locate-path": {"version": "6.0.0", "dev": true, "license": "MIT", "dependencies": {"p-locate": "^5.0.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/lodash": {"version": "4.17.21", "dev": true, "license": "MIT"}, "node_modules/lodash.clonedeep": {"version": "4.5.0", "dev": true, "license": "MIT"}, "node_modules/lodash.isequal": {"version": "4.5.0", "resolved": "https://registry.npmjs.org/lodash.isequal/-/lodash.isequal-4.5.0.tgz", "integrity": "sha512-pDo3lu8Jhfjqls6GkMgpahsF9kCyayhgykjyLMNFTKWrpVdAQtYyB4muAMWozBB4ig/dtWAmsMxLEI8wuz+DYQ==", "dev": true}, "node_modules/lodash.merge": {"version": "4.6.2", "dev": true, "license": "MIT"}, "node_modules/lodash.truncate": {"version": "4.4.2", "dev": true, "license": "MIT"}, "node_modules/long": {"version": "5.2.3", "dev": true, "license": "Apache-2.0"}, "node_modules/lru-cache": {"version": "6.0.0", "dev": true, "license": "ISC", "dependencies": {"yallist": "^4.0.0"}, "engines": {"node": ">=10"}}, "node_modules/merge2": {"version": "1.4.1", "dev": true, "license": "MIT", "engines": {"node": ">= 8"}}, "node_modules/micromatch": {"version": "4.0.5", "dev": true, "license": "MIT", "dependencies": {"braces": "^3.0.2", "picomatch": "^2.3.1"}, "engines": {"node": ">=8.6"}}, "node_modules/minimatch": {"version": "3.1.2", "dev": true, "license": "ISC", "dependencies": {"brace-expansion": "^1.1.7"}, "engines": {"node": "*"}}, "node_modules/minipass": {"version": "7.0.4", "dev": true, "license": "ISC", "engines": {"node": ">=16 || 14 >=14.17"}}, "node_modules/mkdirp": {"version": "2.1.6", "dev": true, "license": "MIT", "bin": {"mkdirp": "dist/cjs/src/bin.js"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/isaacs"}}, "node_modules/moo": {"version": "0.5.2", "dev": true, "license": "BSD-3-<PERSON><PERSON>"}, "node_modules/ms": {"version": "2.1.2", "dev": true, "license": "MIT"}, "node_modules/natural-compare": {"version": "1.4.0", "dev": true, "license": "MIT"}, "node_modules/natural-compare-lite": {"version": "1.4.0", "dev": true, "license": "MIT"}, "node_modules/nearley": {"version": "2.20.1", "dev": true, "license": "MIT", "dependencies": {"commander": "^2.19.0", "moo": "^0.5.0", "railroad-diagrams": "^1.0.0", "randexp": "0.4.6"}, "bin": {"nearley-railroad": "bin/nearley-railroad.js", "nearley-test": "bin/nearley-test.js", "nearley-unparse": "bin/nearley-unparse.js", "nearleyc": "bin/nearleyc.js"}, "funding": {"type": "individual", "url": "https://nearley.js.org/#give-to-nearley"}}, "node_modules/nearley/node_modules/commander": {"version": "2.20.3", "dev": true, "license": "MIT"}, "node_modules/node-gyp-build": {"version": "4.8.0", "dev": true, "license": "MIT", "optional": true, "bin": {"node-gyp-build": "bin.js", "node-gyp-build-optional": "optional.js", "node-gyp-build-test": "build-test.js"}}, "node_modules/npm": {"version": "6.14.18", "bundleDependencies": ["abbrev", "ansicolors", "ansistyles", "aproba", "archy", "bin-links", "bluebird", "byte-size", "cacache", "call-limit", "chownr", "ci-info", "cli-columns", "cli-table3", "cmd-shim", "columnify", "config-chain", "debuglog", "detect-indent", "detect-newline", "dezalgo", "editor", "figgy-pudding", "find-npm-prefix", "fs-vacuum", "fs-write-stream-atomic", "gentle-fs", "glob", "graceful-fs", "has-unicode", "hosted-git-info", "iferr", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "infer-owner", "inflight", "inherits", "ini", "init-package-json", "is-cidr", "json-parse-better-errors", "JSONStream", "lazy-property", "libcipm", "libnpm", "libnpmaccess", "libnpmhook", "libnpmorg", "libnpmsearch", "libnpmteam", "libnpx", "lock-verify", "lockfile", "lodash._baseindexof", "lodash._baseuniq", "lodash._bindcallback", "lodash._cacheindexof", "lodash._createcache", "lodash._getnative", "lodash.clonedeep", "lodash.restparam", "lodash.union", "lodash.uniq", "lodash.without", "lru-cache", "meant", "mississippi", "mkdirp", "move-concurrently", "node-gyp", "nopt", "normalize-package-data", "npm-audit-report", "npm-cache-filename", "npm-install-checks", "npm-lifecycle", "npm-package-arg", "npm-packlist", "npm-pick-manifest", "npm-profile", "npm-registry-fetch", "npm-user-validate", "npmlog", "once", "opener", "osenv", "pacote", "path-is-inside", "promise-inflight", "qrcode-terminal", "query-string", "qw", "read-cmd-shim", "read-installed", "read-package-json", "read-package-tree", "read", "readable-stream", "readdir-scoped-modules", "request", "retry", "<PERSON><PERSON><PERSON>", "safe-buffer", "semver", "sha", "slide", "sorted-object", "sorted-union-stream", "ssri", "stringify-package", "tar", "text-table", "tiny-relative-date", "uid-number", "umask", "unique-filename", "unpipe", "update-notifier", "uuid", "validate-npm-package-license", "validate-npm-package-name", "which", "worker-farm", "write-file-atomic"], "dev": true, "license": "Artistic-2.0", "dependencies": {"abbrev": "~1.1.1", "ansicolors": "~0.3.2", "ansistyles": "~0.1.3", "aproba": "^2.0.0", "archy": "~1.0.0", "bin-links": "^1.1.8", "bluebird": "^3.7.2", "byte-size": "^5.0.1", "cacache": "^12.0.4", "call-limit": "^1.1.1", "chownr": "^1.1.4", "ci-info": "^2.0.0", "cli-columns": "^3.1.2", "cli-table3": "^0.5.1", "cmd-shim": "^3.0.3", "columnify": "~1.5.4", "config-chain": "^1.1.13", "detect-indent": "~5.0.0", "detect-newline": "^2.1.0", "dezalgo": "^1.0.4", "editor": "~1.0.0", "figgy-pudding": "^3.5.2", "find-npm-prefix": "^1.0.2", "fs-vacuum": "~1.2.10", "fs-write-stream-atomic": "~1.0.10", "gentle-fs": "^2.3.1", "glob": "^7.2.3", "graceful-fs": "^4.2.10", "has-unicode": "~2.0.1", "hosted-git-info": "^2.8.9", "iferr": "^1.0.2", "infer-owner": "^1.0.4", "inflight": "~1.0.6", "inherits": "^2.0.4", "ini": "^1.3.8", "init-package-json": "^1.10.3", "is-cidr": "^3.1.1", "json-parse-better-errors": "^1.0.2", "JSONStream": "^1.3.5", "lazy-property": "~1.0.0", "libcipm": "^4.0.8", "libnpm": "^3.0.1", "libnpmaccess": "^3.0.2", "libnpmhook": "^5.0.3", "libnpmorg": "^1.0.1", "libnpmsearch": "^2.0.2", "libnpmteam": "^1.0.2", "libnpx": "^10.2.4", "lock-verify": "^2.2.2", "lockfile": "^1.0.4", "lodash._baseuniq": "~4.6.0", "lodash.clonedeep": "~4.5.0", "lodash.union": "~4.6.0", "lodash.uniq": "~4.5.0", "lodash.without": "~4.4.0", "lru-cache": "^5.1.1", "meant": "^1.0.3", "mississippi": "^3.0.0", "mkdirp": "^0.5.6", "move-concurrently": "^1.0.1", "node-gyp": "^5.1.1", "nopt": "^4.0.3", "normalize-package-data": "^2.5.0", "npm-audit-report": "^1.3.3", "npm-cache-filename": "~1.0.2", "npm-install-checks": "^3.0.2", "npm-lifecycle": "^3.1.5", "npm-package-arg": "^6.1.1", "npm-packlist": "^1.4.8", "npm-pick-manifest": "^3.0.2", "npm-profile": "^4.0.4", "npm-registry-fetch": "^4.0.7", "npm-user-validate": "^1.0.1", "npmlog": "~4.1.2", "once": "~1.4.0", "opener": "^1.5.2", "osenv": "^0.1.5", "pacote": "^9.5.12", "path-is-inside": "~1.0.2", "promise-inflight": "~1.0.1", "qrcode-terminal": "^0.12.0", "query-string": "^6.14.1", "qw": "^1.0.2", "read": "~1.0.7", "read-cmd-shim": "^1.0.5", "read-installed": "~4.0.3", "read-package-json": "^2.1.2", "read-package-tree": "^5.3.1", "readable-stream": "^3.6.0", "readdir-scoped-modules": "^1.1.0", "request": "^2.88.2", "retry": "^0.12.0", "rimraf": "^2.7.1", "safe-buffer": "^5.2.1", "semver": "^5.7.1", "sha": "^3.0.0", "slide": "~1.1.6", "sorted-object": "~2.0.1", "sorted-union-stream": "~2.1.3", "ssri": "^6.0.2", "stringify-package": "^1.0.1", "tar": "^4.4.19", "text-table": "~0.2.0", "tiny-relative-date": "^1.3.0", "uid-number": "0.0.6", "umask": "~1.1.0", "unique-filename": "^1.1.1", "unpipe": "~1.0.0", "update-notifier": "^2.5.0", "uuid": "^3.4.0", "validate-npm-package-license": "^3.0.4", "validate-npm-package-name": "~3.0.0", "which": "^1.3.1", "worker-farm": "^1.7.0", "write-file-atomic": "^2.4.3"}, "bin": {"npm": "bin/npm-cli.js", "npx": "bin/npx-cli.js"}, "engines": {"node": "6 >=6.2.0 || 8 || >=9.3.0"}}, "node_modules/npm-install-peers": {"version": "1.2.2", "dev": true, "license": "MIT", "dependencies": {"chalk": "^1.1.3", "npm": "< 7.0"}, "bin": {"npm-install-peers": "bin/npm-install-peers.js"}}, "node_modules/npm-install-peers/node_modules/ansi-regex": {"version": "2.1.1", "dev": true, "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/npm-install-peers/node_modules/ansi-styles": {"version": "2.2.1", "dev": true, "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/npm-install-peers/node_modules/chalk": {"version": "1.1.3", "dev": true, "license": "MIT", "dependencies": {"ansi-styles": "^2.2.1", "escape-string-regexp": "^1.0.2", "has-ansi": "^2.0.0", "strip-ansi": "^3.0.0", "supports-color": "^2.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/npm-install-peers/node_modules/escape-string-regexp": {"version": "1.0.5", "dev": true, "license": "MIT", "engines": {"node": ">=0.8.0"}}, "node_modules/npm-install-peers/node_modules/strip-ansi": {"version": "3.0.1", "dev": true, "license": "MIT", "dependencies": {"ansi-regex": "^2.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/npm-install-peers/node_modules/supports-color": {"version": "2.0.0", "dev": true, "license": "MIT", "engines": {"node": ">=0.8.0"}}, "node_modules/npm/node_modules/@iarna/cli": {"version": "2.1.0", "dev": true, "inBundle": true, "license": "ISC", "dependencies": {"glob": "^7.1.2", "signal-exit": "^3.0.2"}}, "node_modules/npm/node_modules/abbrev": {"version": "1.1.1", "dev": true, "inBundle": true, "license": "ISC"}, "node_modules/npm/node_modules/agent-base": {"version": "4.3.0", "dev": true, "inBundle": true, "license": "MIT", "dependencies": {"es6-promisify": "^5.0.0"}, "engines": {"node": ">= 4.0.0"}}, "node_modules/npm/node_modules/agentkeepalive": {"version": "3.5.2", "dev": true, "inBundle": true, "license": "MIT", "dependencies": {"humanize-ms": "^1.2.1"}, "engines": {"node": ">= 4.0.0"}}, "node_modules/npm/node_modules/ansi-align": {"version": "2.0.0", "dev": true, "inBundle": true, "license": "ISC", "dependencies": {"string-width": "^2.0.0"}}, "node_modules/npm/node_modules/ansi-regex": {"version": "2.1.1", "dev": true, "inBundle": true, "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/npm/node_modules/ansi-styles": {"version": "3.2.1", "dev": true, "inBundle": true, "license": "MIT", "dependencies": {"color-convert": "^1.9.0"}, "engines": {"node": ">=4"}}, "node_modules/npm/node_modules/ansicolors": {"version": "0.3.2", "dev": true, "inBundle": true, "license": "MIT"}, "node_modules/npm/node_modules/ansistyles": {"version": "0.1.3", "dev": true, "inBundle": true, "license": "MIT"}, "node_modules/npm/node_modules/aproba": {"version": "2.0.0", "dev": true, "inBundle": true, "license": "ISC"}, "node_modules/npm/node_modules/archy": {"version": "1.0.0", "dev": true, "inBundle": true, "license": "MIT"}, "node_modules/npm/node_modules/are-we-there-yet": {"version": "1.1.4", "dev": true, "inBundle": true, "license": "ISC", "dependencies": {"delegates": "^1.0.0", "readable-stream": "^2.0.6"}}, "node_modules/npm/node_modules/are-we-there-yet/node_modules/readable-stream": {"version": "2.3.6", "dev": true, "inBundle": true, "license": "MIT", "dependencies": {"core-util-is": "~1.0.0", "inherits": "~2.0.3", "isarray": "~1.0.0", "process-nextick-args": "~2.0.0", "safe-buffer": "~5.1.1", "string_decoder": "~1.1.1", "util-deprecate": "~1.0.1"}}, "node_modules/npm/node_modules/are-we-there-yet/node_modules/readable-stream/node_modules/safe-buffer": {"version": "5.1.2", "dev": true, "inBundle": true, "license": "MIT"}, "node_modules/npm/node_modules/are-we-there-yet/node_modules/string_decoder": {"version": "1.1.1", "dev": true, "inBundle": true, "license": "MIT", "dependencies": {"safe-buffer": "~5.1.0"}}, "node_modules/npm/node_modules/are-we-there-yet/node_modules/string_decoder/node_modules/safe-buffer": {"version": "5.1.2", "dev": true, "inBundle": true, "license": "MIT"}, "node_modules/npm/node_modules/asap": {"version": "2.0.6", "dev": true, "inBundle": true, "license": "MIT"}, "node_modules/npm/node_modules/asn1": {"version": "0.2.6", "dev": true, "inBundle": true, "license": "MIT", "dependencies": {"safer-buffer": "~2.1.0"}}, "node_modules/npm/node_modules/assert-plus": {"version": "1.0.0", "dev": true, "inBundle": true, "license": "MIT", "engines": {"node": ">=0.8"}}, "node_modules/npm/node_modules/asynckit": {"version": "0.4.0", "dev": true, "inBundle": true, "license": "MIT"}, "node_modules/npm/node_modules/aws-sign2": {"version": "0.7.0", "dev": true, "inBundle": true, "license": "Apache-2.0", "engines": {"node": "*"}}, "node_modules/npm/node_modules/aws4": {"version": "1.11.0", "dev": true, "inBundle": true, "license": "MIT"}, "node_modules/npm/node_modules/balanced-match": {"version": "1.0.2", "dev": true, "inBundle": true, "license": "MIT"}, "node_modules/npm/node_modules/bcrypt-pbkdf": {"version": "1.0.2", "dev": true, "inBundle": true, "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"tweetnacl": "^0.14.3"}}, "node_modules/npm/node_modules/bin-links": {"version": "1.1.8", "dev": true, "inBundle": true, "license": "Artistic-2.0", "dependencies": {"bluebird": "^3.5.3", "cmd-shim": "^3.0.0", "gentle-fs": "^2.3.0", "graceful-fs": "^4.1.15", "npm-normalize-package-bin": "^1.0.0", "write-file-atomic": "^2.3.0"}}, "node_modules/npm/node_modules/bluebird": {"version": "3.7.2", "dev": true, "inBundle": true, "license": "MIT"}, "node_modules/npm/node_modules/boxen": {"version": "1.3.0", "dev": true, "inBundle": true, "license": "MIT", "dependencies": {"ansi-align": "^2.0.0", "camelcase": "^4.0.0", "chalk": "^2.0.1", "cli-boxes": "^1.0.0", "string-width": "^2.0.0", "term-size": "^1.2.0", "widest-line": "^2.0.0"}, "engines": {"node": ">=4"}}, "node_modules/npm/node_modules/brace-expansion": {"version": "1.1.11", "dev": true, "inBundle": true, "license": "MIT", "dependencies": {"balanced-match": "^1.0.0", "concat-map": "0.0.1"}}, "node_modules/npm/node_modules/buffer-from": {"version": "1.0.0", "dev": true, "inBundle": true, "license": "MIT"}, "node_modules/npm/node_modules/builtins": {"version": "1.0.3", "dev": true, "inBundle": true, "license": "MIT"}, "node_modules/npm/node_modules/byline": {"version": "5.0.0", "dev": true, "inBundle": true, "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/npm/node_modules/byte-size": {"version": "5.0.1", "dev": true, "inBundle": true, "license": "MIT", "engines": {"node": ">=6.0.0"}}, "node_modules/npm/node_modules/cacache": {"version": "12.0.4", "dev": true, "inBundle": true, "license": "ISC", "dependencies": {"bluebird": "^3.5.5", "chownr": "^1.1.1", "figgy-pudding": "^3.5.1", "glob": "^7.1.4", "graceful-fs": "^4.1.15", "infer-owner": "^1.0.3", "lru-cache": "^5.1.1", "mississippi": "^3.0.0", "mkdirp": "^0.5.1", "move-concurrently": "^1.0.1", "promise-inflight": "^1.0.1", "rimraf": "^2.6.3", "ssri": "^6.0.1", "unique-filename": "^1.1.1", "y18n": "^4.0.0"}}, "node_modules/npm/node_modules/call-limit": {"version": "1.1.1", "dev": true, "inBundle": true, "license": "ISC"}, "node_modules/npm/node_modules/camelcase": {"version": "4.1.0", "dev": true, "inBundle": true, "license": "MIT", "engines": {"node": ">=4"}}, "node_modules/npm/node_modules/capture-stack-trace": {"version": "1.0.0", "dev": true, "inBundle": true, "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/npm/node_modules/caseless": {"version": "0.12.0", "dev": true, "inBundle": true, "license": "Apache-2.0"}, "node_modules/npm/node_modules/chalk": {"version": "2.4.1", "dev": true, "inBundle": true, "license": "MIT", "dependencies": {"ansi-styles": "^3.2.1", "escape-string-regexp": "^1.0.5", "supports-color": "^5.3.0"}, "engines": {"node": ">=4"}}, "node_modules/npm/node_modules/chownr": {"version": "1.1.4", "dev": true, "inBundle": true, "license": "ISC"}, "node_modules/npm/node_modules/ci-info": {"version": "2.0.0", "dev": true, "inBundle": true, "license": "MIT"}, "node_modules/npm/node_modules/cidr-regex": {"version": "2.0.10", "dev": true, "inBundle": true, "license": "BSD-2-<PERSON><PERSON>", "dependencies": {"ip-regex": "^2.1.0"}, "engines": {"node": ">=4"}}, "node_modules/npm/node_modules/cli-boxes": {"version": "1.0.0", "dev": true, "inBundle": true, "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/npm/node_modules/cli-columns": {"version": "3.1.2", "dev": true, "inBundle": true, "license": "MIT", "dependencies": {"string-width": "^2.0.0", "strip-ansi": "^3.0.1"}, "engines": {"node": ">= 4"}}, "node_modules/npm/node_modules/cli-table3": {"version": "0.5.1", "dev": true, "inBundle": true, "license": "MIT", "dependencies": {"object-assign": "^4.1.0", "string-width": "^2.1.1"}, "engines": {"node": ">=6"}, "optionalDependencies": {"colors": "^1.1.2"}}, "node_modules/npm/node_modules/cliui": {"version": "5.0.0", "dev": true, "inBundle": true, "license": "ISC", "dependencies": {"string-width": "^3.1.0", "strip-ansi": "^5.2.0", "wrap-ansi": "^5.1.0"}}, "node_modules/npm/node_modules/cliui/node_modules/ansi-regex": {"version": "4.1.1", "dev": true, "inBundle": true, "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/npm/node_modules/cliui/node_modules/is-fullwidth-code-point": {"version": "2.0.0", "dev": true, "inBundle": true, "license": "MIT", "engines": {"node": ">=4"}}, "node_modules/npm/node_modules/cliui/node_modules/string-width": {"version": "3.1.0", "dev": true, "inBundle": true, "license": "MIT", "dependencies": {"emoji-regex": "^7.0.1", "is-fullwidth-code-point": "^2.0.0", "strip-ansi": "^5.1.0"}, "engines": {"node": ">=6"}}, "node_modules/npm/node_modules/cliui/node_modules/strip-ansi": {"version": "5.2.0", "dev": true, "inBundle": true, "license": "MIT", "dependencies": {"ansi-regex": "^4.1.0"}, "engines": {"node": ">=6"}}, "node_modules/npm/node_modules/clone": {"version": "1.0.4", "dev": true, "inBundle": true, "license": "MIT", "engines": {"node": ">=0.8"}}, "node_modules/npm/node_modules/cmd-shim": {"version": "3.0.3", "dev": true, "inBundle": true, "license": "ISC", "dependencies": {"graceful-fs": "^4.1.2", "mkdirp": "~0.5.0"}}, "node_modules/npm/node_modules/code-point-at": {"version": "1.1.0", "dev": true, "inBundle": true, "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/npm/node_modules/color-convert": {"version": "1.9.1", "dev": true, "inBundle": true, "license": "MIT", "dependencies": {"color-name": "^1.1.1"}}, "node_modules/npm/node_modules/color-name": {"version": "1.1.3", "dev": true, "inBundle": true, "license": "MIT"}, "node_modules/npm/node_modules/colors": {"version": "1.3.3", "dev": true, "inBundle": true, "license": "MIT", "optional": true, "engines": {"node": ">=0.1.90"}}, "node_modules/npm/node_modules/columnify": {"version": "1.5.4", "dev": true, "inBundle": true, "license": "MIT", "dependencies": {"strip-ansi": "^3.0.0", "wcwidth": "^1.0.0"}}, "node_modules/npm/node_modules/combined-stream": {"version": "1.0.8", "dev": true, "inBundle": true, "license": "MIT", "dependencies": {"delayed-stream": "~1.0.0"}, "engines": {"node": ">= 0.8"}}, "node_modules/npm/node_modules/concat-map": {"version": "0.0.1", "dev": true, "inBundle": true, "license": "MIT"}, "node_modules/npm/node_modules/concat-stream": {"version": "1.6.2", "dev": true, "engines": ["node >= 0.8"], "inBundle": true, "license": "MIT", "dependencies": {"buffer-from": "^1.0.0", "inherits": "^2.0.3", "readable-stream": "^2.2.2", "typedarray": "^0.0.6"}}, "node_modules/npm/node_modules/concat-stream/node_modules/readable-stream": {"version": "2.3.6", "dev": true, "inBundle": true, "license": "MIT", "dependencies": {"core-util-is": "~1.0.0", "inherits": "~2.0.3", "isarray": "~1.0.0", "process-nextick-args": "~2.0.0", "safe-buffer": "~5.1.1", "string_decoder": "~1.1.1", "util-deprecate": "~1.0.1"}}, "node_modules/npm/node_modules/concat-stream/node_modules/readable-stream/node_modules/safe-buffer": {"version": "5.1.2", "dev": true, "inBundle": true, "license": "MIT"}, "node_modules/npm/node_modules/concat-stream/node_modules/string_decoder": {"version": "1.1.1", "dev": true, "inBundle": true, "license": "MIT", "dependencies": {"safe-buffer": "~5.1.0"}}, "node_modules/npm/node_modules/concat-stream/node_modules/string_decoder/node_modules/safe-buffer": {"version": "5.1.2", "dev": true, "inBundle": true, "license": "MIT"}, "node_modules/npm/node_modules/config-chain": {"version": "1.1.13", "dev": true, "inBundle": true, "license": "MIT", "dependencies": {"ini": "^1.3.4", "proto-list": "~1.2.1"}}, "node_modules/npm/node_modules/configstore": {"version": "3.1.5", "dev": true, "inBundle": true, "license": "BSD-2-<PERSON><PERSON>", "dependencies": {"dot-prop": "^4.2.1", "graceful-fs": "^4.1.2", "make-dir": "^1.0.0", "unique-string": "^1.0.0", "write-file-atomic": "^2.0.0", "xdg-basedir": "^3.0.0"}, "engines": {"node": ">=4"}}, "node_modules/npm/node_modules/console-control-strings": {"version": "1.1.0", "dev": true, "inBundle": true, "license": "ISC"}, "node_modules/npm/node_modules/copy-concurrently": {"version": "1.0.5", "dev": true, "inBundle": true, "license": "ISC", "dependencies": {"aproba": "^1.1.1", "fs-write-stream-atomic": "^1.0.8", "iferr": "^0.1.5", "mkdirp": "^0.5.1", "rimraf": "^2.5.4", "run-queue": "^1.0.0"}}, "node_modules/npm/node_modules/copy-concurrently/node_modules/aproba": {"version": "1.2.0", "dev": true, "inBundle": true, "license": "ISC"}, "node_modules/npm/node_modules/copy-concurrently/node_modules/iferr": {"version": "0.1.5", "dev": true, "inBundle": true, "license": "MIT"}, "node_modules/npm/node_modules/core-util-is": {"version": "1.0.2", "dev": true, "inBundle": true, "license": "MIT"}, "node_modules/npm/node_modules/create-error-class": {"version": "3.0.2", "dev": true, "inBundle": true, "license": "MIT", "dependencies": {"capture-stack-trace": "^1.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/npm/node_modules/cross-spawn": {"version": "5.1.0", "dev": true, "inBundle": true, "license": "MIT", "dependencies": {"lru-cache": "^4.0.1", "shebang-command": "^1.2.0", "which": "^1.2.9"}}, "node_modules/npm/node_modules/cross-spawn/node_modules/lru-cache": {"version": "4.1.5", "dev": true, "inBundle": true, "license": "ISC", "dependencies": {"pseudomap": "^1.0.2", "yallist": "^2.1.2"}}, "node_modules/npm/node_modules/cross-spawn/node_modules/yallist": {"version": "2.1.2", "dev": true, "inBundle": true, "license": "ISC"}, "node_modules/npm/node_modules/crypto-random-string": {"version": "1.0.0", "dev": true, "inBundle": true, "license": "MIT", "engines": {"node": ">=4"}}, "node_modules/npm/node_modules/cyclist": {"version": "0.2.2", "dev": true, "inBundle": true}, "node_modules/npm/node_modules/dashdash": {"version": "1.14.1", "dev": true, "inBundle": true, "license": "MIT", "dependencies": {"assert-plus": "^1.0.0"}, "engines": {"node": ">=0.10"}}, "node_modules/npm/node_modules/debug": {"version": "3.1.0", "dev": true, "inBundle": true, "license": "MIT", "dependencies": {"ms": "2.0.0"}}, "node_modules/npm/node_modules/debug/node_modules/ms": {"version": "2.0.0", "dev": true, "inBundle": true, "license": "MIT"}, "node_modules/npm/node_modules/debuglog": {"version": "1.0.1", "dev": true, "inBundle": true, "license": "MIT", "engines": {"node": "*"}}, "node_modules/npm/node_modules/decamelize": {"version": "1.2.0", "dev": true, "inBundle": true, "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/npm/node_modules/decode-uri-component": {"version": "0.2.2", "dev": true, "inBundle": true, "license": "MIT", "engines": {"node": ">=0.10"}}, "node_modules/npm/node_modules/deep-extend": {"version": "0.6.0", "dev": true, "inBundle": true, "license": "MIT", "engines": {"node": ">=4.0.0"}}, "node_modules/npm/node_modules/defaults": {"version": "1.0.3", "dev": true, "inBundle": true, "license": "MIT", "dependencies": {"clone": "^1.0.2"}}, "node_modules/npm/node_modules/define-properties": {"version": "1.1.3", "dev": true, "inBundle": true, "license": "MIT", "dependencies": {"object-keys": "^1.0.12"}, "engines": {"node": ">= 0.4"}}, "node_modules/npm/node_modules/delayed-stream": {"version": "1.0.0", "dev": true, "inBundle": true, "license": "MIT", "engines": {"node": ">=0.4.0"}}, "node_modules/npm/node_modules/delegates": {"version": "1.0.0", "dev": true, "inBundle": true, "license": "MIT"}, "node_modules/npm/node_modules/detect-indent": {"version": "5.0.0", "dev": true, "inBundle": true, "license": "MIT", "engines": {"node": ">=4"}}, "node_modules/npm/node_modules/detect-newline": {"version": "2.1.0", "dev": true, "inBundle": true, "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/npm/node_modules/dezalgo": {"version": "1.0.4", "dev": true, "inBundle": true, "license": "ISC", "dependencies": {"asap": "^2.0.0", "wrappy": "1"}}, "node_modules/npm/node_modules/dot-prop": {"version": "4.2.1", "dev": true, "inBundle": true, "license": "MIT", "dependencies": {"is-obj": "^1.0.0"}, "engines": {"node": ">=4"}}, "node_modules/npm/node_modules/dotenv": {"version": "5.0.1", "dev": true, "inBundle": true, "license": "BSD-2-<PERSON><PERSON>", "engines": {"node": ">=4.6.0"}}, "node_modules/npm/node_modules/duplexer3": {"version": "0.1.4", "dev": true, "inBundle": true, "license": "BSD-3-<PERSON><PERSON>"}, "node_modules/npm/node_modules/duplexify": {"version": "3.6.0", "dev": true, "inBundle": true, "license": "MIT", "dependencies": {"end-of-stream": "^1.0.0", "inherits": "^2.0.1", "readable-stream": "^2.0.0", "stream-shift": "^1.0.0"}}, "node_modules/npm/node_modules/duplexify/node_modules/readable-stream": {"version": "2.3.6", "dev": true, "inBundle": true, "license": "MIT", "dependencies": {"core-util-is": "~1.0.0", "inherits": "~2.0.3", "isarray": "~1.0.0", "process-nextick-args": "~2.0.0", "safe-buffer": "~5.1.1", "string_decoder": "~1.1.1", "util-deprecate": "~1.0.1"}}, "node_modules/npm/node_modules/duplexify/node_modules/readable-stream/node_modules/safe-buffer": {"version": "5.1.2", "dev": true, "inBundle": true, "license": "MIT"}, "node_modules/npm/node_modules/duplexify/node_modules/string_decoder": {"version": "1.1.1", "dev": true, "inBundle": true, "license": "MIT", "dependencies": {"safe-buffer": "~5.1.0"}}, "node_modules/npm/node_modules/duplexify/node_modules/string_decoder/node_modules/safe-buffer": {"version": "5.1.2", "dev": true, "inBundle": true, "license": "MIT"}, "node_modules/npm/node_modules/ecc-jsbn": {"version": "0.1.2", "dev": true, "inBundle": true, "license": "MIT", "dependencies": {"jsbn": "~0.1.0", "safer-buffer": "^2.1.0"}}, "node_modules/npm/node_modules/editor": {"version": "1.0.0", "dev": true, "inBundle": true, "license": "MIT"}, "node_modules/npm/node_modules/emoji-regex": {"version": "7.0.3", "dev": true, "inBundle": true, "license": "MIT"}, "node_modules/npm/node_modules/encoding": {"version": "0.1.12", "dev": true, "inBundle": true, "license": "MIT", "dependencies": {"iconv-lite": "~0.4.13"}}, "node_modules/npm/node_modules/end-of-stream": {"version": "1.4.1", "dev": true, "inBundle": true, "license": "MIT", "dependencies": {"once": "^1.4.0"}}, "node_modules/npm/node_modules/env-paths": {"version": "2.2.1", "dev": true, "inBundle": true, "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/npm/node_modules/err-code": {"version": "1.1.2", "dev": true, "inBundle": true, "license": "MIT"}, "node_modules/npm/node_modules/errno": {"version": "0.1.7", "dev": true, "inBundle": true, "license": "MIT", "dependencies": {"prr": "~1.0.1"}, "bin": {"errno": "cli.js"}}, "node_modules/npm/node_modules/es-abstract": {"version": "1.12.0", "dev": true, "inBundle": true, "license": "MIT", "dependencies": {"es-to-primitive": "^1.1.1", "function-bind": "^1.1.1", "has": "^1.0.1", "is-callable": "^1.1.3", "is-regex": "^1.0.4"}, "engines": {"node": ">= 0.4"}}, "node_modules/npm/node_modules/es-to-primitive": {"version": "1.2.0", "dev": true, "inBundle": true, "license": "MIT", "dependencies": {"is-callable": "^1.1.4", "is-date-object": "^1.0.1", "is-symbol": "^1.0.2"}, "engines": {"node": ">= 0.4"}}, "node_modules/npm/node_modules/es6-promise": {"version": "4.2.8", "dev": true, "inBundle": true, "license": "MIT"}, "node_modules/npm/node_modules/es6-promisify": {"version": "5.0.0", "dev": true, "inBundle": true, "license": "MIT", "dependencies": {"es6-promise": "^4.0.3"}}, "node_modules/npm/node_modules/escape-string-regexp": {"version": "1.0.5", "dev": true, "inBundle": true, "license": "MIT", "engines": {"node": ">=0.8.0"}}, "node_modules/npm/node_modules/execa": {"version": "0.7.0", "dev": true, "inBundle": true, "license": "MIT", "dependencies": {"cross-spawn": "^5.0.1", "get-stream": "^3.0.0", "is-stream": "^1.1.0", "npm-run-path": "^2.0.0", "p-finally": "^1.0.0", "signal-exit": "^3.0.0", "strip-eof": "^1.0.0"}, "engines": {"node": ">=4"}}, "node_modules/npm/node_modules/execa/node_modules/get-stream": {"version": "3.0.0", "dev": true, "inBundle": true, "license": "MIT", "engines": {"node": ">=4"}}, "node_modules/npm/node_modules/extend": {"version": "3.0.2", "dev": true, "inBundle": true, "license": "MIT"}, "node_modules/npm/node_modules/extsprintf": {"version": "1.3.0", "dev": true, "engines": ["node >=0.6.0"], "inBundle": true, "license": "MIT"}, "node_modules/npm/node_modules/fast-json-stable-stringify": {"version": "2.0.0", "dev": true, "inBundle": true, "license": "MIT"}, "node_modules/npm/node_modules/figgy-pudding": {"version": "3.5.2", "dev": true, "inBundle": true, "license": "ISC"}, "node_modules/npm/node_modules/filter-obj": {"version": "1.1.0", "dev": true, "inBundle": true, "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/npm/node_modules/find-npm-prefix": {"version": "1.0.2", "dev": true, "inBundle": true, "license": "ISC"}, "node_modules/npm/node_modules/flush-write-stream": {"version": "1.0.3", "dev": true, "inBundle": true, "license": "MIT", "dependencies": {"inherits": "^2.0.1", "readable-stream": "^2.0.4"}}, "node_modules/npm/node_modules/flush-write-stream/node_modules/readable-stream": {"version": "2.3.6", "dev": true, "inBundle": true, "license": "MIT", "dependencies": {"core-util-is": "~1.0.0", "inherits": "~2.0.3", "isarray": "~1.0.0", "process-nextick-args": "~2.0.0", "safe-buffer": "~5.1.1", "string_decoder": "~1.1.1", "util-deprecate": "~1.0.1"}}, "node_modules/npm/node_modules/flush-write-stream/node_modules/readable-stream/node_modules/safe-buffer": {"version": "5.1.2", "dev": true, "inBundle": true, "license": "MIT"}, "node_modules/npm/node_modules/flush-write-stream/node_modules/string_decoder": {"version": "1.1.1", "dev": true, "inBundle": true, "license": "MIT", "dependencies": {"safe-buffer": "~5.1.0"}}, "node_modules/npm/node_modules/flush-write-stream/node_modules/string_decoder/node_modules/safe-buffer": {"version": "5.1.2", "dev": true, "inBundle": true, "license": "MIT"}, "node_modules/npm/node_modules/forever-agent": {"version": "0.6.1", "dev": true, "inBundle": true, "license": "Apache-2.0", "engines": {"node": "*"}}, "node_modules/npm/node_modules/form-data": {"version": "2.3.3", "dev": true, "inBundle": true, "license": "MIT", "dependencies": {"asynckit": "^0.4.0", "combined-stream": "^1.0.6", "mime-types": "^2.1.12"}, "engines": {"node": ">= 0.12"}}, "node_modules/npm/node_modules/from2": {"version": "2.3.0", "dev": true, "inBundle": true, "license": "MIT", "dependencies": {"inherits": "^2.0.1", "readable-stream": "^2.0.0"}}, "node_modules/npm/node_modules/from2/node_modules/readable-stream": {"version": "2.3.6", "dev": true, "inBundle": true, "license": "MIT", "dependencies": {"core-util-is": "~1.0.0", "inherits": "~2.0.3", "isarray": "~1.0.0", "process-nextick-args": "~2.0.0", "safe-buffer": "~5.1.1", "string_decoder": "~1.1.1", "util-deprecate": "~1.0.1"}}, "node_modules/npm/node_modules/from2/node_modules/readable-stream/node_modules/safe-buffer": {"version": "5.1.2", "dev": true, "inBundle": true, "license": "MIT"}, "node_modules/npm/node_modules/from2/node_modules/string_decoder": {"version": "1.1.1", "dev": true, "inBundle": true, "license": "MIT", "dependencies": {"safe-buffer": "~5.1.0"}}, "node_modules/npm/node_modules/from2/node_modules/string_decoder/node_modules/safe-buffer": {"version": "5.1.2", "dev": true, "inBundle": true, "license": "MIT"}, "node_modules/npm/node_modules/fs-minipass": {"version": "1.2.7", "dev": true, "inBundle": true, "license": "ISC", "dependencies": {"minipass": "^2.6.0"}}, "node_modules/npm/node_modules/fs-minipass/node_modules/minipass": {"version": "2.9.0", "dev": true, "inBundle": true, "license": "ISC", "dependencies": {"safe-buffer": "^5.1.2", "yallist": "^3.0.0"}}, "node_modules/npm/node_modules/fs-vacuum": {"version": "1.2.10", "dev": true, "inBundle": true, "license": "ISC", "dependencies": {"graceful-fs": "^4.1.2", "path-is-inside": "^1.0.1", "rimraf": "^2.5.2"}}, "node_modules/npm/node_modules/fs-write-stream-atomic": {"version": "1.0.10", "dev": true, "inBundle": true, "license": "ISC", "dependencies": {"graceful-fs": "^4.1.2", "iferr": "^0.1.5", "imurmurhash": "^0.1.4", "readable-stream": "1 || 2"}}, "node_modules/npm/node_modules/fs-write-stream-atomic/node_modules/iferr": {"version": "0.1.5", "dev": true, "inBundle": true, "license": "MIT"}, "node_modules/npm/node_modules/fs-write-stream-atomic/node_modules/readable-stream": {"version": "2.3.6", "dev": true, "inBundle": true, "license": "MIT", "dependencies": {"core-util-is": "~1.0.0", "inherits": "~2.0.3", "isarray": "~1.0.0", "process-nextick-args": "~2.0.0", "safe-buffer": "~5.1.1", "string_decoder": "~1.1.1", "util-deprecate": "~1.0.1"}}, "node_modules/npm/node_modules/fs-write-stream-atomic/node_modules/readable-stream/node_modules/safe-buffer": {"version": "5.1.2", "dev": true, "inBundle": true, "license": "MIT"}, "node_modules/npm/node_modules/fs-write-stream-atomic/node_modules/string_decoder": {"version": "1.1.1", "dev": true, "inBundle": true, "license": "MIT", "dependencies": {"safe-buffer": "~5.1.0"}}, "node_modules/npm/node_modules/fs-write-stream-atomic/node_modules/string_decoder/node_modules/safe-buffer": {"version": "5.1.2", "dev": true, "inBundle": true, "license": "MIT"}, "node_modules/npm/node_modules/fs.realpath": {"version": "1.0.0", "dev": true, "inBundle": true, "license": "ISC"}, "node_modules/npm/node_modules/function-bind": {"version": "1.1.1", "dev": true, "inBundle": true, "license": "MIT"}, "node_modules/npm/node_modules/gauge": {"version": "2.7.4", "dev": true, "inBundle": true, "license": "ISC", "dependencies": {"aproba": "^1.0.3", "console-control-strings": "^1.0.0", "has-unicode": "^2.0.0", "object-assign": "^4.1.0", "signal-exit": "^3.0.0", "string-width": "^1.0.1", "strip-ansi": "^3.0.1", "wide-align": "^1.1.0"}}, "node_modules/npm/node_modules/gauge/node_modules/aproba": {"version": "1.2.0", "dev": true, "inBundle": true, "license": "ISC"}, "node_modules/npm/node_modules/gauge/node_modules/string-width": {"version": "1.0.2", "dev": true, "inBundle": true, "license": "MIT", "dependencies": {"code-point-at": "^1.0.0", "is-fullwidth-code-point": "^1.0.0", "strip-ansi": "^3.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/npm/node_modules/genfun": {"version": "5.0.0", "dev": true, "inBundle": true, "license": "MIT"}, "node_modules/npm/node_modules/gentle-fs": {"version": "2.3.1", "dev": true, "inBundle": true, "license": "Artistic-2.0", "dependencies": {"aproba": "^1.1.2", "chownr": "^1.1.2", "cmd-shim": "^3.0.3", "fs-vacuum": "^1.2.10", "graceful-fs": "^4.1.11", "iferr": "^0.1.5", "infer-owner": "^1.0.4", "mkdirp": "^0.5.1", "path-is-inside": "^1.0.2", "read-cmd-shim": "^1.0.1", "slide": "^1.1.6"}}, "node_modules/npm/node_modules/gentle-fs/node_modules/aproba": {"version": "1.2.0", "dev": true, "inBundle": true, "license": "ISC"}, "node_modules/npm/node_modules/gentle-fs/node_modules/iferr": {"version": "0.1.5", "dev": true, "inBundle": true, "license": "MIT"}, "node_modules/npm/node_modules/get-caller-file": {"version": "2.0.5", "dev": true, "inBundle": true, "license": "ISC", "engines": {"node": "6.* || 8.* || >= 10.*"}}, "node_modules/npm/node_modules/get-stream": {"version": "4.1.0", "dev": true, "inBundle": true, "license": "MIT", "dependencies": {"pump": "^3.0.0"}, "engines": {"node": ">=6"}}, "node_modules/npm/node_modules/getpass": {"version": "0.1.7", "dev": true, "inBundle": true, "license": "MIT", "dependencies": {"assert-plus": "^1.0.0"}}, "node_modules/npm/node_modules/glob": {"version": "7.2.3", "dev": true, "inBundle": true, "license": "ISC", "dependencies": {"fs.realpath": "^1.0.0", "inflight": "^1.0.4", "inherits": "2", "minimatch": "^3.1.1", "once": "^1.3.0", "path-is-absolute": "^1.0.0"}, "engines": {"node": "*"}, "funding": {"url": "https://github.com/sponsors/isaacs"}}, "node_modules/npm/node_modules/glob/node_modules/minimatch": {"version": "3.1.2", "dev": true, "inBundle": true, "license": "ISC", "dependencies": {"brace-expansion": "^1.1.7"}, "engines": {"node": "*"}}, "node_modules/npm/node_modules/global-dirs": {"version": "0.1.1", "dev": true, "inBundle": true, "license": "MIT", "dependencies": {"ini": "^1.3.4"}, "engines": {"node": ">=4"}}, "node_modules/npm/node_modules/got": {"version": "6.7.1", "dev": true, "inBundle": true, "license": "MIT", "dependencies": {"create-error-class": "^3.0.0", "duplexer3": "^0.1.4", "get-stream": "^3.0.0", "is-redirect": "^1.0.0", "is-retry-allowed": "^1.0.0", "is-stream": "^1.0.0", "lowercase-keys": "^1.0.0", "safe-buffer": "^5.0.1", "timed-out": "^4.0.0", "unzip-response": "^2.0.1", "url-parse-lax": "^1.0.0"}, "engines": {"node": ">=4"}}, "node_modules/npm/node_modules/got/node_modules/get-stream": {"version": "3.0.0", "dev": true, "inBundle": true, "license": "MIT", "engines": {"node": ">=4"}}, "node_modules/npm/node_modules/graceful-fs": {"version": "4.2.10", "dev": true, "inBundle": true, "license": "ISC"}, "node_modules/npm/node_modules/har-schema": {"version": "2.0.0", "dev": true, "inBundle": true, "license": "ISC", "engines": {"node": ">=4"}}, "node_modules/npm/node_modules/har-validator": {"version": "5.1.5", "deprecated": "this library is no longer supported", "dev": true, "inBundle": true, "license": "MIT", "dependencies": {"ajv": "^6.12.3", "har-schema": "^2.0.0"}, "engines": {"node": ">=6"}}, "node_modules/npm/node_modules/har-validator/node_modules/ajv": {"version": "6.12.6", "dev": true, "inBundle": true, "license": "MIT", "dependencies": {"fast-deep-equal": "^3.1.1", "fast-json-stable-stringify": "^2.0.0", "json-schema-traverse": "^0.4.1", "uri-js": "^4.2.2"}, "funding": {"type": "github", "url": "https://github.com/sponsors/epoberezkin"}}, "node_modules/npm/node_modules/har-validator/node_modules/fast-deep-equal": {"version": "3.1.3", "dev": true, "inBundle": true, "license": "MIT"}, "node_modules/npm/node_modules/har-validator/node_modules/json-schema-traverse": {"version": "0.4.1", "dev": true, "inBundle": true, "license": "MIT"}, "node_modules/npm/node_modules/has": {"version": "1.0.3", "dev": true, "inBundle": true, "license": "MIT", "dependencies": {"function-bind": "^1.1.1"}, "engines": {"node": ">= 0.4.0"}}, "node_modules/npm/node_modules/has-flag": {"version": "3.0.0", "dev": true, "inBundle": true, "license": "MIT", "engines": {"node": ">=4"}}, "node_modules/npm/node_modules/has-symbols": {"version": "1.0.0", "dev": true, "inBundle": true, "license": "MIT", "engines": {"node": ">= 0.4"}}, "node_modules/npm/node_modules/has-unicode": {"version": "2.0.1", "dev": true, "inBundle": true, "license": "ISC"}, "node_modules/npm/node_modules/hosted-git-info": {"version": "2.8.9", "dev": true, "inBundle": true, "license": "ISC"}, "node_modules/npm/node_modules/http-cache-semantics": {"version": "3.8.1", "dev": true, "inBundle": true, "license": "BSD-2-<PERSON><PERSON>"}, "node_modules/npm/node_modules/http-proxy-agent": {"version": "2.1.0", "dev": true, "inBundle": true, "license": "MIT", "dependencies": {"agent-base": "4", "debug": "3.1.0"}, "engines": {"node": ">= 4.5.0"}}, "node_modules/npm/node_modules/http-signature": {"version": "1.2.0", "dev": true, "inBundle": true, "license": "MIT", "dependencies": {"assert-plus": "^1.0.0", "jsprim": "^1.2.2", "sshpk": "^1.7.0"}, "engines": {"node": ">=0.8", "npm": ">=1.3.7"}}, "node_modules/npm/node_modules/https-proxy-agent": {"version": "2.2.4", "dev": true, "inBundle": true, "license": "MIT", "dependencies": {"agent-base": "^4.3.0", "debug": "^3.1.0"}, "engines": {"node": ">= 4.5.0"}}, "node_modules/npm/node_modules/humanize-ms": {"version": "1.2.1", "dev": true, "inBundle": true, "license": "MIT", "dependencies": {"ms": "^2.0.0"}}, "node_modules/npm/node_modules/iconv-lite": {"version": "0.4.23", "dev": true, "inBundle": true, "license": "MIT", "dependencies": {"safer-buffer": ">= 2.1.2 < 3"}, "engines": {"node": ">=0.10.0"}}, "node_modules/npm/node_modules/iferr": {"version": "1.0.2", "dev": true, "inBundle": true, "license": "MIT", "engines": {"node": ">=6.0.0"}}, "node_modules/npm/node_modules/ignore-walk": {"version": "3.0.3", "dev": true, "inBundle": true, "license": "ISC", "dependencies": {"minimatch": "^3.0.4"}}, "node_modules/npm/node_modules/import-lazy": {"version": "2.1.0", "dev": true, "inBundle": true, "license": "MIT", "engines": {"node": ">=4"}}, "node_modules/npm/node_modules/imurmurhash": {"version": "0.1.4", "dev": true, "inBundle": true, "license": "MIT", "engines": {"node": ">=0.8.19"}}, "node_modules/npm/node_modules/infer-owner": {"version": "1.0.4", "dev": true, "inBundle": true, "license": "ISC"}, "node_modules/npm/node_modules/inflight": {"version": "1.0.6", "dev": true, "inBundle": true, "license": "ISC", "dependencies": {"once": "^1.3.0", "wrappy": "1"}}, "node_modules/npm/node_modules/inherits": {"version": "2.0.4", "dev": true, "inBundle": true, "license": "ISC"}, "node_modules/npm/node_modules/ini": {"version": "1.3.8", "dev": true, "inBundle": true, "license": "ISC"}, "node_modules/npm/node_modules/init-package-json": {"version": "1.10.3", "dev": true, "inBundle": true, "license": "ISC", "dependencies": {"glob": "^7.1.1", "npm-package-arg": "^4.0.0 || ^5.0.0 || ^6.0.0", "promzard": "^0.3.0", "read": "~1.0.1", "read-package-json": "1 || 2", "semver": "2.x || 3.x || 4 || 5", "validate-npm-package-license": "^3.0.1", "validate-npm-package-name": "^3.0.0"}}, "node_modules/npm/node_modules/ip": {"version": "1.1.5", "dev": true, "inBundle": true, "license": "MIT"}, "node_modules/npm/node_modules/ip-regex": {"version": "2.1.0", "dev": true, "inBundle": true, "license": "MIT", "engines": {"node": ">=4"}}, "node_modules/npm/node_modules/is-callable": {"version": "1.1.4", "dev": true, "inBundle": true, "license": "MIT", "engines": {"node": ">= 0.4"}}, "node_modules/npm/node_modules/is-ci": {"version": "1.2.1", "dev": true, "inBundle": true, "license": "MIT", "dependencies": {"ci-info": "^1.5.0"}, "bin": {"is-ci": "bin.js"}}, "node_modules/npm/node_modules/is-ci/node_modules/ci-info": {"version": "1.6.0", "dev": true, "inBundle": true, "license": "MIT"}, "node_modules/npm/node_modules/is-cidr": {"version": "3.1.1", "dev": true, "inBundle": true, "license": "BSD-2-<PERSON><PERSON>", "dependencies": {"cidr-regex": "^2.0.10"}, "engines": {"node": ">=6"}}, "node_modules/npm/node_modules/is-date-object": {"version": "1.0.1", "dev": true, "inBundle": true, "license": "MIT", "engines": {"node": ">= 0.4"}}, "node_modules/npm/node_modules/is-fullwidth-code-point": {"version": "1.0.0", "dev": true, "inBundle": true, "license": "MIT", "dependencies": {"number-is-nan": "^1.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/npm/node_modules/is-installed-globally": {"version": "0.1.0", "dev": true, "inBundle": true, "license": "MIT", "dependencies": {"global-dirs": "^0.1.0", "is-path-inside": "^1.0.0"}, "engines": {"node": ">=4"}}, "node_modules/npm/node_modules/is-npm": {"version": "1.0.0", "dev": true, "inBundle": true, "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/npm/node_modules/is-obj": {"version": "1.0.1", "dev": true, "inBundle": true, "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/npm/node_modules/is-path-inside": {"version": "1.0.1", "dev": true, "inBundle": true, "license": "MIT", "dependencies": {"path-is-inside": "^1.0.1"}, "engines": {"node": ">=0.10.0"}}, "node_modules/npm/node_modules/is-redirect": {"version": "1.0.0", "dev": true, "inBundle": true, "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/npm/node_modules/is-regex": {"version": "1.0.4", "dev": true, "inBundle": true, "license": "MIT", "dependencies": {"has": "^1.0.1"}, "engines": {"node": ">= 0.4"}}, "node_modules/npm/node_modules/is-retry-allowed": {"version": "1.2.0", "dev": true, "inBundle": true, "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/npm/node_modules/is-stream": {"version": "1.1.0", "dev": true, "inBundle": true, "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/npm/node_modules/is-symbol": {"version": "1.0.2", "dev": true, "inBundle": true, "license": "MIT", "dependencies": {"has-symbols": "^1.0.0"}, "engines": {"node": ">= 0.4"}}, "node_modules/npm/node_modules/is-typedarray": {"version": "1.0.0", "dev": true, "inBundle": true, "license": "MIT"}, "node_modules/npm/node_modules/isarray": {"version": "1.0.0", "dev": true, "inBundle": true, "license": "MIT"}, "node_modules/npm/node_modules/isexe": {"version": "2.0.0", "dev": true, "inBundle": true, "license": "ISC"}, "node_modules/npm/node_modules/isstream": {"version": "0.1.2", "dev": true, "inBundle": true, "license": "MIT"}, "node_modules/npm/node_modules/jsbn": {"version": "0.1.1", "dev": true, "inBundle": true, "license": "MIT"}, "node_modules/npm/node_modules/json-parse-better-errors": {"version": "1.0.2", "dev": true, "inBundle": true, "license": "MIT"}, "node_modules/npm/node_modules/json-parse-even-better-errors": {"version": "2.3.1", "dev": true, "inBundle": true, "license": "MIT"}, "node_modules/npm/node_modules/json-schema": {"version": "0.4.0", "dev": true, "inBundle": true, "license": "(AFL-2.1 OR BSD-3-Clause)"}, "node_modules/npm/node_modules/json-stringify-safe": {"version": "5.0.1", "dev": true, "inBundle": true, "license": "ISC"}, "node_modules/npm/node_modules/jsonparse": {"version": "1.3.1", "dev": true, "engines": ["node >= 0.2.0"], "inBundle": true, "license": "MIT"}, "node_modules/npm/node_modules/JSONStream": {"version": "1.3.5", "dev": true, "inBundle": true, "license": "(MIT OR Apache-2.0)", "dependencies": {"jsonparse": "^1.2.0", "through": ">=2.2.7 <3"}, "bin": {"JSONStream": "bin.js"}, "engines": {"node": "*"}}, "node_modules/npm/node_modules/jsprim": {"version": "1.4.2", "dev": true, "inBundle": true, "license": "MIT", "dependencies": {"assert-plus": "1.0.0", "extsprintf": "1.3.0", "json-schema": "0.4.0", "verror": "1.10.0"}, "engines": {"node": ">=0.6.0"}}, "node_modules/npm/node_modules/latest-version": {"version": "3.1.0", "dev": true, "inBundle": true, "license": "MIT", "dependencies": {"package-json": "^4.0.0"}, "engines": {"node": ">=4"}}, "node_modules/npm/node_modules/lazy-property": {"version": "1.0.0", "dev": true, "inBundle": true, "license": "MIT"}, "node_modules/npm/node_modules/libcipm": {"version": "4.0.8", "dev": true, "inBundle": true, "license": "MIT", "dependencies": {"bin-links": "^1.1.2", "bluebird": "^3.5.1", "figgy-pudding": "^3.5.1", "find-npm-prefix": "^1.0.2", "graceful-fs": "^4.1.11", "ini": "^1.3.5", "lock-verify": "^2.1.0", "mkdirp": "^0.5.1", "npm-lifecycle": "^3.0.0", "npm-logical-tree": "^1.2.1", "npm-package-arg": "^6.1.0", "pacote": "^9.1.0", "read-package-json": "^2.0.13", "rimraf": "^2.6.2", "worker-farm": "^1.6.0"}}, "node_modules/npm/node_modules/libnpm": {"version": "3.0.1", "dev": true, "inBundle": true, "license": "ISC", "dependencies": {"bin-links": "^1.1.2", "bluebird": "^3.5.3", "find-npm-prefix": "^1.0.2", "libnpmaccess": "^3.0.2", "libnpmconfig": "^1.2.1", "libnpmhook": "^5.0.3", "libnpmorg": "^1.0.1", "libnpmpublish": "^1.1.2", "libnpmsearch": "^2.0.2", "libnpmteam": "^1.0.2", "lock-verify": "^2.0.2", "npm-lifecycle": "^3.0.0", "npm-logical-tree": "^1.2.1", "npm-package-arg": "^6.1.0", "npm-profile": "^4.0.2", "npm-registry-fetch": "^4.0.0", "npmlog": "^4.1.2", "pacote": "^9.5.3", "read-package-json": "^2.0.13", "stringify-package": "^1.0.0"}}, "node_modules/npm/node_modules/libnpmaccess": {"version": "3.0.2", "dev": true, "inBundle": true, "license": "ISC", "dependencies": {"aproba": "^2.0.0", "get-stream": "^4.0.0", "npm-package-arg": "^6.1.0", "npm-registry-fetch": "^4.0.0"}}, "node_modules/npm/node_modules/libnpmconfig": {"version": "1.2.1", "dev": true, "inBundle": true, "license": "ISC", "dependencies": {"figgy-pudding": "^3.5.1", "find-up": "^3.0.0", "ini": "^1.3.5"}}, "node_modules/npm/node_modules/libnpmconfig/node_modules/find-up": {"version": "3.0.0", "dev": true, "inBundle": true, "license": "MIT", "dependencies": {"locate-path": "^3.0.0"}, "engines": {"node": ">=6"}}, "node_modules/npm/node_modules/libnpmconfig/node_modules/locate-path": {"version": "3.0.0", "dev": true, "inBundle": true, "license": "MIT", "dependencies": {"p-locate": "^3.0.0", "path-exists": "^3.0.0"}, "engines": {"node": ">=6"}}, "node_modules/npm/node_modules/libnpmconfig/node_modules/p-limit": {"version": "2.2.0", "dev": true, "inBundle": true, "license": "MIT", "dependencies": {"p-try": "^2.0.0"}, "engines": {"node": ">=6"}}, "node_modules/npm/node_modules/libnpmconfig/node_modules/p-locate": {"version": "3.0.0", "dev": true, "inBundle": true, "license": "MIT", "dependencies": {"p-limit": "^2.0.0"}, "engines": {"node": ">=6"}}, "node_modules/npm/node_modules/libnpmconfig/node_modules/p-try": {"version": "2.2.0", "dev": true, "inBundle": true, "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/npm/node_modules/libnpmhook": {"version": "5.0.3", "dev": true, "inBundle": true, "license": "ISC", "dependencies": {"aproba": "^2.0.0", "figgy-pudding": "^3.4.1", "get-stream": "^4.0.0", "npm-registry-fetch": "^4.0.0"}}, "node_modules/npm/node_modules/libnpmorg": {"version": "1.0.1", "dev": true, "inBundle": true, "license": "ISC", "dependencies": {"aproba": "^2.0.0", "figgy-pudding": "^3.4.1", "get-stream": "^4.0.0", "npm-registry-fetch": "^4.0.0"}}, "node_modules/npm/node_modules/libnpmpublish": {"version": "1.1.2", "dev": true, "inBundle": true, "license": "ISC", "dependencies": {"aproba": "^2.0.0", "figgy-pudding": "^3.5.1", "get-stream": "^4.0.0", "lodash.clonedeep": "^4.5.0", "normalize-package-data": "^2.4.0", "npm-package-arg": "^6.1.0", "npm-registry-fetch": "^4.0.0", "semver": "^5.5.1", "ssri": "^6.0.1"}}, "node_modules/npm/node_modules/libnpmsearch": {"version": "2.0.2", "dev": true, "inBundle": true, "license": "ISC", "dependencies": {"figgy-pudding": "^3.5.1", "get-stream": "^4.0.0", "npm-registry-fetch": "^4.0.0"}}, "node_modules/npm/node_modules/libnpmteam": {"version": "1.0.2", "dev": true, "inBundle": true, "license": "ISC", "dependencies": {"aproba": "^2.0.0", "figgy-pudding": "^3.4.1", "get-stream": "^4.0.0", "npm-registry-fetch": "^4.0.0"}}, "node_modules/npm/node_modules/libnpx": {"version": "10.2.4", "dev": true, "inBundle": true, "license": "ISC", "dependencies": {"dotenv": "^5.0.1", "npm-package-arg": "^6.0.0", "rimraf": "^2.6.2", "safe-buffer": "^5.1.0", "update-notifier": "^2.3.0", "which": "^1.3.0", "y18n": "^4.0.0", "yargs": "^14.2.3"}, "engines": {"node": ">=4"}}, "node_modules/npm/node_modules/lock-verify": {"version": "2.2.2", "dev": true, "inBundle": true, "license": "ISC", "dependencies": {"@iarna/cli": "^2.1.0", "npm-package-arg": "^6.1.0", "semver": "^5.4.1"}, "bin": {"lock-verify": "cli.js"}}, "node_modules/npm/node_modules/lockfile": {"version": "1.0.4", "dev": true, "inBundle": true, "license": "ISC", "dependencies": {"signal-exit": "^3.0.2"}}, "node_modules/npm/node_modules/lodash._baseindexof": {"version": "3.1.0", "extraneous": true, "inBundle": true, "license": "MIT"}, "node_modules/npm/node_modules/lodash._baseuniq": {"version": "4.6.0", "dev": true, "inBundle": true, "license": "MIT", "dependencies": {"lodash._createset": "~4.0.0", "lodash._root": "~3.0.0"}}, "node_modules/npm/node_modules/lodash._bindcallback": {"version": "3.0.1", "extraneous": true, "inBundle": true, "license": "MIT"}, "node_modules/npm/node_modules/lodash._cacheindexof": {"version": "3.0.2", "extraneous": true, "inBundle": true, "license": "MIT"}, "node_modules/npm/node_modules/lodash._createcache": {"version": "3.1.2", "extraneous": true, "inBundle": true, "license": "MIT", "dependencies": {"lodash._getnative": "^3.0.0"}}, "node_modules/npm/node_modules/lodash._createset": {"version": "4.0.3", "dev": true, "inBundle": true, "license": "MIT"}, "node_modules/npm/node_modules/lodash._getnative": {"version": "3.9.1", "extraneous": true, "inBundle": true, "license": "MIT"}, "node_modules/npm/node_modules/lodash._root": {"version": "3.0.1", "dev": true, "inBundle": true, "license": "MIT"}, "node_modules/npm/node_modules/lodash.clonedeep": {"version": "4.5.0", "dev": true, "inBundle": true, "license": "MIT"}, "node_modules/npm/node_modules/lodash.restparam": {"version": "3.6.1", "extraneous": true, "inBundle": true, "license": "MIT"}, "node_modules/npm/node_modules/lodash.union": {"version": "4.6.0", "dev": true, "inBundle": true, "license": "MIT"}, "node_modules/npm/node_modules/lodash.uniq": {"version": "4.5.0", "dev": true, "inBundle": true, "license": "MIT"}, "node_modules/npm/node_modules/lodash.without": {"version": "4.4.0", "dev": true, "inBundle": true, "license": "MIT"}, "node_modules/npm/node_modules/lowercase-keys": {"version": "1.0.1", "dev": true, "inBundle": true, "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/npm/node_modules/lru-cache": {"version": "5.1.1", "dev": true, "inBundle": true, "license": "ISC", "dependencies": {"yallist": "^3.0.2"}}, "node_modules/npm/node_modules/make-dir": {"version": "1.3.0", "dev": true, "inBundle": true, "license": "MIT", "dependencies": {"pify": "^3.0.0"}, "engines": {"node": ">=4"}}, "node_modules/npm/node_modules/make-fetch-happen": {"version": "5.0.2", "dev": true, "inBundle": true, "license": "ISC", "dependencies": {"agentkeepalive": "^3.4.1", "cacache": "^12.0.0", "http-cache-semantics": "^3.8.1", "http-proxy-agent": "^2.1.0", "https-proxy-agent": "^2.2.3", "lru-cache": "^5.1.1", "mississippi": "^3.0.0", "node-fetch-npm": "^2.0.2", "promise-retry": "^1.1.1", "socks-proxy-agent": "^4.0.0", "ssri": "^6.0.0"}}, "node_modules/npm/node_modules/meant": {"version": "1.0.3", "dev": true, "inBundle": true, "license": "MIT"}, "node_modules/npm/node_modules/mime-db": {"version": "1.35.0", "dev": true, "inBundle": true, "license": "MIT", "engines": {"node": ">= 0.6"}}, "node_modules/npm/node_modules/mime-types": {"version": "2.1.19", "dev": true, "inBundle": true, "license": "MIT", "dependencies": {"mime-db": "~1.35.0"}, "engines": {"node": ">= 0.6"}}, "node_modules/npm/node_modules/minimatch": {"version": "3.1.2", "dev": true, "inBundle": true, "license": "ISC", "dependencies": {"brace-expansion": "^1.1.7"}, "engines": {"node": "*"}}, "node_modules/npm/node_modules/minimist": {"version": "1.2.6", "dev": true, "inBundle": true, "license": "MIT"}, "node_modules/npm/node_modules/minizlib": {"version": "1.3.3", "dev": true, "inBundle": true, "license": "MIT", "dependencies": {"minipass": "^2.9.0"}}, "node_modules/npm/node_modules/minizlib/node_modules/minipass": {"version": "2.9.0", "dev": true, "inBundle": true, "license": "ISC", "dependencies": {"safe-buffer": "^5.1.2", "yallist": "^3.0.0"}}, "node_modules/npm/node_modules/mississippi": {"version": "3.0.0", "dev": true, "inBundle": true, "license": "BSD-2-<PERSON><PERSON>", "dependencies": {"concat-stream": "^1.5.0", "duplexify": "^3.4.2", "end-of-stream": "^1.1.0", "flush-write-stream": "^1.0.0", "from2": "^2.1.0", "parallel-transform": "^1.1.0", "pump": "^3.0.0", "pumpify": "^1.3.3", "stream-each": "^1.1.0", "through2": "^2.0.0"}, "engines": {"node": ">=4.0.0"}}, "node_modules/npm/node_modules/mkdirp": {"version": "0.5.6", "dev": true, "inBundle": true, "license": "MIT", "dependencies": {"minimist": "^1.2.6"}, "bin": {"mkdirp": "bin/cmd.js"}}, "node_modules/npm/node_modules/move-concurrently": {"version": "1.0.1", "dev": true, "inBundle": true, "license": "ISC", "dependencies": {"aproba": "^1.1.1", "copy-concurrently": "^1.0.0", "fs-write-stream-atomic": "^1.0.8", "mkdirp": "^0.5.1", "rimraf": "^2.5.4", "run-queue": "^1.0.3"}}, "node_modules/npm/node_modules/move-concurrently/node_modules/aproba": {"version": "1.2.0", "dev": true, "inBundle": true, "license": "ISC"}, "node_modules/npm/node_modules/ms": {"version": "2.1.1", "dev": true, "inBundle": true, "license": "MIT"}, "node_modules/npm/node_modules/mute-stream": {"version": "0.0.7", "dev": true, "inBundle": true, "license": "ISC"}, "node_modules/npm/node_modules/node-fetch-npm": {"version": "2.0.2", "dev": true, "inBundle": true, "license": "MIT", "dependencies": {"encoding": "^0.1.11", "json-parse-better-errors": "^1.0.0", "safe-buffer": "^5.1.1"}, "engines": {"node": ">=4"}}, "node_modules/npm/node_modules/node-gyp": {"version": "5.1.1", "dev": true, "inBundle": true, "license": "MIT", "dependencies": {"env-paths": "^2.2.0", "glob": "^7.1.4", "graceful-fs": "^4.2.2", "mkdirp": "^0.5.1", "nopt": "^4.0.1", "npmlog": "^4.1.2", "request": "^2.88.0", "rimraf": "^2.6.3", "semver": "^5.7.1", "tar": "^4.4.12", "which": "^1.3.1"}, "bin": {"node-gyp": "bin/node-gyp.js"}, "engines": {"node": ">= 6.0.0"}}, "node_modules/npm/node_modules/nopt": {"version": "4.0.3", "dev": true, "inBundle": true, "license": "ISC", "dependencies": {"abbrev": "1", "osenv": "^0.1.4"}, "bin": {"nopt": "bin/nopt.js"}}, "node_modules/npm/node_modules/normalize-package-data": {"version": "2.5.0", "dev": true, "inBundle": true, "license": "BSD-2-<PERSON><PERSON>", "dependencies": {"hosted-git-info": "^2.1.4", "resolve": "^1.10.0", "semver": "2 || 3 || 4 || 5", "validate-npm-package-license": "^3.0.1"}}, "node_modules/npm/node_modules/normalize-package-data/node_modules/resolve": {"version": "1.10.0", "dev": true, "inBundle": true, "license": "MIT", "dependencies": {"path-parse": "^1.0.6"}}, "node_modules/npm/node_modules/npm-audit-report": {"version": "1.3.3", "dev": true, "inBundle": true, "license": "ISC", "dependencies": {"cli-table3": "^0.5.0", "console-control-strings": "^1.1.0"}}, "node_modules/npm/node_modules/npm-bundled": {"version": "1.1.1", "dev": true, "inBundle": true, "license": "ISC", "dependencies": {"npm-normalize-package-bin": "^1.0.1"}}, "node_modules/npm/node_modules/npm-cache-filename": {"version": "1.0.2", "dev": true, "inBundle": true, "license": "ISC"}, "node_modules/npm/node_modules/npm-install-checks": {"version": "3.0.2", "dev": true, "inBundle": true, "license": "BSD-2-<PERSON><PERSON>", "dependencies": {"semver": "^2.3.0 || 3.x || 4 || 5"}}, "node_modules/npm/node_modules/npm-lifecycle": {"version": "3.1.5", "dev": true, "inBundle": true, "license": "Artistic-2.0", "dependencies": {"byline": "^5.0.0", "graceful-fs": "^4.1.15", "node-gyp": "^5.0.2", "resolve-from": "^4.0.0", "slide": "^1.1.6", "uid-number": "0.0.6", "umask": "^1.1.0", "which": "^1.3.1"}}, "node_modules/npm/node_modules/npm-logical-tree": {"version": "1.2.1", "dev": true, "inBundle": true, "license": "ISC"}, "node_modules/npm/node_modules/npm-normalize-package-bin": {"version": "1.0.1", "dev": true, "inBundle": true, "license": "ISC"}, "node_modules/npm/node_modules/npm-package-arg": {"version": "6.1.1", "dev": true, "inBundle": true, "license": "ISC", "dependencies": {"hosted-git-info": "^2.7.1", "osenv": "^0.1.5", "semver": "^5.6.0", "validate-npm-package-name": "^3.0.0"}}, "node_modules/npm/node_modules/npm-packlist": {"version": "1.4.8", "dev": true, "inBundle": true, "license": "ISC", "dependencies": {"ignore-walk": "^3.0.1", "npm-bundled": "^1.0.1", "npm-normalize-package-bin": "^1.0.1"}}, "node_modules/npm/node_modules/npm-pick-manifest": {"version": "3.0.2", "dev": true, "inBundle": true, "license": "ISC", "dependencies": {"figgy-pudding": "^3.5.1", "npm-package-arg": "^6.0.0", "semver": "^5.4.1"}}, "node_modules/npm/node_modules/npm-profile": {"version": "4.0.4", "dev": true, "inBundle": true, "license": "ISC", "dependencies": {"aproba": "^1.1.2 || 2", "figgy-pudding": "^3.4.1", "npm-registry-fetch": "^4.0.0"}}, "node_modules/npm/node_modules/npm-registry-fetch": {"version": "4.0.7", "dev": true, "inBundle": true, "license": "ISC", "dependencies": {"bluebird": "^3.5.1", "figgy-pudding": "^3.4.1", "JSONStream": "^1.3.4", "lru-cache": "^5.1.1", "make-fetch-happen": "^5.0.0", "npm-package-arg": "^6.1.0", "safe-buffer": "^5.2.0"}}, "node_modules/npm/node_modules/npm-registry-fetch/node_modules/safe-buffer": {"version": "5.2.1", "dev": true, "funding": [{"type": "github", "url": "https://github.com/sponsors/feross"}, {"type": "patreon", "url": "https://www.patreon.com/feross"}, {"type": "consulting", "url": "https://feross.org/support"}], "inBundle": true, "license": "MIT"}, "node_modules/npm/node_modules/npm-run-path": {"version": "2.0.2", "dev": true, "inBundle": true, "license": "MIT", "dependencies": {"path-key": "^2.0.0"}, "engines": {"node": ">=4"}}, "node_modules/npm/node_modules/npm-user-validate": {"version": "1.0.1", "dev": true, "inBundle": true, "license": "BSD-2-<PERSON><PERSON>"}, "node_modules/npm/node_modules/npmlog": {"version": "4.1.2", "dev": true, "inBundle": true, "license": "ISC", "dependencies": {"are-we-there-yet": "~1.1.2", "console-control-strings": "~1.1.0", "gauge": "~2.7.3", "set-blocking": "~2.0.0"}}, "node_modules/npm/node_modules/number-is-nan": {"version": "1.0.1", "dev": true, "inBundle": true, "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/npm/node_modules/oauth-sign": {"version": "0.9.0", "dev": true, "inBundle": true, "license": "Apache-2.0", "engines": {"node": "*"}}, "node_modules/npm/node_modules/object-assign": {"version": "4.1.1", "dev": true, "inBundle": true, "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/npm/node_modules/object-keys": {"version": "1.0.12", "dev": true, "inBundle": true, "license": "MIT", "engines": {"node": ">= 0.4"}}, "node_modules/npm/node_modules/object.getownpropertydescriptors": {"version": "2.0.3", "dev": true, "inBundle": true, "license": "MIT", "dependencies": {"define-properties": "^1.1.2", "es-abstract": "^1.5.1"}, "engines": {"node": ">= 0.8"}}, "node_modules/npm/node_modules/once": {"version": "1.4.0", "dev": true, "inBundle": true, "license": "ISC", "dependencies": {"wrappy": "1"}}, "node_modules/npm/node_modules/opener": {"version": "1.5.2", "dev": true, "inBundle": true, "license": "(WTFPL OR MIT)", "bin": {"opener": "bin/opener-bin.js"}}, "node_modules/npm/node_modules/os-homedir": {"version": "1.0.2", "dev": true, "inBundle": true, "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/npm/node_modules/os-tmpdir": {"version": "1.0.2", "dev": true, "inBundle": true, "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/npm/node_modules/osenv": {"version": "0.1.5", "dev": true, "inBundle": true, "license": "ISC", "dependencies": {"os-homedir": "^1.0.0", "os-tmpdir": "^1.0.0"}}, "node_modules/npm/node_modules/p-finally": {"version": "1.0.0", "dev": true, "inBundle": true, "license": "MIT", "engines": {"node": ">=4"}}, "node_modules/npm/node_modules/package-json": {"version": "4.0.1", "dev": true, "inBundle": true, "license": "MIT", "dependencies": {"got": "^6.7.1", "registry-auth-token": "^3.0.1", "registry-url": "^3.0.3", "semver": "^5.1.0"}, "engines": {"node": ">=4"}}, "node_modules/npm/node_modules/pacote": {"version": "9.5.12", "dev": true, "inBundle": true, "license": "MIT", "dependencies": {"bluebird": "^3.5.3", "cacache": "^12.0.2", "chownr": "^1.1.2", "figgy-pudding": "^3.5.1", "get-stream": "^4.1.0", "glob": "^7.1.3", "infer-owner": "^1.0.4", "lru-cache": "^5.1.1", "make-fetch-happen": "^5.0.0", "minimatch": "^3.0.4", "minipass": "^2.3.5", "mississippi": "^3.0.0", "mkdirp": "^0.5.1", "normalize-package-data": "^2.4.0", "npm-normalize-package-bin": "^1.0.0", "npm-package-arg": "^6.1.0", "npm-packlist": "^1.1.12", "npm-pick-manifest": "^3.0.0", "npm-registry-fetch": "^4.0.0", "osenv": "^0.1.5", "promise-inflight": "^1.0.1", "promise-retry": "^1.1.1", "protoduck": "^5.0.1", "rimraf": "^2.6.2", "safe-buffer": "^5.1.2", "semver": "^5.6.0", "ssri": "^6.0.1", "tar": "^4.4.10", "unique-filename": "^1.1.1", "which": "^1.3.1"}}, "node_modules/npm/node_modules/pacote/node_modules/minipass": {"version": "2.9.0", "dev": true, "inBundle": true, "license": "ISC", "dependencies": {"safe-buffer": "^5.1.2", "yallist": "^3.0.0"}}, "node_modules/npm/node_modules/parallel-transform": {"version": "1.1.0", "dev": true, "inBundle": true, "license": "MIT", "dependencies": {"cyclist": "~0.2.2", "inherits": "^2.0.3", "readable-stream": "^2.1.5"}}, "node_modules/npm/node_modules/parallel-transform/node_modules/readable-stream": {"version": "2.3.6", "dev": true, "inBundle": true, "license": "MIT", "dependencies": {"core-util-is": "~1.0.0", "inherits": "~2.0.3", "isarray": "~1.0.0", "process-nextick-args": "~2.0.0", "safe-buffer": "~5.1.1", "string_decoder": "~1.1.1", "util-deprecate": "~1.0.1"}}, "node_modules/npm/node_modules/parallel-transform/node_modules/readable-stream/node_modules/safe-buffer": {"version": "5.1.2", "dev": true, "inBundle": true, "license": "MIT"}, "node_modules/npm/node_modules/parallel-transform/node_modules/string_decoder": {"version": "1.1.1", "dev": true, "inBundle": true, "license": "MIT", "dependencies": {"safe-buffer": "~5.1.0"}}, "node_modules/npm/node_modules/parallel-transform/node_modules/string_decoder/node_modules/safe-buffer": {"version": "5.1.2", "dev": true, "inBundle": true, "license": "MIT"}, "node_modules/npm/node_modules/path-exists": {"version": "3.0.0", "dev": true, "inBundle": true, "license": "MIT", "engines": {"node": ">=4"}}, "node_modules/npm/node_modules/path-is-absolute": {"version": "1.0.1", "dev": true, "inBundle": true, "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/npm/node_modules/path-is-inside": {"version": "1.0.2", "dev": true, "inBundle": true, "license": "(WTFPL OR MIT)"}, "node_modules/npm/node_modules/path-key": {"version": "2.0.1", "dev": true, "inBundle": true, "license": "MIT", "engines": {"node": ">=4"}}, "node_modules/npm/node_modules/path-parse": {"version": "1.0.7", "dev": true, "inBundle": true, "license": "MIT"}, "node_modules/npm/node_modules/performance-now": {"version": "2.1.0", "dev": true, "inBundle": true, "license": "MIT"}, "node_modules/npm/node_modules/pify": {"version": "3.0.0", "dev": true, "inBundle": true, "license": "MIT", "engines": {"node": ">=4"}}, "node_modules/npm/node_modules/prepend-http": {"version": "1.0.4", "dev": true, "inBundle": true, "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/npm/node_modules/process-nextick-args": {"version": "2.0.0", "dev": true, "inBundle": true, "license": "MIT"}, "node_modules/npm/node_modules/promise-inflight": {"version": "1.0.1", "dev": true, "inBundle": true, "license": "ISC"}, "node_modules/npm/node_modules/promise-retry": {"version": "1.1.1", "dev": true, "inBundle": true, "license": "MIT", "dependencies": {"err-code": "^1.0.0", "retry": "^0.10.0"}, "engines": {"node": ">=0.12"}}, "node_modules/npm/node_modules/promise-retry/node_modules/retry": {"version": "0.10.1", "dev": true, "inBundle": true, "license": "MIT", "engines": {"node": "*"}}, "node_modules/npm/node_modules/promzard": {"version": "0.3.0", "dev": true, "inBundle": true, "license": "ISC", "dependencies": {"read": "1"}}, "node_modules/npm/node_modules/proto-list": {"version": "1.2.4", "dev": true, "inBundle": true, "license": "ISC"}, "node_modules/npm/node_modules/protoduck": {"version": "5.0.1", "dev": true, "inBundle": true, "license": "MIT", "dependencies": {"genfun": "^5.0.0"}}, "node_modules/npm/node_modules/prr": {"version": "1.0.1", "dev": true, "inBundle": true, "license": "MIT"}, "node_modules/npm/node_modules/pseudomap": {"version": "1.0.2", "dev": true, "inBundle": true, "license": "ISC"}, "node_modules/npm/node_modules/psl": {"version": "1.9.0", "dev": true, "inBundle": true, "license": "MIT"}, "node_modules/npm/node_modules/pump": {"version": "3.0.0", "dev": true, "inBundle": true, "license": "MIT", "dependencies": {"end-of-stream": "^1.1.0", "once": "^1.3.1"}}, "node_modules/npm/node_modules/pumpify": {"version": "1.5.1", "dev": true, "inBundle": true, "license": "MIT", "dependencies": {"duplexify": "^3.6.0", "inherits": "^2.0.3", "pump": "^2.0.0"}}, "node_modules/npm/node_modules/pumpify/node_modules/pump": {"version": "2.0.1", "dev": true, "inBundle": true, "license": "MIT", "dependencies": {"end-of-stream": "^1.1.0", "once": "^1.3.1"}}, "node_modules/npm/node_modules/qrcode-terminal": {"version": "0.12.0", "dev": true, "inBundle": true, "bin": {"qrcode-terminal": "bin/qrcode-terminal.js"}}, "node_modules/npm/node_modules/qs": {"version": "6.5.3", "dev": true, "inBundle": true, "license": "BSD-3-<PERSON><PERSON>", "engines": {"node": ">=0.6"}}, "node_modules/npm/node_modules/query-string": {"version": "6.14.1", "dev": true, "inBundle": true, "license": "MIT", "dependencies": {"decode-uri-component": "^0.2.0", "filter-obj": "^1.1.0", "split-on-first": "^1.0.0", "strict-uri-encode": "^2.0.0"}, "engines": {"node": ">=6"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/npm/node_modules/qw": {"version": "1.0.2", "dev": true, "inBundle": true, "license": "ISC"}, "node_modules/npm/node_modules/rc": {"version": "1.2.8", "dev": true, "inBundle": true, "license": "(BSD-2-<PERSON><PERSON> OR MIT OR Apache-2.0)", "dependencies": {"deep-extend": "^0.6.0", "ini": "~1.3.0", "minimist": "^1.2.0", "strip-json-comments": "~2.0.1"}, "bin": {"rc": "cli.js"}}, "node_modules/npm/node_modules/read": {"version": "1.0.7", "dev": true, "inBundle": true, "license": "ISC", "dependencies": {"mute-stream": "~0.0.4"}, "engines": {"node": ">=0.8"}}, "node_modules/npm/node_modules/read-cmd-shim": {"version": "1.0.5", "dev": true, "inBundle": true, "license": "ISC", "dependencies": {"graceful-fs": "^4.1.2"}}, "node_modules/npm/node_modules/read-installed": {"version": "4.0.3", "dev": true, "inBundle": true, "license": "ISC", "dependencies": {"debuglog": "^1.0.1", "read-package-json": "^2.0.0", "readdir-scoped-modules": "^1.0.0", "semver": "2 || 3 || 4 || 5", "slide": "~1.1.3", "util-extend": "^1.0.1"}, "optionalDependencies": {"graceful-fs": "^4.1.2"}}, "node_modules/npm/node_modules/read-package-json": {"version": "2.1.2", "dev": true, "inBundle": true, "license": "ISC", "dependencies": {"glob": "^7.1.1", "json-parse-even-better-errors": "^2.3.0", "normalize-package-data": "^2.0.0", "npm-normalize-package-bin": "^1.0.0"}}, "node_modules/npm/node_modules/read-package-tree": {"version": "5.3.1", "dev": true, "inBundle": true, "license": "ISC", "dependencies": {"read-package-json": "^2.0.0", "readdir-scoped-modules": "^1.0.0", "util-promisify": "^2.1.0"}}, "node_modules/npm/node_modules/readable-stream": {"version": "3.6.0", "dev": true, "inBundle": true, "license": "MIT", "dependencies": {"inherits": "^2.0.3", "string_decoder": "^1.1.1", "util-deprecate": "^1.0.1"}, "engines": {"node": ">= 6"}}, "node_modules/npm/node_modules/readdir-scoped-modules": {"version": "1.1.0", "dev": true, "inBundle": true, "license": "ISC", "dependencies": {"debuglog": "^1.0.1", "dezalgo": "^1.0.0", "graceful-fs": "^4.1.2", "once": "^1.3.0"}}, "node_modules/npm/node_modules/registry-auth-token": {"version": "3.4.0", "dev": true, "inBundle": true, "license": "MIT", "dependencies": {"rc": "^1.1.6", "safe-buffer": "^5.0.1"}}, "node_modules/npm/node_modules/registry-url": {"version": "3.1.0", "dev": true, "inBundle": true, "license": "MIT", "dependencies": {"rc": "^1.0.1"}, "engines": {"node": ">=0.10.0"}}, "node_modules/npm/node_modules/request": {"version": "2.88.2", "deprecated": "request has been deprecated, see https://github.com/request/request/issues/3142", "dev": true, "inBundle": true, "license": "Apache-2.0", "dependencies": {"aws-sign2": "~0.7.0", "aws4": "^1.8.0", "caseless": "~0.12.0", "combined-stream": "~1.0.6", "extend": "~3.0.2", "forever-agent": "~0.6.1", "form-data": "~2.3.2", "har-validator": "~5.1.3", "http-signature": "~1.2.0", "is-typedarray": "~1.0.0", "isstream": "~0.1.2", "json-stringify-safe": "~5.0.1", "mime-types": "~2.1.19", "oauth-sign": "~0.9.0", "performance-now": "^2.1.0", "qs": "~6.5.2", "safe-buffer": "^5.1.2", "tough-cookie": "~2.5.0", "tunnel-agent": "^0.6.0", "uuid": "^3.3.2"}, "engines": {"node": ">= 6"}}, "node_modules/npm/node_modules/require-directory": {"version": "2.1.1", "dev": true, "inBundle": true, "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/npm/node_modules/require-main-filename": {"version": "2.0.0", "dev": true, "inBundle": true, "license": "ISC"}, "node_modules/npm/node_modules/resolve-from": {"version": "4.0.0", "dev": true, "inBundle": true, "license": "MIT", "engines": {"node": ">=4"}}, "node_modules/npm/node_modules/retry": {"version": "0.12.0", "dev": true, "inBundle": true, "license": "MIT", "engines": {"node": ">= 4"}}, "node_modules/npm/node_modules/rimraf": {"version": "2.7.1", "dev": true, "inBundle": true, "license": "ISC", "dependencies": {"glob": "^7.1.3"}, "bin": {"rimraf": "bin.js"}}, "node_modules/npm/node_modules/run-queue": {"version": "1.0.3", "dev": true, "inBundle": true, "license": "ISC", "dependencies": {"aproba": "^1.1.1"}}, "node_modules/npm/node_modules/run-queue/node_modules/aproba": {"version": "1.2.0", "dev": true, "inBundle": true, "license": "ISC"}, "node_modules/npm/node_modules/safe-buffer": {"version": "5.2.1", "dev": true, "funding": [{"type": "github", "url": "https://github.com/sponsors/feross"}, {"type": "patreon", "url": "https://www.patreon.com/feross"}, {"type": "consulting", "url": "https://feross.org/support"}], "inBundle": true, "license": "MIT"}, "node_modules/npm/node_modules/safer-buffer": {"version": "2.1.2", "dev": true, "inBundle": true, "license": "MIT"}, "node_modules/npm/node_modules/semver": {"version": "5.7.1", "dev": true, "inBundle": true, "license": "ISC", "bin": {"semver": "bin/semver"}}, "node_modules/npm/node_modules/semver-diff": {"version": "2.1.0", "dev": true, "inBundle": true, "license": "MIT", "dependencies": {"semver": "^5.0.3"}, "engines": {"node": ">=0.10.0"}}, "node_modules/npm/node_modules/set-blocking": {"version": "2.0.0", "dev": true, "inBundle": true, "license": "ISC"}, "node_modules/npm/node_modules/sha": {"version": "3.0.0", "dev": true, "inBundle": true, "license": "(BSD-2-<PERSON><PERSON> OR MIT)", "dependencies": {"graceful-fs": "^4.1.2"}}, "node_modules/npm/node_modules/shebang-command": {"version": "1.2.0", "dev": true, "inBundle": true, "license": "MIT", "dependencies": {"shebang-regex": "^1.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/npm/node_modules/shebang-regex": {"version": "1.0.0", "dev": true, "inBundle": true, "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/npm/node_modules/signal-exit": {"version": "3.0.2", "dev": true, "inBundle": true, "license": "ISC"}, "node_modules/npm/node_modules/slide": {"version": "1.1.6", "dev": true, "inBundle": true, "license": "ISC", "engines": {"node": "*"}}, "node_modules/npm/node_modules/smart-buffer": {"version": "4.1.0", "dev": true, "inBundle": true, "license": "MIT", "engines": {"node": ">= 6.0.0", "npm": ">= 3.0.0"}}, "node_modules/npm/node_modules/socks": {"version": "2.3.3", "dev": true, "inBundle": true, "license": "MIT", "dependencies": {"ip": "1.1.5", "smart-buffer": "^4.1.0"}, "engines": {"node": ">= 6.0.0", "npm": ">= 3.0.0"}}, "node_modules/npm/node_modules/socks-proxy-agent": {"version": "4.0.2", "dev": true, "inBundle": true, "license": "MIT", "dependencies": {"agent-base": "~4.2.1", "socks": "~2.3.2"}, "engines": {"node": ">= 6"}}, "node_modules/npm/node_modules/socks-proxy-agent/node_modules/agent-base": {"version": "4.2.1", "dev": true, "inBundle": true, "license": "MIT", "dependencies": {"es6-promisify": "^5.0.0"}, "engines": {"node": ">= 4.0.0"}}, "node_modules/npm/node_modules/sorted-object": {"version": "2.0.1", "dev": true, "inBundle": true, "license": "(WTFPL OR MIT)"}, "node_modules/npm/node_modules/sorted-union-stream": {"version": "2.1.3", "dev": true, "inBundle": true, "license": "MIT", "dependencies": {"from2": "^1.3.0", "stream-iterate": "^1.1.0"}}, "node_modules/npm/node_modules/sorted-union-stream/node_modules/from2": {"version": "1.3.0", "dev": true, "inBundle": true, "license": "MIT", "dependencies": {"inherits": "~2.0.1", "readable-stream": "~1.1.10"}}, "node_modules/npm/node_modules/sorted-union-stream/node_modules/isarray": {"version": "0.0.1", "dev": true, "inBundle": true, "license": "MIT"}, "node_modules/npm/node_modules/sorted-union-stream/node_modules/readable-stream": {"version": "1.1.14", "dev": true, "inBundle": true, "license": "MIT", "dependencies": {"core-util-is": "~1.0.0", "inherits": "~2.0.1", "isarray": "0.0.1", "string_decoder": "~0.10.x"}}, "node_modules/npm/node_modules/sorted-union-stream/node_modules/string_decoder": {"version": "0.10.31", "dev": true, "inBundle": true, "license": "MIT"}, "node_modules/npm/node_modules/spdx-correct": {"version": "3.0.0", "dev": true, "inBundle": true, "license": "Apache-2.0", "dependencies": {"spdx-expression-parse": "^3.0.0", "spdx-license-ids": "^3.0.0"}}, "node_modules/npm/node_modules/spdx-exceptions": {"version": "2.1.0", "dev": true, "inBundle": true, "license": "CC-BY-3.0"}, "node_modules/npm/node_modules/spdx-expression-parse": {"version": "3.0.0", "dev": true, "inBundle": true, "license": "MIT", "dependencies": {"spdx-exceptions": "^2.1.0", "spdx-license-ids": "^3.0.0"}}, "node_modules/npm/node_modules/spdx-license-ids": {"version": "3.0.5", "dev": true, "inBundle": true, "license": "CC0-1.0"}, "node_modules/npm/node_modules/split-on-first": {"version": "1.1.0", "dev": true, "inBundle": true, "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/npm/node_modules/sshpk": {"version": "1.17.0", "dev": true, "inBundle": true, "license": "MIT", "dependencies": {"asn1": "~0.2.3", "assert-plus": "^1.0.0", "bcrypt-pbkdf": "^1.0.0", "dashdash": "^1.12.0", "ecc-jsbn": "~0.1.1", "getpass": "^0.1.1", "jsbn": "~0.1.0", "safer-buffer": "^2.0.2", "tweetnacl": "~0.14.0"}, "bin": {"sshpk-conv": "bin/sshpk-conv", "sshpk-sign": "bin/sshpk-sign", "sshpk-verify": "bin/sshpk-verify"}, "engines": {"node": ">=0.10.0"}}, "node_modules/npm/node_modules/ssri": {"version": "6.0.2", "dev": true, "inBundle": true, "license": "ISC", "dependencies": {"figgy-pudding": "^3.5.1"}}, "node_modules/npm/node_modules/stream-each": {"version": "1.2.2", "dev": true, "inBundle": true, "license": "MIT", "dependencies": {"end-of-stream": "^1.1.0", "stream-shift": "^1.0.0"}}, "node_modules/npm/node_modules/stream-iterate": {"version": "1.2.0", "dev": true, "inBundle": true, "license": "MIT", "dependencies": {"readable-stream": "^2.1.5", "stream-shift": "^1.0.0"}}, "node_modules/npm/node_modules/stream-iterate/node_modules/readable-stream": {"version": "2.3.6", "dev": true, "inBundle": true, "license": "MIT", "dependencies": {"core-util-is": "~1.0.0", "inherits": "~2.0.3", "isarray": "~1.0.0", "process-nextick-args": "~2.0.0", "safe-buffer": "~5.1.1", "string_decoder": "~1.1.1", "util-deprecate": "~1.0.1"}}, "node_modules/npm/node_modules/stream-iterate/node_modules/readable-stream/node_modules/safe-buffer": {"version": "5.1.2", "dev": true, "inBundle": true, "license": "MIT"}, "node_modules/npm/node_modules/stream-iterate/node_modules/string_decoder": {"version": "1.1.1", "dev": true, "inBundle": true, "license": "MIT", "dependencies": {"safe-buffer": "~5.1.0"}}, "node_modules/npm/node_modules/stream-iterate/node_modules/string_decoder/node_modules/safe-buffer": {"version": "5.1.2", "dev": true, "inBundle": true, "license": "MIT"}, "node_modules/npm/node_modules/stream-shift": {"version": "1.0.0", "dev": true, "inBundle": true, "license": "MIT"}, "node_modules/npm/node_modules/strict-uri-encode": {"version": "2.0.0", "dev": true, "inBundle": true, "license": "MIT", "engines": {"node": ">=4"}}, "node_modules/npm/node_modules/string_decoder": {"version": "1.3.0", "dev": true, "inBundle": true, "license": "MIT", "dependencies": {"safe-buffer": "~5.2.0"}}, "node_modules/npm/node_modules/string_decoder/node_modules/safe-buffer": {"version": "5.2.0", "dev": true, "inBundle": true, "license": "MIT"}, "node_modules/npm/node_modules/string-width": {"version": "2.1.1", "dev": true, "inBundle": true, "license": "MIT", "dependencies": {"is-fullwidth-code-point": "^2.0.0", "strip-ansi": "^4.0.0"}, "engines": {"node": ">=4"}}, "node_modules/npm/node_modules/string-width/node_modules/ansi-regex": {"version": "3.0.0", "dev": true, "inBundle": true, "license": "MIT", "engines": {"node": ">=4"}}, "node_modules/npm/node_modules/string-width/node_modules/is-fullwidth-code-point": {"version": "2.0.0", "dev": true, "inBundle": true, "license": "MIT", "engines": {"node": ">=4"}}, "node_modules/npm/node_modules/string-width/node_modules/strip-ansi": {"version": "4.0.0", "dev": true, "inBundle": true, "license": "MIT", "dependencies": {"ansi-regex": "^3.0.0"}, "engines": {"node": ">=4"}}, "node_modules/npm/node_modules/stringify-package": {"version": "1.0.1", "dev": true, "inBundle": true, "license": "ISC"}, "node_modules/npm/node_modules/strip-ansi": {"version": "3.0.1", "dev": true, "inBundle": true, "license": "MIT", "dependencies": {"ansi-regex": "^2.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/npm/node_modules/strip-eof": {"version": "1.0.0", "dev": true, "inBundle": true, "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/npm/node_modules/strip-json-comments": {"version": "2.0.1", "dev": true, "inBundle": true, "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/npm/node_modules/supports-color": {"version": "5.4.0", "dev": true, "inBundle": true, "license": "MIT", "dependencies": {"has-flag": "^3.0.0"}, "engines": {"node": ">=4"}}, "node_modules/npm/node_modules/tar": {"version": "4.4.19", "dev": true, "inBundle": true, "license": "ISC", "dependencies": {"chownr": "^1.1.4", "fs-minipass": "^1.2.7", "minipass": "^2.9.0", "minizlib": "^1.3.3", "mkdirp": "^0.5.5", "safe-buffer": "^5.2.1", "yallist": "^3.1.1"}, "engines": {"node": ">=4.5"}}, "node_modules/npm/node_modules/tar/node_modules/minipass": {"version": "2.9.0", "dev": true, "inBundle": true, "license": "ISC", "dependencies": {"safe-buffer": "^5.1.2", "yallist": "^3.0.0"}}, "node_modules/npm/node_modules/tar/node_modules/safe-buffer": {"version": "5.2.1", "dev": true, "funding": [{"type": "github", "url": "https://github.com/sponsors/feross"}, {"type": "patreon", "url": "https://www.patreon.com/feross"}, {"type": "consulting", "url": "https://feross.org/support"}], "inBundle": true, "license": "MIT"}, "node_modules/npm/node_modules/tar/node_modules/yallist": {"version": "3.1.1", "dev": true, "inBundle": true, "license": "ISC"}, "node_modules/npm/node_modules/term-size": {"version": "1.2.0", "dev": true, "inBundle": true, "license": "MIT", "dependencies": {"execa": "^0.7.0"}, "engines": {"node": ">=4"}}, "node_modules/npm/node_modules/text-table": {"version": "0.2.0", "dev": true, "inBundle": true, "license": "MIT"}, "node_modules/npm/node_modules/through": {"version": "2.3.8", "dev": true, "inBundle": true, "license": "MIT"}, "node_modules/npm/node_modules/through2": {"version": "2.0.3", "dev": true, "inBundle": true, "license": "MIT", "dependencies": {"readable-stream": "^2.1.5", "xtend": "~4.0.1"}}, "node_modules/npm/node_modules/through2/node_modules/readable-stream": {"version": "2.3.6", "dev": true, "inBundle": true, "license": "MIT", "dependencies": {"core-util-is": "~1.0.0", "inherits": "~2.0.3", "isarray": "~1.0.0", "process-nextick-args": "~2.0.0", "safe-buffer": "~5.1.1", "string_decoder": "~1.1.1", "util-deprecate": "~1.0.1"}}, "node_modules/npm/node_modules/through2/node_modules/readable-stream/node_modules/safe-buffer": {"version": "5.1.2", "dev": true, "inBundle": true, "license": "MIT"}, "node_modules/npm/node_modules/through2/node_modules/string_decoder": {"version": "1.1.1", "dev": true, "inBundle": true, "license": "MIT", "dependencies": {"safe-buffer": "~5.1.0"}}, "node_modules/npm/node_modules/through2/node_modules/string_decoder/node_modules/safe-buffer": {"version": "5.1.2", "dev": true, "inBundle": true, "license": "MIT"}, "node_modules/npm/node_modules/timed-out": {"version": "4.0.1", "dev": true, "inBundle": true, "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/npm/node_modules/tiny-relative-date": {"version": "1.3.0", "dev": true, "inBundle": true, "license": "MIT"}, "node_modules/npm/node_modules/tough-cookie": {"version": "2.5.0", "dev": true, "inBundle": true, "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"psl": "^1.1.28", "punycode": "^2.1.1"}, "engines": {"node": ">=0.8"}}, "node_modules/npm/node_modules/tough-cookie/node_modules/punycode": {"version": "2.1.1", "dev": true, "inBundle": true, "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/npm/node_modules/tunnel-agent": {"version": "0.6.0", "dev": true, "inBundle": true, "license": "Apache-2.0", "dependencies": {"safe-buffer": "^5.0.1"}, "engines": {"node": "*"}}, "node_modules/npm/node_modules/tweetnacl": {"version": "0.14.5", "dev": true, "inBundle": true, "license": "Unlicense"}, "node_modules/npm/node_modules/typedarray": {"version": "0.0.6", "dev": true, "inBundle": true, "license": "MIT"}, "node_modules/npm/node_modules/uid-number": {"version": "0.0.6", "dev": true, "inBundle": true, "license": "ISC", "engines": {"node": "*"}}, "node_modules/npm/node_modules/umask": {"version": "1.1.0", "dev": true, "inBundle": true, "license": "MIT"}, "node_modules/npm/node_modules/unique-filename": {"version": "1.1.1", "dev": true, "inBundle": true, "license": "ISC", "dependencies": {"unique-slug": "^2.0.0"}}, "node_modules/npm/node_modules/unique-slug": {"version": "2.0.0", "dev": true, "inBundle": true, "license": "ISC", "dependencies": {"imurmurhash": "^0.1.4"}}, "node_modules/npm/node_modules/unique-string": {"version": "1.0.0", "dev": true, "inBundle": true, "license": "MIT", "dependencies": {"crypto-random-string": "^1.0.0"}, "engines": {"node": ">=4"}}, "node_modules/npm/node_modules/unpipe": {"version": "1.0.0", "dev": true, "inBundle": true, "license": "MIT", "engines": {"node": ">= 0.8"}}, "node_modules/npm/node_modules/unzip-response": {"version": "2.0.1", "dev": true, "inBundle": true, "license": "MIT", "engines": {"node": ">=4"}}, "node_modules/npm/node_modules/update-notifier": {"version": "2.5.0", "dev": true, "inBundle": true, "license": "BSD-2-<PERSON><PERSON>", "dependencies": {"boxen": "^1.2.1", "chalk": "^2.0.1", "configstore": "^3.0.0", "import-lazy": "^2.1.0", "is-ci": "^1.0.10", "is-installed-globally": "^0.1.0", "is-npm": "^1.0.0", "latest-version": "^3.0.0", "semver-diff": "^2.0.0", "xdg-basedir": "^3.0.0"}, "engines": {"node": ">=4"}}, "node_modules/npm/node_modules/uri-js": {"version": "4.4.1", "dev": true, "inBundle": true, "license": "BSD-2-<PERSON><PERSON>", "dependencies": {"punycode": "^2.1.0"}}, "node_modules/npm/node_modules/uri-js/node_modules/punycode": {"version": "2.1.1", "dev": true, "inBundle": true, "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/npm/node_modules/url-parse-lax": {"version": "1.0.0", "dev": true, "inBundle": true, "license": "MIT", "dependencies": {"prepend-http": "^1.0.1"}, "engines": {"node": ">=0.10.0"}}, "node_modules/npm/node_modules/util-deprecate": {"version": "1.0.2", "dev": true, "inBundle": true, "license": "MIT"}, "node_modules/npm/node_modules/util-extend": {"version": "1.0.3", "dev": true, "inBundle": true, "license": "MIT"}, "node_modules/npm/node_modules/util-promisify": {"version": "2.1.0", "dev": true, "inBundle": true, "license": "MIT", "dependencies": {"object.getownpropertydescriptors": "^2.0.3"}}, "node_modules/npm/node_modules/uuid": {"version": "3.4.0", "deprecated": "Please upgrade  to version 7 or higher.  Older versions may use Math.random() in certain circumstances, which is known to be problematic.  See https://v8.dev/blog/math-random for details.", "dev": true, "inBundle": true, "license": "MIT", "bin": {"uuid": "bin/uuid"}}, "node_modules/npm/node_modules/validate-npm-package-license": {"version": "3.0.4", "dev": true, "inBundle": true, "license": "Apache-2.0", "dependencies": {"spdx-correct": "^3.0.0", "spdx-expression-parse": "^3.0.0"}}, "node_modules/npm/node_modules/validate-npm-package-name": {"version": "3.0.0", "dev": true, "inBundle": true, "license": "ISC", "dependencies": {"builtins": "^1.0.3"}}, "node_modules/npm/node_modules/verror": {"version": "1.10.0", "dev": true, "engines": ["node >=0.6.0"], "inBundle": true, "license": "MIT", "dependencies": {"assert-plus": "^1.0.0", "core-util-is": "1.0.2", "extsprintf": "^1.2.0"}}, "node_modules/npm/node_modules/wcwidth": {"version": "1.0.1", "dev": true, "inBundle": true, "license": "MIT", "dependencies": {"defaults": "^1.0.3"}}, "node_modules/npm/node_modules/which": {"version": "1.3.1", "dev": true, "inBundle": true, "license": "ISC", "dependencies": {"isexe": "^2.0.0"}, "bin": {"which": "bin/which"}}, "node_modules/npm/node_modules/which-module": {"version": "2.0.0", "dev": true, "inBundle": true, "license": "ISC"}, "node_modules/npm/node_modules/wide-align": {"version": "1.1.2", "dev": true, "inBundle": true, "license": "ISC", "dependencies": {"string-width": "^1.0.2"}}, "node_modules/npm/node_modules/wide-align/node_modules/string-width": {"version": "1.0.2", "dev": true, "inBundle": true, "license": "MIT", "dependencies": {"code-point-at": "^1.0.0", "is-fullwidth-code-point": "^1.0.0", "strip-ansi": "^3.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/npm/node_modules/widest-line": {"version": "2.0.1", "dev": true, "inBundle": true, "license": "MIT", "dependencies": {"string-width": "^2.1.1"}, "engines": {"node": ">=4"}}, "node_modules/npm/node_modules/worker-farm": {"version": "1.7.0", "dev": true, "inBundle": true, "license": "MIT", "dependencies": {"errno": "~0.1.7"}}, "node_modules/npm/node_modules/wrap-ansi": {"version": "5.1.0", "dev": true, "inBundle": true, "license": "MIT", "dependencies": {"ansi-styles": "^3.2.0", "string-width": "^3.0.0", "strip-ansi": "^5.0.0"}, "engines": {"node": ">=6"}}, "node_modules/npm/node_modules/wrap-ansi/node_modules/ansi-regex": {"version": "4.1.1", "dev": true, "inBundle": true, "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/npm/node_modules/wrap-ansi/node_modules/is-fullwidth-code-point": {"version": "2.0.0", "dev": true, "inBundle": true, "license": "MIT", "engines": {"node": ">=4"}}, "node_modules/npm/node_modules/wrap-ansi/node_modules/string-width": {"version": "3.1.0", "dev": true, "inBundle": true, "license": "MIT", "dependencies": {"emoji-regex": "^7.0.1", "is-fullwidth-code-point": "^2.0.0", "strip-ansi": "^5.1.0"}, "engines": {"node": ">=6"}}, "node_modules/npm/node_modules/wrap-ansi/node_modules/strip-ansi": {"version": "5.2.0", "dev": true, "inBundle": true, "license": "MIT", "dependencies": {"ansi-regex": "^4.1.0"}, "engines": {"node": ">=6"}}, "node_modules/npm/node_modules/wrappy": {"version": "1.0.2", "dev": true, "inBundle": true, "license": "ISC"}, "node_modules/npm/node_modules/write-file-atomic": {"version": "2.4.3", "dev": true, "inBundle": true, "license": "ISC", "dependencies": {"graceful-fs": "^4.1.11", "imurmurhash": "^0.1.4", "signal-exit": "^3.0.2"}}, "node_modules/npm/node_modules/xdg-basedir": {"version": "3.0.0", "dev": true, "inBundle": true, "license": "MIT", "engines": {"node": ">=4"}}, "node_modules/npm/node_modules/xtend": {"version": "4.0.1", "dev": true, "inBundle": true, "license": "MIT", "engines": {"node": ">=0.4"}}, "node_modules/npm/node_modules/y18n": {"version": "4.0.1", "dev": true, "inBundle": true, "license": "ISC"}, "node_modules/npm/node_modules/yallist": {"version": "3.0.3", "dev": true, "inBundle": true, "license": "ISC"}, "node_modules/npm/node_modules/yargs": {"version": "14.2.3", "dev": true, "inBundle": true, "license": "MIT", "dependencies": {"cliui": "^5.0.0", "decamelize": "^1.2.0", "find-up": "^3.0.0", "get-caller-file": "^2.0.1", "require-directory": "^2.1.1", "require-main-filename": "^2.0.0", "set-blocking": "^2.0.0", "string-width": "^3.0.0", "which-module": "^2.0.0", "y18n": "^4.0.0", "yargs-parser": "^15.0.1"}}, "node_modules/npm/node_modules/yargs-parser": {"version": "15.0.1", "dev": true, "inBundle": true, "license": "ISC", "dependencies": {"camelcase": "^5.0.0", "decamelize": "^1.2.0"}}, "node_modules/npm/node_modules/yargs-parser/node_modules/camelcase": {"version": "5.3.1", "dev": true, "inBundle": true, "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/npm/node_modules/yargs/node_modules/ansi-regex": {"version": "4.1.0", "dev": true, "inBundle": true, "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/npm/node_modules/yargs/node_modules/find-up": {"version": "3.0.0", "dev": true, "inBundle": true, "license": "MIT", "dependencies": {"locate-path": "^3.0.0"}, "engines": {"node": ">=6"}}, "node_modules/npm/node_modules/yargs/node_modules/is-fullwidth-code-point": {"version": "2.0.0", "dev": true, "inBundle": true, "license": "MIT", "engines": {"node": ">=4"}}, "node_modules/npm/node_modules/yargs/node_modules/locate-path": {"version": "3.0.0", "dev": true, "inBundle": true, "license": "MIT", "dependencies": {"p-locate": "^3.0.0", "path-exists": "^3.0.0"}, "engines": {"node": ">=6"}}, "node_modules/npm/node_modules/yargs/node_modules/p-limit": {"version": "2.3.0", "dev": true, "inBundle": true, "license": "MIT", "dependencies": {"p-try": "^2.0.0"}, "engines": {"node": ">=6"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/npm/node_modules/yargs/node_modules/p-locate": {"version": "3.0.0", "dev": true, "inBundle": true, "license": "MIT", "dependencies": {"p-limit": "^2.0.0"}, "engines": {"node": ">=6"}}, "node_modules/npm/node_modules/yargs/node_modules/p-try": {"version": "2.2.0", "dev": true, "inBundle": true, "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/npm/node_modules/yargs/node_modules/string-width": {"version": "3.1.0", "dev": true, "inBundle": true, "license": "MIT", "dependencies": {"emoji-regex": "^7.0.1", "is-fullwidth-code-point": "^2.0.0", "strip-ansi": "^5.1.0"}, "engines": {"node": ">=6"}}, "node_modules/npm/node_modules/yargs/node_modules/strip-ansi": {"version": "5.2.0", "dev": true, "inBundle": true, "license": "MIT", "dependencies": {"ansi-regex": "^4.1.0"}, "engines": {"node": ">=6"}}, "node_modules/once": {"version": "1.4.0", "dev": true, "license": "ISC", "dependencies": {"wrappy": "1"}}, "node_modules/optionator": {"version": "0.9.3", "dev": true, "license": "MIT", "dependencies": {"@aashutoshrathi/word-wrap": "^1.2.3", "deep-is": "^0.1.3", "fast-levenshtein": "^2.0.6", "levn": "^0.4.1", "prelude-ls": "^1.2.1", "type-check": "^0.4.0"}, "engines": {"node": ">= 0.8.0"}}, "node_modules/p-limit": {"version": "3.1.0", "dev": true, "license": "MIT", "dependencies": {"yocto-queue": "^0.1.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/p-locate": {"version": "5.0.0", "dev": true, "license": "MIT", "dependencies": {"p-limit": "^3.0.2"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/parent-module": {"version": "1.0.1", "dev": true, "license": "MIT", "dependencies": {"callsites": "^3.0.0"}, "engines": {"node": ">=6"}}, "node_modules/path-browserify": {"version": "1.0.1", "dev": true, "license": "MIT"}, "node_modules/path-exists": {"version": "4.0.0", "dev": true, "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/path-is-absolute": {"version": "1.0.1", "dev": true, "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/path-key": {"version": "3.1.1", "dev": true, "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/path-parse": {"version": "1.0.7", "dev": true, "license": "MIT"}, "node_modules/path-scurry": {"version": "1.10.1", "dev": true, "license": "BlueOak-1.0.0", "dependencies": {"lru-cache": "^9.1.1 || ^10.0.0", "minipass": "^5.0.0 || ^6.0.2 || ^7.0.0"}, "engines": {"node": ">=16 || 14 >=14.17"}, "funding": {"url": "https://github.com/sponsors/isaacs"}}, "node_modules/path-scurry/node_modules/lru-cache": {"version": "10.2.0", "dev": true, "license": "ISC", "engines": {"node": "14 || >=16.14"}}, "node_modules/path-type": {"version": "4.0.0", "dev": true, "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/picocolors": {"version": "1.0.1", "dev": true, "license": "ISC"}, "node_modules/picomatch": {"version": "2.3.1", "dev": true, "license": "MIT", "engines": {"node": ">=8.6"}, "funding": {"url": "https://github.com/sponsors/jonschlinkert"}}, "node_modules/prelude-ls": {"version": "1.2.1", "dev": true, "license": "MIT", "engines": {"node": ">= 0.8.0"}}, "node_modules/prettier": {"version": "2.8.8", "dev": true, "license": "MIT", "bin": {"prettier": "bin-prettier.js"}, "engines": {"node": ">=10.13.0"}, "funding": {"url": "https://github.com/prettier/prettier?sponsor=1"}}, "node_modules/pretty-format": {"version": "29.7.0", "dev": true, "license": "MIT", "dependencies": {"@jest/schemas": "^29.6.3", "ansi-styles": "^5.0.0", "react-is": "^18.0.0"}, "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}}, "node_modules/pretty-format/node_modules/ansi-styles": {"version": "5.2.0", "dev": true, "license": "MIT", "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/chalk/ansi-styles?sponsor=1"}}, "node_modules/punycode": {"version": "2.3.1", "dev": true, "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/queue-microtask": {"version": "1.2.3", "dev": true, "funding": [{"type": "github", "url": "https://github.com/sponsors/feross"}, {"type": "patreon", "url": "https://www.patreon.com/feross"}, {"type": "consulting", "url": "https://feross.org/support"}], "license": "MIT"}, "node_modules/railroad-diagrams": {"version": "1.0.0", "dev": true, "license": "CC0-1.0"}, "node_modules/randexp": {"version": "0.4.6", "dev": true, "license": "MIT", "dependencies": {"discontinuous-range": "1.0.0", "ret": "~0.1.10"}, "engines": {"node": ">=0.12"}}, "node_modules/react-is": {"version": "18.3.1", "dev": true, "license": "MIT"}, "node_modules/regexp-to-ast": {"version": "0.5.0", "dev": true, "license": "MIT"}, "node_modules/require-directory": {"version": "2.1.1", "dev": true, "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/require-from-string": {"version": "2.0.2", "dev": true, "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/resolve": {"version": "1.19.0", "dev": true, "license": "MIT", "dependencies": {"is-core-module": "^2.1.0", "path-parse": "^1.0.6"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/resolve-from": {"version": "4.0.0", "dev": true, "license": "MIT", "engines": {"node": ">=4"}}, "node_modules/resolve-pkg-maps": {"version": "1.0.0", "dev": true, "license": "MIT", "funding": {"url": "https://github.com/privatenumber/resolve-pkg-maps?sponsor=1"}}, "node_modules/ret": {"version": "0.1.15", "dev": true, "license": "MIT", "engines": {"node": ">=0.12"}}, "node_modules/reusify": {"version": "1.0.4", "dev": true, "license": "MIT", "engines": {"iojs": ">=1.0.0", "node": ">=0.10.0"}}, "node_modules/rimraf": {"version": "5.0.5", "dev": true, "license": "ISC", "dependencies": {"glob": "^10.3.7"}, "bin": {"rimraf": "dist/esm/bin.mjs"}, "engines": {"node": ">=14"}, "funding": {"url": "https://github.com/sponsors/isaacs"}}, "node_modules/rimraf/node_modules/brace-expansion": {"version": "2.0.1", "dev": true, "license": "MIT", "dependencies": {"balanced-match": "^1.0.0"}}, "node_modules/rimraf/node_modules/glob": {"version": "10.3.10", "dev": true, "license": "ISC", "dependencies": {"foreground-child": "^3.1.0", "jackspeak": "^2.3.5", "minimatch": "^9.0.1", "minipass": "^5.0.0 || ^6.0.2 || ^7.0.0", "path-scurry": "^1.10.1"}, "bin": {"glob": "dist/esm/bin.mjs"}, "engines": {"node": ">=16 || 14 >=14.17"}, "funding": {"url": "https://github.com/sponsors/isaacs"}}, "node_modules/rimraf/node_modules/minimatch": {"version": "9.0.3", "dev": true, "license": "ISC", "dependencies": {"brace-expansion": "^2.0.1"}, "engines": {"node": ">=16 || 14 >=14.17"}, "funding": {"url": "https://github.com/sponsors/isaacs"}}, "node_modules/run-parallel": {"version": "1.2.0", "dev": true, "funding": [{"type": "github", "url": "https://github.com/sponsors/feross"}, {"type": "patreon", "url": "https://www.patreon.com/feross"}, {"type": "consulting", "url": "https://feross.org/support"}], "license": "MIT", "dependencies": {"queue-microtask": "^1.2.2"}}, "node_modules/secure-random": {"version": "1.1.2", "dev": true, "license": "MIT"}, "node_modules/semver": {"version": "7.5.4", "dev": true, "license": "ISC", "dependencies": {"lru-cache": "^6.0.0"}, "bin": {"semver": "bin/semver.js"}, "engines": {"node": ">=10"}}, "node_modules/shebang-command": {"version": "2.0.0", "dev": true, "license": "MIT", "dependencies": {"shebang-regex": "^3.0.0"}, "engines": {"node": ">=8"}}, "node_modules/shebang-regex": {"version": "3.0.0", "dev": true, "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/signal-exit": {"version": "4.1.0", "dev": true, "license": "ISC", "engines": {"node": ">=14"}, "funding": {"url": "https://github.com/sponsors/isaacs"}}, "node_modules/slash": {"version": "3.0.0", "dev": true, "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/slice-ansi": {"version": "4.0.0", "dev": true, "license": "MIT", "dependencies": {"ansi-styles": "^4.0.0", "astral-regex": "^2.0.0", "is-fullwidth-code-point": "^3.0.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/chalk/slice-ansi?sponsor=1"}}, "node_modules/slice-ansi/node_modules/ansi-styles": {"version": "4.3.0", "dev": true, "license": "MIT", "dependencies": {"color-convert": "^2.0.1"}, "engines": {"node": ">=8"}, "funding": {"url": "https://github.com/chalk/ansi-styles?sponsor=1"}}, "node_modules/slice-ansi/node_modules/color-convert": {"version": "2.0.1", "dev": true, "license": "MIT", "dependencies": {"color-name": "~1.1.4"}, "engines": {"node": ">=7.0.0"}}, "node_modules/slice-ansi/node_modules/color-name": {"version": "1.1.4", "dev": true, "license": "MIT"}, "node_modules/spdx-exceptions": {"version": "2.4.0", "dev": true, "license": "CC-BY-3.0"}, "node_modules/spdx-license-ids": {"version": "3.0.16", "dev": true, "license": "CC0-1.0"}, "node_modules/stack-utils": {"version": "2.0.6", "dev": true, "license": "MIT", "dependencies": {"escape-string-regexp": "^2.0.0"}, "engines": {"node": ">=10"}}, "node_modules/stack-utils/node_modules/escape-string-regexp": {"version": "2.0.0", "dev": true, "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/string-width": {"version": "4.2.3", "dev": true, "license": "MIT", "dependencies": {"emoji-regex": "^8.0.0", "is-fullwidth-code-point": "^3.0.0", "strip-ansi": "^6.0.1"}, "engines": {"node": ">=8"}}, "node_modules/string-width-cjs": {"name": "string-width", "version": "4.2.3", "dev": true, "license": "MIT", "dependencies": {"emoji-regex": "^8.0.0", "is-fullwidth-code-point": "^3.0.0", "strip-ansi": "^6.0.1"}, "engines": {"node": ">=8"}}, "node_modules/strip-ansi": {"version": "6.0.1", "dev": true, "license": "MIT", "dependencies": {"ansi-regex": "^5.0.1"}, "engines": {"node": ">=8"}}, "node_modules/strip-ansi-cjs": {"name": "strip-ansi", "version": "6.0.1", "dev": true, "license": "MIT", "dependencies": {"ansi-regex": "^5.0.1"}, "engines": {"node": ">=8"}}, "node_modules/strip-json-comments": {"version": "3.1.1", "dev": true, "license": "MIT", "engines": {"node": ">=8"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/supports-color": {"version": "5.5.0", "dev": true, "license": "MIT", "dependencies": {"has-flag": "^3.0.0"}, "engines": {"node": ">=4"}}, "node_modules/table": {"version": "6.8.1", "dev": true, "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"ajv": "^8.0.1", "lodash.truncate": "^4.4.2", "slice-ansi": "^4.0.0", "string-width": "^4.2.3", "strip-ansi": "^6.0.1"}, "engines": {"node": ">=10.0.0"}}, "node_modules/table/node_modules/ajv": {"version": "8.12.0", "dev": true, "license": "MIT", "dependencies": {"fast-deep-equal": "^3.1.1", "json-schema-traverse": "^1.0.0", "require-from-string": "^2.0.2", "uri-js": "^4.2.2"}, "funding": {"type": "github", "url": "https://github.com/sponsors/epoberezkin"}}, "node_modules/table/node_modules/json-schema-traverse": {"version": "1.0.0", "dev": true, "license": "MIT"}, "node_modules/text-table": {"version": "0.2.0", "dev": true, "license": "MIT"}, "node_modules/to-regex-range": {"version": "5.0.1", "dev": true, "license": "MIT", "dependencies": {"is-number": "^7.0.0"}, "engines": {"node": ">=8.0"}}, "node_modules/ts-mixer": {"version": "6.0.3", "dev": true, "license": "MIT"}, "node_modules/ts-morph": {"version": "19.0.0", "dev": true, "license": "MIT", "dependencies": {"@ts-morph/common": "~0.20.0", "code-block-writer": "^12.0.0"}}, "node_modules/tslib": {"version": "2.8.0", "resolved": "https://registry.npmjs.org/tslib/-/tslib-2.8.0.tgz", "integrity": "sha512-jWVzBLplnCmoaTr13V9dYbiQ99wvZRd0vNWaDRg+aVYRcjDF3nDksxFDE/+fkXnKhpnUUkmx5pK/v8mCtLVqZA==", "dev": true}, "node_modules/tsutils": {"version": "3.21.0", "dev": true, "license": "MIT", "dependencies": {"tslib": "^1.8.1"}, "engines": {"node": ">= 6"}, "peerDependencies": {"typescript": ">=2.8.0 || >= 3.2.0-dev || >= 3.3.0-dev || >= 3.4.0-dev || >= 3.5.0-dev || >= 3.6.0-dev || >= 3.6.0-beta || >= 3.7.0-dev || >= 3.7.0-beta"}}, "node_modules/tsutils/node_modules/tslib": {"version": "1.14.1", "dev": true, "license": "0BSD"}, "node_modules/tsx": {"version": "4.7.0", "dev": true, "license": "MIT", "dependencies": {"esbuild": "~0.19.10", "get-tsconfig": "^4.7.2"}, "bin": {"tsx": "dist/cli.mjs"}, "engines": {"node": ">=18.0.0"}, "optionalDependencies": {"fsevents": "~2.3.3"}}, "node_modules/type-check": {"version": "0.4.0", "dev": true, "license": "MIT", "dependencies": {"prelude-ls": "^1.2.1"}, "engines": {"node": ">= 0.8.0"}}, "node_modules/type-fest": {"version": "0.20.2", "dev": true, "license": "(MIT OR CC0-1.0)", "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/typescript": {"version": "4.9.5", "dev": true, "license": "Apache-2.0", "bin": {"tsc": "bin/tsc", "tsserver": "bin/tsserver"}, "engines": {"node": ">=4.2.0"}}, "node_modules/undici-types": {"version": "5.26.5", "dev": true, "license": "MIT"}, "node_modules/uri-js": {"version": "4.4.1", "dev": true, "license": "BSD-2-<PERSON><PERSON>", "dependencies": {"punycode": "^2.1.0"}}, "node_modules/utf-8-validate": {"version": "6.0.4", "dev": true, "hasInstallScript": true, "license": "MIT", "optional": true, "dependencies": {"node-gyp-build": "^4.3.0"}, "engines": {"node": ">=6.14.2"}}, "node_modules/varint": {"version": "6.0.0", "dev": true, "license": "MIT"}, "node_modules/visitor-as": {"version": "0.11.4", "dev": true, "license": "MIT", "dependencies": {"lodash.clonedeep": "^4.5.0", "ts-mixer": "^6.0.2"}, "peerDependencies": {"assemblyscript": "^0.25.0"}}, "node_modules/vscode-json-languageservice": {"version": "4.2.1", "dev": true, "license": "MIT", "dependencies": {"jsonc-parser": "^3.0.0", "vscode-languageserver-textdocument": "^1.0.3", "vscode-languageserver-types": "^3.16.0", "vscode-nls": "^5.0.0", "vscode-uri": "^3.0.3"}}, "node_modules/vscode-languageserver-textdocument": {"version": "1.0.11", "dev": true, "license": "MIT"}, "node_modules/vscode-languageserver-types": {"version": "3.17.5", "dev": true, "license": "MIT"}, "node_modules/vscode-nls": {"version": "5.2.0", "dev": true, "license": "MIT"}, "node_modules/vscode-uri": {"version": "3.0.8", "dev": true, "license": "MIT"}, "node_modules/which": {"version": "2.0.2", "dev": true, "license": "ISC", "dependencies": {"isexe": "^2.0.0"}, "bin": {"node-which": "bin/node-which"}, "engines": {"node": ">= 8"}}, "node_modules/wrap-ansi": {"version": "7.0.0", "dev": true, "license": "MIT", "dependencies": {"ansi-styles": "^4.0.0", "string-width": "^4.1.0", "strip-ansi": "^6.0.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/chalk/wrap-ansi?sponsor=1"}}, "node_modules/wrap-ansi-cjs": {"name": "wrap-ansi", "version": "7.0.0", "dev": true, "license": "MIT", "dependencies": {"ansi-styles": "^4.0.0", "string-width": "^4.1.0", "strip-ansi": "^6.0.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/chalk/wrap-ansi?sponsor=1"}}, "node_modules/wrap-ansi-cjs/node_modules/ansi-styles": {"version": "4.3.0", "dev": true, "license": "MIT", "dependencies": {"color-convert": "^2.0.1"}, "engines": {"node": ">=8"}, "funding": {"url": "https://github.com/chalk/ansi-styles?sponsor=1"}}, "node_modules/wrap-ansi-cjs/node_modules/color-convert": {"version": "2.0.1", "dev": true, "license": "MIT", "dependencies": {"color-name": "~1.1.4"}, "engines": {"node": ">=7.0.0"}}, "node_modules/wrap-ansi-cjs/node_modules/color-name": {"version": "1.1.4", "dev": true, "license": "MIT"}, "node_modules/wrap-ansi/node_modules/ansi-styles": {"version": "4.3.0", "dev": true, "license": "MIT", "dependencies": {"color-convert": "^2.0.1"}, "engines": {"node": ">=8"}, "funding": {"url": "https://github.com/chalk/ansi-styles?sponsor=1"}}, "node_modules/wrap-ansi/node_modules/color-convert": {"version": "2.0.1", "dev": true, "license": "MIT", "dependencies": {"color-name": "~1.1.4"}, "engines": {"node": ">=7.0.0"}}, "node_modules/wrap-ansi/node_modules/color-name": {"version": "1.1.4", "dev": true, "license": "MIT"}, "node_modules/wrappy": {"version": "1.0.2", "dev": true, "license": "ISC"}, "node_modules/y18n": {"version": "5.0.8", "dev": true, "license": "ISC", "engines": {"node": ">=10"}}, "node_modules/yallist": {"version": "4.0.0", "dev": true, "license": "ISC"}, "node_modules/yaml": {"version": "2.5.0", "dev": true, "license": "ISC", "bin": {"yaml": "bin.mjs"}, "engines": {"node": ">= 14"}}, "node_modules/yargs": {"version": "17.7.2", "dev": true, "license": "MIT", "dependencies": {"cliui": "^8.0.1", "escalade": "^3.1.1", "get-caller-file": "^2.0.5", "require-directory": "^2.1.1", "string-width": "^4.2.3", "y18n": "^5.0.5", "yargs-parser": "^21.1.1"}, "engines": {"node": ">=12"}}, "node_modules/yargs-parser": {"version": "21.1.1", "dev": true, "license": "ISC", "engines": {"node": ">=12"}}, "node_modules/yocto-queue": {"version": "0.1.0", "dev": true, "license": "MIT", "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}}}