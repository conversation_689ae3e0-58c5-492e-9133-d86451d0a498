import { Args, Result, Serializable } from '@massalabs/as-types';
import { Address } from '@massalabs/massa-as-sdk';
import { u256 } from 'as-bignum/assembly';

export class FlashLoan implements Serializable {
  constructor(
    public poolAddress: Address = new Address(),
    public tokenAddress: Address = new Address(),
    public amount: u256 = u256.Zero,
  ) {}

  serialize(): StaticArray<u8> {
    return new Args()
      .add(this.poolAddress)
      .add(this.tokenAddress)
      .add(this.amount)
      .serialize();
  }

  deserialize(data: StaticArray<u8>, offset: i32): Result<i32> {
    const args = new Args(data, offset);

    this.poolAddress = new Address(args.nextString().expect('Invalid address'));
    this.tokenAddress = new Address(
      args.nextString().expect('Invalid address'),
    );
    this.amount = args.nextU256().expect('Invalid amount');

    return new Result(args.offset);
  }

  toString(): string {
    return (
      `Flash Pool Address: ${this.poolAddress.toString()}\n` +
      `Flash Token Address: ${this.tokenAddress.toString()}\n` +
      `Flash Amount: ${this.amount.toString()}\n`
    );
  }
}
