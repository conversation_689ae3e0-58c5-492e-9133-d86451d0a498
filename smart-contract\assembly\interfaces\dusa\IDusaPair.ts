import {
  Args,
  bytesToFixedSizeArray,
  bytesToString,
  bytesToU256,
  byteToBool,
  Result,
} from '@massalabs/as-types';
import { Address, call, Storage } from '@massalabs/massa-as-sdk';

import { u256 } from 'as-bignum/assembly/integer/u256';
import { MRC20Wrapper } from '@massalabs/sc-standards/assembly/contracts/MRC20/wrapper';

/// @notice The token that is used as the base currency for the pair
export const TOKEN_X = 'TOKEN_X';
/// @notice The token that is used as the quote currency for the pair
export const TOKEN_Y = 'TOKEN_Y';

export class IDusaPair {
  _origin: Address;

  /**
   * Wraps a smart contract exposing standard token FFI.
   *
   * @param {Address} at - Address of the smart contract.
   */
  constructor(at: Address) {
    this._origin = at;
  }

  /**
   * Swap tokens iterating over the bins until the entire amount is swapped.
   * Will swap token X for token Y if `_swapForY` is true, and token Y for token X if `_swapForY` is false.
   * This function will not transfer the tokens from the caller, it is expected that the tokens have already been
   * transferred to this contract through another contract.
   * That is why this function shouldn't be called directly, but through one of the swap functions of the router
   * that will also perform safety checks.
   *
   * @param {bool} swapForY - Whether you've swapping token X for token Y (true) or token Y for token X (false)
   * @param {Address} to - The address to send the tokens to
   * @param masToSend The amount of Massa to send for storage
   *
   */
  swap(swapForY: bool, to: Address, masToSend: u64): u256 {
    const args = new Args().add(swapForY).add(to);
    const res = call(this._origin, 'swap', args, masToSend);
    return bytesToU256(res);
  }

  /**
   * Execute a flash loan.
   * The caller must implement the `IFlashLoanCallback` interface and have the `flashLoanCallback` function.
   * The `flashLoanCallback` function will be called by the pair contract to execute the logic of the flash loan.
   * The caller must return `true` if the flash loan was successful, and `false` otherwise.
   * The caller is expected to transfer the `amount + fee` of the token to this contract.
   *
   * @param {MRC20Wrapper} token - The token to flash loan
   * @param {u256} amount - The amount of tokens to flash loan
   * @param masToSend The amount of Massa to send for storage
   *
   */
  flashLoan(token: MRC20Wrapper, amount: u256, masToSend: u64): void {
    const args = new Args().add(token).add(amount);
    call(this._origin, 'flashLoan', args, masToSend);
  }

  getTokenX(): MRC20Wrapper {
    return new MRC20Wrapper(new Address(Storage.getOf(this._origin, TOKEN_X)));
  }

  getTokenY(): MRC20Wrapper {
    return new MRC20Wrapper(new Address(Storage.getOf(this._origin, TOKEN_Y)));
  }
}
