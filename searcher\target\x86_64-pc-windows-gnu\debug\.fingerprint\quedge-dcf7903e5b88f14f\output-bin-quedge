{"$message_type":"diagnostic","message":"unused import: `display::print_tokens_indices_from_matrix`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src\\main.rs","byte_start":324,"byte_end":365,"line_start":8,"line_end":8,"column_start":15,"column_end":56,"is_primary":true,"text":[{"text":"use helpers::{display::print_tokens_indices_from_matrix, sparse::export_to_png};","highlight_start":15,"highlight_end":56}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"`#[warn(unused_imports)]` on by default","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"remove the unused import","code":null,"level":"help","spans":[{"file_name":"src\\main.rs","byte_start":324,"byte_end":367,"line_start":8,"line_end":8,"column_start":15,"column_end":58,"is_primary":true,"text":[{"text":"use helpers::{display::print_tokens_indices_from_matrix, sparse::export_to_png};","highlight_start":15,"highlight_end":58}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src\\main.rs","byte_start":323,"byte_end":324,"line_start":8,"line_end":8,"column_start":14,"column_end":15,"is_primary":true,"text":[{"text":"use helpers::{display::print_tokens_indices_from_matrix, sparse::export_to_png};","highlight_start":14,"highlight_end":15}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src\\main.rs","byte_start":388,"byte_end":389,"line_start":8,"line_end":8,"column_start":79,"column_end":80,"is_primary":true,"text":[{"text":"use helpers::{display::print_tokens_indices_from_matrix, sparse::export_to_png};","highlight_start":79,"highlight_end":80}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused import: `display::print_tokens_indices_from_matrix`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\main.rs:8:15\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m8\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse helpers::{display::print_tokens_indices_from_matrix, sparse::export_to_png};\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m               \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: `#[warn(unused_imports)]` on by default\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `lettre::SmtpTransport`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src\\main.rs","byte_start":396,"byte_end":417,"line_start":9,"line_end":9,"column_start":5,"column_end":26,"is_primary":true,"text":[{"text":"use lettre::SmtpTransport;","highlight_start":5,"highlight_end":26}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"src\\main.rs","byte_start":392,"byte_end":420,"line_start":9,"line_end":10,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use lettre::SmtpTransport;","highlight_start":1,"highlight_end":27},{"text":"use rust_massa_web3::{client::grpc_client::PublicGrpcClient, types::ChainId};","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused import: `lettre::SmtpTransport`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\main.rs:9:5\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m9\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse lettre::SmtpTransport;\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused imports: `DefaultEditor` and `error::ReadlineError`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src\\main.rs","byte_start":515,"byte_end":528,"line_start":11,"line_end":11,"column_start":17,"column_end":30,"is_primary":true,"text":[{"text":"use rustyline::{DefaultEditor, error::ReadlineError};","highlight_start":17,"highlight_end":30}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\main.rs","byte_start":530,"byte_end":550,"line_start":11,"line_end":11,"column_start":32,"column_end":52,"is_primary":true,"text":[{"text":"use rustyline::{DefaultEditor, error::ReadlineError};","highlight_start":32,"highlight_end":52}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"src\\main.rs","byte_start":499,"byte_end":554,"line_start":11,"line_end":12,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use rustyline::{DefaultEditor, error::ReadlineError};","highlight_start":1,"highlight_end":54},{"text":"use state::GlobalState;","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused imports: `DefaultEditor` and `error::ReadlineError`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\main.rs:11:17\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m11\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse rustyline::{DefaultEditor, error::ReadlineError};\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                 \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `state::GlobalState`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src\\main.rs","byte_start":558,"byte_end":576,"line_start":12,"line_end":12,"column_start":5,"column_end":23,"is_primary":true,"text":[{"text":"use state::GlobalState;","highlight_start":5,"highlight_end":23}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"src\\main.rs","byte_start":554,"byte_end":579,"line_start":12,"line_end":13,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use state::GlobalState;","highlight_start":1,"highlight_end":24},{"text":"use std::{","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused import: `state::GlobalState`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\main.rs:12:5\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m12\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse state::GlobalState;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `warn`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src\\main.rs","byte_start":711,"byte_end":715,"line_start":18,"line_end":18,"column_start":28,"column_end":32,"is_primary":true,"text":[{"text":"use tracing::{error, info, warn};","highlight_start":28,"highlight_end":32}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the unused import","code":null,"level":"help","spans":[{"file_name":"src\\main.rs","byte_start":709,"byte_end":715,"line_start":18,"line_end":18,"column_start":26,"column_end":32,"is_primary":true,"text":[{"text":"use tracing::{error, info, warn};","highlight_start":26,"highlight_end":32}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused import: `warn`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\main.rs:18:28\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m18\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse tracing::{error, info, warn};\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                            \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `tracing::debug`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src\\dexes\\eaglefi.rs","byte_start":327,"byte_end":341,"line_start":10,"line_end":10,"column_start":5,"column_end":19,"is_primary":true,"text":[{"text":"use tracing::debug;","highlight_start":5,"highlight_end":19}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"src\\dexes\\eaglefi.rs","byte_start":323,"byte_end":344,"line_start":10,"line_end":11,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use tracing::debug;","highlight_start":1,"highlight_end":20},{"text":"","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused import: `tracing::debug`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\dexes\\eaglefi.rs:10:5\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m10\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse tracing::debug;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused imports: `debug` and `info`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src\\dexes\\dusa.rs","byte_start":812,"byte_end":817,"line_start":21,"line_end":21,"column_start":15,"column_end":20,"is_primary":true,"text":[{"text":"use tracing::{debug, info};","highlight_start":15,"highlight_end":20}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\dexes\\dusa.rs","byte_start":819,"byte_end":823,"line_start":21,"line_end":21,"column_start":22,"column_end":26,"is_primary":true,"text":[{"text":"use tracing::{debug, info};","highlight_start":22,"highlight_end":26}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"src\\dexes\\dusa.rs","byte_start":798,"byte_end":827,"line_start":21,"line_end":22,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use tracing::{debug, info};","highlight_start":1,"highlight_end":28},{"text":"","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused imports: `debug` and `info`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\dexes\\dusa.rs:21:15\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m21\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse tracing::{debug, info};\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m               \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `HashSet`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src\\executor\\builders.rs","byte_start":32,"byte_end":39,"line_start":1,"line_end":1,"column_start":33,"column_end":40,"is_primary":true,"text":[{"text":"use std::collections::{HashMap, HashSet};","highlight_start":33,"highlight_end":40}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the unused import","code":null,"level":"help","spans":[{"file_name":"src\\executor\\builders.rs","byte_start":30,"byte_end":39,"line_start":1,"line_end":1,"column_start":31,"column_end":40,"is_primary":true,"text":[{"text":"use std::collections::{HashMap, HashSet};","highlight_start":31,"highlight_end":40}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src\\executor\\builders.rs","byte_start":22,"byte_end":23,"line_start":1,"line_end":1,"column_start":23,"column_end":24,"is_primary":true,"text":[{"text":"use std::collections::{HashMap, HashSet};","highlight_start":23,"highlight_end":24}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src\\executor\\builders.rs","byte_start":39,"byte_end":40,"line_start":1,"line_end":1,"column_start":40,"column_end":41,"is_primary":true,"text":[{"text":"use std::collections::{HashMap, HashSet};","highlight_start":40,"highlight_end":41}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused import: `HashSet`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\executor\\builders.rs:1:33\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m1\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse std::collections::{HashMap, HashSet};\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                 \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `format_units`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src\\executor\\builders.rs","byte_start":142,"byte_end":154,"line_start":6,"line_end":6,"column_start":13,"column_end":25,"is_primary":true,"text":[{"text":"    utils::{format_units, parse_units},","highlight_start":13,"highlight_end":25}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the unused import","code":null,"level":"help","spans":[{"file_name":"src\\executor\\builders.rs","byte_start":142,"byte_end":156,"line_start":6,"line_end":6,"column_start":13,"column_end":27,"is_primary":true,"text":[{"text":"    utils::{format_units, parse_units},","highlight_start":13,"highlight_end":27}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src\\executor\\builders.rs","byte_start":141,"byte_end":142,"line_start":6,"line_end":6,"column_start":12,"column_end":13,"is_primary":true,"text":[{"text":"    utils::{format_units, parse_units},","highlight_start":12,"highlight_end":13}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src\\executor\\builders.rs","byte_start":167,"byte_end":168,"line_start":6,"line_end":6,"column_start":38,"column_end":39,"is_primary":true,"text":[{"text":"    utils::{format_units, parse_units},","highlight_start":38,"highlight_end":39}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused import: `format_units`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\executor\\builders.rs:6:13\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m6\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    utils::{format_units, parse_units},\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m             \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused imports: `error` and `info`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src\\executor\\builders.rs","byte_start":196,"byte_end":201,"line_start":8,"line_end":8,"column_start":22,"column_end":27,"is_primary":true,"text":[{"text":"use tracing::{debug, error, info, warn};","highlight_start":22,"highlight_end":27}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\executor\\builders.rs","byte_start":203,"byte_end":207,"line_start":8,"line_end":8,"column_start":29,"column_end":33,"is_primary":true,"text":[{"text":"use tracing::{debug, error, info, warn};","highlight_start":29,"highlight_end":33}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the unused imports","code":null,"level":"help","spans":[{"file_name":"src\\executor\\builders.rs","byte_start":194,"byte_end":207,"line_start":8,"line_end":8,"column_start":20,"column_end":33,"is_primary":true,"text":[{"text":"use tracing::{debug, error, info, warn};","highlight_start":20,"highlight_end":33}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused imports: `error` and `info`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\executor\\builders.rs:8:22\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m8\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse tracing::{debug, error, info, warn};\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                      \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `Pool`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src\\executor\\builders.rs","byte_start":432,"byte_end":436,"line_start":16,"line_end":16,"column_start":18,"column_end":22,"is_primary":true,"text":[{"text":"    types::{DEX, Pool, PoolArb, Token},","highlight_start":18,"highlight_end":22}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the unused import","code":null,"level":"help","spans":[{"file_name":"src\\executor\\builders.rs","byte_start":430,"byte_end":436,"line_start":16,"line_end":16,"column_start":16,"column_end":22,"is_primary":true,"text":[{"text":"    types::{DEX, Pool, PoolArb, Token},","highlight_start":16,"highlight_end":22}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused import: `Pool`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\executor\\builders.rs:16:18\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m16\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    types::{DEX, Pool, PoolArb, Token},\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused imports: `debug`, `info`, and `warn`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src\\executor\\simulation.rs","byte_start":96,"byte_end":101,"line_start":3,"line_end":3,"column_start":15,"column_end":20,"is_primary":true,"text":[{"text":"use tracing::{debug, error, info, warn};","highlight_start":15,"highlight_end":20}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\executor\\simulation.rs","byte_start":110,"byte_end":114,"line_start":3,"line_end":3,"column_start":29,"column_end":33,"is_primary":true,"text":[{"text":"use tracing::{debug, error, info, warn};","highlight_start":29,"highlight_end":33}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\executor\\simulation.rs","byte_start":116,"byte_end":120,"line_start":3,"line_end":3,"column_start":35,"column_end":39,"is_primary":true,"text":[{"text":"use tracing::{debug, error, info, warn};","highlight_start":35,"highlight_end":39}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the unused imports","code":null,"level":"help","spans":[{"file_name":"src\\executor\\simulation.rs","byte_start":96,"byte_end":103,"line_start":3,"line_end":3,"column_start":15,"column_end":22,"is_primary":true,"text":[{"text":"use tracing::{debug, error, info, warn};","highlight_start":15,"highlight_end":22}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src\\executor\\simulation.rs","byte_start":108,"byte_end":120,"line_start":3,"line_end":3,"column_start":27,"column_end":39,"is_primary":true,"text":[{"text":"use tracing::{debug, error, info, warn};","highlight_start":27,"highlight_end":39}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src\\executor\\simulation.rs","byte_start":95,"byte_end":96,"line_start":3,"line_end":3,"column_start":14,"column_end":15,"is_primary":true,"text":[{"text":"use tracing::{debug, error, info, warn};","highlight_start":14,"highlight_end":15}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src\\executor\\simulation.rs","byte_start":120,"byte_end":121,"line_start":3,"line_end":3,"column_start":39,"column_end":40,"is_primary":true,"text":[{"text":"use tracing::{debug, error, info, warn};","highlight_start":39,"highlight_end":40}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused imports: `debug`, `info`, and `warn`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\executor\\simulation.rs:3:15\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m3\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse tracing::{debug, error, info, warn};\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m               \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused imports: `GlobalState`, `PoolArb`, `Pool`, `print_sparse_matrix`, and `serializable::arb_route::ArbRoute`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src\\helpers\\mod.rs","byte_start":444,"byte_end":463,"line_start":13,"line_end":13,"column_start":36,"column_end":55,"is_primary":true,"text":[{"text":"    helpers::display::{find_token, print_sparse_matrix},","highlight_start":36,"highlight_end":55}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\helpers\\mod.rs","byte_start":471,"byte_end":504,"line_start":14,"line_end":14,"column_start":5,"column_end":38,"is_primary":true,"text":[{"text":"    serializable::arb_route::ArbRoute,","highlight_start":5,"highlight_end":38}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\helpers\\mod.rs","byte_start":519,"byte_end":530,"line_start":15,"line_end":15,"column_start":13,"column_end":24,"is_primary":true,"text":[{"text":"    state::{GlobalState, SharedAppState, GLOBAL_START_AMOUNTS},","highlight_start":13,"highlight_end":24}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\helpers\\mod.rs","byte_start":601,"byte_end":605,"line_start":17,"line_end":17,"column_start":13,"column_end":17,"is_primary":true,"text":[{"text":"    types::{Pool, PoolArb, Token, TokenMatrix},","highlight_start":13,"highlight_end":17}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\helpers\\mod.rs","byte_start":607,"byte_end":614,"line_start":17,"line_end":17,"column_start":19,"column_end":26,"is_primary":true,"text":[{"text":"    types::{Pool, PoolArb, Token, TokenMatrix},","highlight_start":19,"highlight_end":26}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the unused imports","code":null,"level":"help","spans":[{"file_name":"src\\helpers\\mod.rs","byte_start":442,"byte_end":463,"line_start":13,"line_end":13,"column_start":34,"column_end":55,"is_primary":true,"text":[{"text":"    helpers::display::{find_token, print_sparse_matrix},","highlight_start":34,"highlight_end":55}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src\\helpers\\mod.rs","byte_start":431,"byte_end":432,"line_start":13,"line_end":13,"column_start":23,"column_end":24,"is_primary":true,"text":[{"text":"    helpers::display::{find_token, print_sparse_matrix},","highlight_start":23,"highlight_end":24}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src\\helpers\\mod.rs","byte_start":463,"byte_end":464,"line_start":13,"line_end":13,"column_start":55,"column_end":56,"is_primary":true,"text":[{"text":"    helpers::display::{find_token, print_sparse_matrix},","highlight_start":55,"highlight_end":56}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src\\helpers\\mod.rs","byte_start":464,"byte_end":504,"line_start":13,"line_end":14,"column_start":56,"column_end":38,"is_primary":true,"text":[{"text":"    helpers::display::{find_token, print_sparse_matrix},","highlight_start":56,"highlight_end":57},{"text":"    serializable::arb_route::ArbRoute,","highlight_start":1,"highlight_end":38}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src\\helpers\\mod.rs","byte_start":519,"byte_end":532,"line_start":15,"line_end":15,"column_start":13,"column_end":26,"is_primary":true,"text":[{"text":"    state::{GlobalState, SharedAppState, GLOBAL_START_AMOUNTS},","highlight_start":13,"highlight_end":26}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src\\helpers\\mod.rs","byte_start":601,"byte_end":616,"line_start":17,"line_end":17,"column_start":13,"column_end":28,"is_primary":true,"text":[{"text":"    types::{Pool, PoolArb, Token, TokenMatrix},","highlight_start":13,"highlight_end":28}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused imports: `GlobalState`, `PoolArb`, `Pool`, `print_sparse_matrix`, and `serializable::arb_route::ArbRoute`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\helpers\\mod.rs:13:36\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m13\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    helpers::display::{find_token, print_sparse_matrix},\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m14\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    serializable::arb_route::ArbRoute,\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m15\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    state::{GlobalState, SharedAppState, GLOBAL_START_AMOUNTS},\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m             \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m16\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    strategies,\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m17\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    types::{Pool, PoolArb, Token, TokenMatrix},\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m             \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `std::sync::Arc`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src\\spawns\\balance_notifier.rs","byte_start":4,"byte_end":18,"line_start":1,"line_end":1,"column_start":5,"column_end":19,"is_primary":true,"text":[{"text":"use std::sync::Arc;","highlight_start":5,"highlight_end":19}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"src\\spawns\\balance_notifier.rs","byte_start":0,"byte_end":21,"line_start":1,"line_end":2,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use std::sync::Arc;","highlight_start":1,"highlight_end":20},{"text":"","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused import: `std::sync::Arc`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\spawns\\balance_notifier.rs:1:5\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m1\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse std::sync::Arc;\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `transport::smtp::authentication::Credentials`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src\\spawns\\balance_notifier.rs","byte_start":83,"byte_end":127,"line_start":4,"line_end":4,"column_start":29,"column_end":73,"is_primary":true,"text":[{"text":"use lettre::{SmtpTransport, transport::smtp::authentication::Credentials};","highlight_start":29,"highlight_end":73}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the unused import","code":null,"level":"help","spans":[{"file_name":"src\\spawns\\balance_notifier.rs","byte_start":81,"byte_end":127,"line_start":4,"line_end":4,"column_start":27,"column_end":73,"is_primary":true,"text":[{"text":"use lettre::{SmtpTransport, transport::smtp::authentication::Credentials};","highlight_start":27,"highlight_end":73}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src\\spawns\\balance_notifier.rs","byte_start":67,"byte_end":68,"line_start":4,"line_end":4,"column_start":13,"column_end":14,"is_primary":true,"text":[{"text":"use lettre::{SmtpTransport, transport::smtp::authentication::Credentials};","highlight_start":13,"highlight_end":14}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src\\spawns\\balance_notifier.rs","byte_start":127,"byte_end":128,"line_start":4,"line_end":4,"column_start":73,"column_end":74,"is_primary":true,"text":[{"text":"use lettre::{SmtpTransport, transport::smtp::authentication::Credentials};","highlight_start":73,"highlight_end":74}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused import: `transport::smtp::authentication::Credentials`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\spawns\\balance_notifier.rs:4:29\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m4\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse lettre::{SmtpTransport, transport::smtp::authentication::Credentials};\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                             \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused imports: `CONFIG` and `state::AppState`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src\\spawns\\balance_notifier.rs","byte_start":267,"byte_end":273,"line_start":10,"line_end":10,"column_start":9,"column_end":15,"is_primary":true,"text":[{"text":"        CONFIG,","highlight_start":9,"highlight_end":15}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\spawns\\balance_notifier.rs","byte_start":409,"byte_end":424,"line_start":14,"line_end":14,"column_start":5,"column_end":20,"is_primary":true,"text":[{"text":"    state::AppState,","highlight_start":5,"highlight_end":20}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the unused imports","code":null,"level":"help","spans":[{"file_name":"src\\spawns\\balance_notifier.rs","byte_start":267,"byte_end":284,"line_start":10,"line_end":11,"column_start":9,"column_end":9,"is_primary":true,"text":[{"text":"        CONFIG,","highlight_start":9,"highlight_end":16},{"text":"        current::{BALANCE_NOTIFIER_INTERVAL, BALANCE_NOTIFIER_THRESHOLD},","highlight_start":1,"highlight_end":9}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src\\spawns\\balance_notifier.rs","byte_start":256,"byte_end":267,"line_start":9,"line_end":10,"column_start":13,"column_end":9,"is_primary":true,"text":[{"text":"    config::{","highlight_start":13,"highlight_end":14},{"text":"        CONFIG,","highlight_start":1,"highlight_end":9}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src\\spawns\\balance_notifier.rs","byte_start":348,"byte_end":356,"line_start":11,"line_end":12,"column_start":73,"column_end":6,"is_primary":true,"text":[{"text":"        current::{BALANCE_NOTIFIER_INTERVAL, BALANCE_NOTIFIER_THRESHOLD},","highlight_start":73,"highlight_end":74},{"text":"    },","highlight_start":1,"highlight_end":6}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src\\spawns\\balance_notifier.rs","byte_start":402,"byte_end":424,"line_start":13,"line_end":14,"column_start":44,"column_end":20,"is_primary":true,"text":[{"text":"    helpers::email::send_email_notification,","highlight_start":44,"highlight_end":45},{"text":"    state::AppState,","highlight_start":1,"highlight_end":20}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused imports: `CONFIG` and `state::AppState`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\spawns\\balance_notifier.rs:10:9\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m10\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        CONFIG,\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m14\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    state::AppState,\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `collections::HashMap`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src\\spawns\\swap_fetcher.rs","byte_start":10,"byte_end":30,"line_start":1,"line_end":1,"column_start":11,"column_end":31,"is_primary":true,"text":[{"text":"use std::{collections::HashMap, sync::Arc};","highlight_start":11,"highlight_end":31}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the unused import","code":null,"level":"help","spans":[{"file_name":"src\\spawns\\swap_fetcher.rs","byte_start":10,"byte_end":32,"line_start":1,"line_end":1,"column_start":11,"column_end":33,"is_primary":true,"text":[{"text":"use std::{collections::HashMap, sync::Arc};","highlight_start":11,"highlight_end":33}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src\\spawns\\swap_fetcher.rs","byte_start":9,"byte_end":10,"line_start":1,"line_end":1,"column_start":10,"column_end":11,"is_primary":true,"text":[{"text":"use std::{collections::HashMap, sync::Arc};","highlight_start":10,"highlight_end":11}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src\\spawns\\swap_fetcher.rs","byte_start":41,"byte_end":42,"line_start":1,"line_end":1,"column_start":42,"column_end":43,"is_primary":true,"text":[{"text":"use std::{collections::HashMap, sync::Arc};","highlight_start":42,"highlight_end":43}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused import: `collections::HashMap`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\spawns\\swap_fetcher.rs:1:11\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m1\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse std::{collections::HashMap, sync::Arc};\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m           \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `GlobalState`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src\\spawns\\swap_fetcher.rs","byte_start":586,"byte_end":597,"line_start":19,"line_end":19,"column_start":23,"column_end":34,"is_primary":true,"text":[{"text":"    state::{AppState, GlobalState, SharedAppState, SharedPool},","highlight_start":23,"highlight_end":34}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the unused import","code":null,"level":"help","spans":[{"file_name":"src\\spawns\\swap_fetcher.rs","byte_start":584,"byte_end":597,"line_start":19,"line_end":19,"column_start":21,"column_end":34,"is_primary":true,"text":[{"text":"    state::{AppState, GlobalState, SharedAppState, SharedPool},","highlight_start":21,"highlight_end":34}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused import: `GlobalState`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\spawns\\swap_fetcher.rs:19:23\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m19\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    state::{AppState, GlobalState, SharedAppState, SharedPool},\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                       \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `sync::Arc`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src\\strategies\\bellman_ford_matrix.rs","byte_start":32,"byte_end":41,"line_start":1,"line_end":1,"column_start":33,"column_end":42,"is_primary":true,"text":[{"text":"use std::{collections::HashMap, sync::Arc};","highlight_start":33,"highlight_end":42}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the unused import","code":null,"level":"help","spans":[{"file_name":"src\\strategies\\bellman_ford_matrix.rs","byte_start":30,"byte_end":41,"line_start":1,"line_end":1,"column_start":31,"column_end":42,"is_primary":true,"text":[{"text":"use std::{collections::HashMap, sync::Arc};","highlight_start":31,"highlight_end":42}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src\\strategies\\bellman_ford_matrix.rs","byte_start":9,"byte_end":10,"line_start":1,"line_end":1,"column_start":10,"column_end":11,"is_primary":true,"text":[{"text":"use std::{collections::HashMap, sync::Arc};","highlight_start":10,"highlight_end":11}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src\\strategies\\bellman_ford_matrix.rs","byte_start":41,"byte_end":42,"line_start":1,"line_end":1,"column_start":42,"column_end":43,"is_primary":true,"text":[{"text":"use std::{collections::HashMap, sync::Arc};","highlight_start":42,"highlight_end":43}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused import: `sync::Arc`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\strategies\\bellman_ford_matrix.rs:1:33\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m1\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse std::{collections::HashMap, sync::Arc};\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                 \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `address`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"src\\spawns\\swap_fetcher.rs","byte_start":8612,"byte_end":8619,"line_start":231,"line_end":231,"column_start":5,"column_end":12,"is_primary":true,"text":[{"text":"    address: &str,","highlight_start":5,"highlight_end":12}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"`#[warn(unused_variables)]` on by default","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"src\\spawns\\swap_fetcher.rs","byte_start":8612,"byte_end":8619,"line_start":231,"line_end":231,"column_start":5,"column_end":12,"is_primary":true,"text":[{"text":"    address: &str,","highlight_start":5,"highlight_end":12}],"label":null,"suggested_replacement":"_address","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused variable: `address`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\spawns\\swap_fetcher.rs:231:5\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m231\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    address: &str,\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: if this is intentional, prefix it with an underscore: `_address`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: `#[warn(unused_variables)]` on by default\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"value assigned to `last_event` is never read","code":{"code":"unused_assignments","explanation":null},"level":"warning","spans":[{"file_name":"src\\executor\\mod.rs","byte_start":12041,"byte_end":12051,"line_start":356,"line_end":356,"column_start":13,"column_end":23,"is_primary":true,"text":[{"text":"            last_event = &event;","highlight_start":13,"highlight_end":23}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"maybe it is overwritten before being read?","code":null,"level":"help","spans":[],"children":[],"rendered":null},{"message":"`#[warn(unused_assignments)]` on by default","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: value assigned to `last_event` is never read\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\executor\\mod.rs:356:13\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m356\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            last_event = &event;\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m             \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mhelp\u001b[0m\u001b[0m: maybe it is overwritten before being read?\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: `#[warn(unused_assignments)]` on by default\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"21 warnings emitted","code":null,"level":"warning","spans":[],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: 21 warnings emitted\u001b[0m\n\n"}
