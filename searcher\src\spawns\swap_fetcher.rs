use std::{collections::HashMap, sync::Arc};

use anyhow::{Context, Result};
use rust_massa_web3::{
    client::grpc_client::PublicGrpcClient,
    massa_proto_rs::massa::{
        api::v1::NewSlotExecutionOutputsRequest,
        model::v1::{ExecutionOutputStatus, OperationExecutionStatus, ScExecutionEvent},
    },
    tokio_stream::{StreamExt, wrappers::ReceiverStream},
    tonic::Request,
};
use tokio::sync::mpsc;
use tracing::{debug, error, info, warn};

use crate::{
    dexes::{dusa, eaglefi},
    helpers::{self, detect_and_execute_arb},
    state::{AppState, GlobalState, SharedAppState, SharedPool},
    strategies,
    types::{DEX, Pool},
};

pub async fn start_swap_fetcher(app_state: Arc<AppState>) -> Result<()> {
    let mut grpc_client = app_state.grpc_client.clone();
    let mut massa_client = grpc_client.client.clone();

    // Create a request stream from the filters (filters still not working on massa. need to be handled manually later)
    let request_stream = NewSlotExecutionOutputsRequest { filters: vec![] };

    let is_event_fetcher_final = false;

    let event_fetcher_condition_status = if is_event_fetcher_final {
        info!("Swap event fetcher working in final state");
        ExecutionOutputStatus::Final as i32
    } else {
        info!("Swap event fetcher working in candidate state");
        ExecutionOutputStatus::Candidate as i32
    };

    let execution_status = event_fetcher_condition_status;

    let (tx, rx) = mpsc::channel(128);

    let ack = ReceiverStream::new(rx);

    let response = massa_client
        .new_slot_execution_outputs(Request::new(ack))
        .await
        .context("Failed to get new slot execution outputs response from massa client")?;

    tx.send(request_stream)
        .await
        .context("Failed to send request stream")?;

    let mut response_stream = response.into_inner();

    info!("Start waitiing for an arbitrage opportunity...");

    while let Some(response) = response_stream.next().await {
        let new_slot_execution_output =
            response.context("Failed to get message from slot execution stream.")?;

        // Test error
        // return Err(anyhow::anyhow!("Test in swap fetcher slot execution output"));

        let slot_execution_output = new_slot_execution_output
            .output
            .context("Failed to get the output of the slot slot execution.")?;

        // Check if the slot execution output is a the appropriate status
        if slot_execution_output.status != execution_status {
            // Skip this slot execution output because it is not the appropriate status for the event fetcher
            continue;
        }

        let execution_output = slot_execution_output
            .execution_output
            .context("Failed to get execution output from slot execution output response")?;

        let states_changes = execution_output
            .state_changes
            .context("Failed to get states changes from execution output response")?;

        let slot_events = execution_output.events;

        if slot_events.is_empty() {
            // warn!("No events found in slot execution output");
            continue;
        }

        let executed_ops_changes = states_changes.executed_ops_changes;

        // for each executed ops change inside the executed ops changes
        for executed_ops_change in executed_ops_changes {
            let operation_id = executed_ops_change.operation_id;

            let executed_ops_change_value = executed_ops_change
                .value
                .context("Failed to get executed ops change value")?;

            // Skip if the operation status is not success
            if executed_ops_change_value.status != OperationExecutionStatus::Success as i32 {
                warn!("Operation {} is not successful, skipping", operation_id);
                continue;
            }

            // Handle the operation events
            handle_process_operation_events(
                &operation_id,
                &slot_events,
                app_state.clone(),
                &mut grpc_client,
            )
            .await
            .context("Failed to handle the operation events")?;
        }
    }

    Ok(())
}

/// Handles the operation events for the given operation id
/// it takes the slot events and filter the one that are relevant to the operation id
/// then it process each event by calling the `process_swap_event` function
/// if the event is the last event of the operation it will also call the `process_swap_event` function with the `is_detecting_arb` flag set to true
async fn handle_process_operation_events(
    operation_id: &str,
    slot_events: &[ScExecutionEvent],
    app_state: SharedAppState,
    grpc_client: &mut PublicGrpcClient,
) -> Result<()> {
    // info!("Processing normal operation {}", operation_id);

    let pools_hash_map = &app_state.pools;

    // Find events from this operation

    let swap_operation_events: Vec<&ScExecutionEvent> = slot_events
        .iter()
        .filter(|event| {
            // Get the event details from the event
            let event_details = helpers::massa::extract_event_details(&event);

            if event_details.operation_id == Some(operation_id.to_owned()) {
                if event_details.event_data.starts_with("SWAP:") {
                    return true;
                }
            }

            false
        })
        .collect();

    // Process operation events if needed
    if !swap_operation_events.is_empty() {
        let len_swap_operation_events = swap_operation_events.len();

        info!(
            "Found {} Swap events from the operation {}. Processing them...",
            len_swap_operation_events, &operation_id
        );

        for (event_index, sc_event) in swap_operation_events.iter().enumerate() {
            let event_details = helpers::massa::extract_event_details(sc_event);

            if event_index == len_swap_operation_events - 1 {
                debug!("Processing last event of the operation {}...", operation_id);

                for address in event_details.call_stack.clone() {
                    if !address.starts_with("AS") {
                        continue;
                    }

                    if let Some(shared_pool) = pools_hash_map.get(&address).cloned() {
                        match process_swap_event(
                            sc_event,
                            app_state.clone(),
                            grpc_client,
                            shared_pool,
                            &address,
                            true,
                        )
                        .await
                        {
                            Ok(_) => {
                                info!("Swap event processed successfully");
                            }
                            Err(e) => {
                                error!("Process swap event failed.will skip it. Error: {}", e);
                            }
                        };
                    } else {
                        warn!("Pool {} not found in the pools hash map", address);
                        continue;
                    }
                }
            } else {
                for address in event_details.call_stack.clone() {
                    if let Some(shared_pool) = pools_hash_map.get(&address).cloned() {
                        match process_swap_event(
                            sc_event,
                            app_state.clone(),
                            grpc_client,
                            shared_pool,
                            &address,
                            false,
                        )
                        .await
                        {
                            Ok(_) => {
                                info!("Swap event processed successfully");
                            }
                            Err(e) => {
                                error!("Process swap event failed.will skip it. Error: {}", e);
                            }
                        };
                    }
                }
            }
        }
    }
    return Ok(());
}

async fn process_swap_event(
    _event: &ScExecutionEvent,
    app_state: SharedAppState,
    mut grpc_client: &mut PublicGrpcClient,
    shared_pool: SharedPool,
    address: &str,
    is_detecting_arb: bool,
) -> Result<()> {
    let mut updated_pool = Pool::default();

    let mut token_matrix_guard = app_state.token_matrix.lock().await;

    let mut pool_gurad = shared_pool.lock().await;

    if pool_gurad.dex == DEX::EagleFi {
        info!("New detected swap on pool: {}", pool_gurad.address);
        // Update the pool data in the hash map
        updated_pool = eaglefi::update_pool_data(&pool_gurad, &mut grpc_client)
            .await
            .context("Failed to update pool data")?;

        // Update the pool in the hash map
        *pool_gurad = updated_pool.clone();
    } else if pool_gurad.dex == DEX::Dusa {
        info!("New detected swap on pool: {}", pool_gurad.address);
        // Update the pool data in the hash map
        updated_pool = dusa::update_pool_data(&pool_gurad, &mut grpc_client)
            .await
            .context("Failed to  update pool data")?;

        // Update the pool in the hash map
        *pool_gurad = updated_pool.clone();
    }

    if !updated_pool.address.is_empty() {
        // Update the sparse matrix with the new pool data
        let new_matrix = strategies::bellman_ford_matrix::update_sparse_matrix_by_pool(
            token_matrix_guard.clone(),
            &updated_pool,
        )
        .await;

        // Update the token matrix
        *token_matrix_guard = new_matrix;

        // info!("Displaying the sparse matrix after update...");
        // Display the sparse matrix after
        // print_sparse_matrix(&token_matrix);

        if !is_detecting_arb {
            warn!(
                "Skipping arbitrage detection after updating the sparse matrix because of an order from the events stream"
            );
            return Ok(());
        }

        // Drop the used mutexes
        drop(token_matrix_guard);

        // Wait for the arbitrage to be executed instead of opening a new thread for each arbitrage
        tokio::spawn(async move {
            if let Err(e) = detect_and_execute_arb(app_state).await {
                error!("Error in detect_and_execute_arb: {}", e);
            }
        });
    }

    Ok(())
}
