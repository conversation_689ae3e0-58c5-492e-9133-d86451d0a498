[package]
name = "quedge"
version = "0.1.0"
edition = "2024"

[dependencies]
dotenvy = "0.15.7"
tokio = "1.44.2"
# Add the local library as a path dependency
# rust-massa-web3 = { path = "../../rust-massa-web3" }
rust-massa-web3 = { git = "https://github.com/NaDasai/rust-massa-web3.git", branch = "dev" }
anyhow = "1.0.98"
sprs = "0.11.3"
byteorder = "1.5.0"
tracing = "0.1.41"
tracing-appender = "0.2.3"
tracing-subscriber = { version = "0.3.19", features = ["env-filter"] }
once_cell = "1.21.3"
rustyline = "15.0.0"
petgraph = "0.8.1"
csv = "1.3.1"
serde = { version = "1.0.219", features = ["derive"] }
chrono = "0.4.41"
lettre = "0.11.16"
rustls-native-certs = "0.8.1"
toml = "0.8.23"
futures = "0.3.31"


[features]
default = ["mainnet"]
buildnet = []
mainnet = []
