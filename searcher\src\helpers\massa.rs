use anyhow::Result;
use rust_massa_web3::{
    alloy_primitives::U256, basic_elements::serializers::bytes_to_u256, client::grpc_client::PublicGrpcClient, massa_proto_rs::massa::model::v1::ScExecutionEvent, types::{event::EventDetails, ReadStorageKey}
};

/// Gets the token allowance for a specific owner-spender pair
///
/// # Arguments
///
/// * `grpc_client` - A mutable reference to the GRPC client used to interact with the Massa network
/// * `token_address` - The address of the token contract
/// * `owner_address` - The address of the token owner
/// * `spender_address` - The address of the account allowed to spend tokens
///
/// # Returns
///
/// * `Result<U256>` - The allowance amount as a U256 value if successful, or an error if the operation fails
pub async fn get_token_allowance(
    grpc_client: &mut PublicGrpcClient,
    token_address: &str,
    owner_address: &str,
    spender_address: &str,
) -> Result<U256> {
    let allowance_key = format!("ALLOWANCE{}{}", owner_address, spender_address);

    let result_datastores = grpc_client
        .read_storage_key(vec![ReadStorageKey {
            smart_contract_address: token_address.to_string(),
            key: allowance_key,
        }])
        .await?;

    let allowance = bytes_to_u256(&result_datastores[0].candidate_value);

    Ok(allowance)
}

// Extract informations from event and returns it as an EventDetails struct
pub fn extract_event_details(event: &ScExecutionEvent) -> EventDetails {
    let event_context = event.context.as_ref().unwrap();
    let origin_slot = event_context.origin_slot.as_ref().unwrap();

    EventDetails {
        event_id: format!(
            "{}_{}_{}",
            origin_slot.period, origin_slot.thread, event_context.index_in_slot
        ),
        event_period: origin_slot.period,
        event_thread: origin_slot.thread,
        event_index: event_context.index_in_slot,
        operation_id: event_context.origin_operation_id.clone(),
        event_data: String::from_utf8(event.data.clone()).unwrap_or_default(),
        is_failure: event_context.is_failure,
        call_stack: event_context.call_stack.clone(),
        status: event_context.status,
    }
}

// Extract informations from event and returns it as an EventDetails struct
pub fn extract_event_details_from_event_id(event_id: String) -> EventDetails {
    // If event_id is "0", return a default EventDetails struct with all fields set to 0 or empty string
    if event_id == "0" {
        return EventDetails::default();
    }

    // Else Split the event_id by "_" to get the period, thread, and index
    let parts: Vec<&str> = event_id.split('_').collect();

    let period = parts[0].parse::<u64>().unwrap();
    let thread = parts[1].parse::<u32>().unwrap();
    let index = parts[2].parse::<u64>().unwrap();

    EventDetails {
        event_id,
        event_period: period,
        event_thread: thread,
        event_index: index,
        operation_id: None,
        event_data: String::new(),
        is_failure: false,
        call_stack: Vec::new(),
        status: 0,
    }
}
