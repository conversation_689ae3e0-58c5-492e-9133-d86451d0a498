use once_cell::sync::Lazy;

#[cfg(feature = "buildnet")]
pub mod current {
    pub use super::buildnet::*;
}

#[cfg(feature = "mainnet")]
pub mod current {
    pub use super::mainnet::*;
}

pub mod buildnet;
pub mod mainnet;

#[derive(Debug, Clone)]
pub struct Config {
    pub is_arb_execute: bool,
    pub is_cmd: bool,
    pub mailer_username: String,
    pub mailer_password: String,
    pub admin_email: String,
    pub chain: String,
}

impl Config {
    pub fn load() -> Self {
        dotenvy::dotenv().ok();

        let is_arb_execute =
            std::env::var("IS_ARB_EXECUTE").unwrap_or("false".to_string()) == "true";
        let is_cmd = std::env::var("IS_CMD").unwrap_or("false".to_string()) == "true";
        let mailer_username =
            std::env::var("MAILER_USERNAME").expect("Missing MAILER_USERNAME env var");
        let mailer_password =
            std::env::var("MAILER_PASSWORD").expect("Missing MAILER_PASSWORD env var");
        let admin_email = std::env::var("ADMIN_EMAIL").expect("Missing ADMIN_EMAIL env var");
        let chain = std::env::var("CHAIN").unwrap_or("buildnet".to_string());

        Self {
            is_arb_execute,
            is_cmd,
            mailer_username,
            mailer_password,
            admin_email,
            chain,
        }
    }
}

// Define a globally accessible static Config instance
pub static CONFIG: Lazy<Config> = Lazy::new(Config::load);
