# Massa Arbitrage Searcher

This is a searcher bot for finding arbitrage opportunities on Massa blockchain DEXes.

## Features

The searcher supports different massa chains and can be configured to execute arbitrage opportunities.

### 1. Massa Mainnet Arbitrage (The Default feature)

```bash
cargo run --no-default-features --features mainnet
```

### 2. Massa Buildnet Arbitrage

```bash
cargo run --no-default-features --features buildnet
```

## Configuration

The searcher uses environment variables for configuration. Copy `.env.example` to `.env` and adjust the values:

- `IS_ARB_EXECUTE`: Set to true to execute found arbitrage opportunities
- `IS_CMD`: Set to true to enable command line interface
- `IS_INCREASE_ALLOWENCE`: Set to true to increase allowance for the searcher
- `MASSA_GRPC_URL`: The URL of the Massa node
- `PRIVATE_KEY`: The private key of the searcher
- `MAILER_USERNAME`: The username of the email account
- `MAILER_PASSWORD`: The password of the email account
- `ADMIN_EMAIL`: The email address of the admin
- `CHAIN`: The chain to use (mainnet or buildnet)

## Logging

Logs are written to `logs/quedge.log` with daily rotation enabled.

All arbitrage opportunities are logged to `arb_history/arb_<timestamp>.csv`.
