use std::collections::BTreeMap;

use anyhow::{Context, Result};
use rust_massa_web3::alloy_primitives::U256;
use rust_massa_web3::alloy_primitives::utils::{format_units, parse_units};
use rust_massa_web3::basic_elements::serializers::bytes_to_u256;
use rust_massa_web3::constants::MAX_GAS_CALL;
use rust_massa_web3::massa_proto_rs::massa::api::v1::ExecuteReadOnlyCallRequest;
use rust_massa_web3::massa_proto_rs::massa::model::v1::read_only_execution_call::Target;
use rust_massa_web3::massa_proto_rs::massa::model::v1::{
    FunctionCall, NativeAmount, ReadOnlyExecutionCall,
};
use rust_massa_web3::{
    basic_elements::{
        args::Args,
        serializers::{bytes_to_string, bytes_to_u8},
    },
    client::grpc_client::PublicGrpcClient,
    types::ReadStorageKey,
};
use tracing::{debug, info};

use crate::config::current::{DEFAULT_WALLET_ADDRESS, DUSA_SWAP_ROUTER_ADDRESS};
use crate::types::Bin;
use crate::{
    helpers::dusa::get_a_token_price_from_bin_id,
    types::{DEX, Pool, Token},
};

pub async fn get_pool_data(pool_address: &str, grpc_client: &mut PublicGrpcClient) -> Result<Pool> {
    let a_token_storage_key = ReadStorageKey {
        smart_contract_address: pool_address.to_string(),
        key: "TOKEN_X".to_string(),
    };

    let b_token_storage_key = ReadStorageKey {
        smart_contract_address: pool_address.to_string(),
        key: "TOKEN_Y".to_string(),
    };

    let pair_info_storage_key = ReadStorageKey {
        smart_contract_address: pool_address.to_string(),
        key: "PAIR_INFORMATION".to_string(),
    };

    let fees_parameters_storage_key = ReadStorageKey {
        smart_contract_address: pool_address.to_string(),
        key: "FEES_PARAMETERS".to_string(),
    };

    let storage_keys = vec![
        a_token_storage_key,
        b_token_storage_key,
        pair_info_storage_key,
        fees_parameters_storage_key,
    ];

    let result = grpc_client
        .read_storage_key(storage_keys)
        .await
        .context("Failed to read storage keys from dusa pool")?;

    let a_token_address = bytes_to_string(&result[0].candidate_value);
    let b_token_address = bytes_to_string(&result[1].candidate_value);
    let pair_info_serialised = result[2].candidate_value.clone();
    let fees_parameters_serialised = result[3].candidate_value.clone();

    // Deserialize the pair info
    let mut args = Args::from_bytes(pair_info_serialised);

    let active_bin_id = args.next_u32().context("Failed to get activeId")?;
    let a_reserve_u256 = args.next_u256().context("Failed to get a reserve")?;
    let b_reserve_u256 = args.next_u256().context("Failed to get b reserve")?;

    // Deserialize the fees parameters
    let mut args = Args::from_bytes(fees_parameters_serialised);
    let pair_bin_step = args.next_u32().context("Failed to get pair bin step")?;

    // Get those tokens info
    let a_token_name_storage_key = ReadStorageKey {
        smart_contract_address: a_token_address.clone(),
        key: "NAME".to_string(),
    };

    let a_token_symbol_storage_key = ReadStorageKey {
        smart_contract_address: a_token_address.clone(),
        key: "SYMBOL".to_string(),
    };

    let a_token_decimals_storage_key = ReadStorageKey {
        smart_contract_address: a_token_address.clone(),
        key: "DECIMALS".to_string(),
    };

    let b_token_name_storage_key = ReadStorageKey {
        smart_contract_address: b_token_address.clone(),
        key: "NAME".to_string(),
    };

    let b_token_symbol_storage_key = ReadStorageKey {
        smart_contract_address: b_token_address.clone(),
        key: "SYMBOL".to_string(),
    };

    let b_token_decimals_storage_key = ReadStorageKey {
        smart_contract_address: b_token_address.clone(),
        key: "DECIMALS".to_string(),
    };

    // Storage that returns the active_bin_detaiils
    let active_bin_details_storage_key = ReadStorageKey {
        smart_contract_address: pool_address.to_string(),
        key: format!("bin::{}", active_bin_id),
    };

    let prev_bin_id = active_bin_id - 1;
    let next_bin_id = active_bin_id + 1;

    // Storage that returns the prev bin details
    let prev_bin_details_storage_key = ReadStorageKey {
        smart_contract_address: pool_address.to_string(),
        key: format!("bin::{}", prev_bin_id),
    };

    // Storage that returns the next bin details
    let next_bin_details_storage_key = ReadStorageKey {
        smart_contract_address: pool_address.to_string(),
        key: format!("bin::{}", next_bin_id),
    };

    let storage_keys = vec![
        a_token_name_storage_key,
        a_token_symbol_storage_key,
        a_token_decimals_storage_key,
        b_token_name_storage_key,
        b_token_symbol_storage_key,
        b_token_decimals_storage_key,
        active_bin_details_storage_key,
        prev_bin_details_storage_key,
        next_bin_details_storage_key,
    ];

    let result = grpc_client
        .read_storage_key(storage_keys)
        .await
        .context("Failed to read storage keys from dusa tokens")?;

    let a_token_name = bytes_to_string(&result[0].candidate_value);
    let a_token_symbol = bytes_to_string(&result[1].candidate_value);
    let a_token_decimals = bytes_to_u8(&result[2].candidate_value);
    let b_token_name = bytes_to_string(&result[3].candidate_value);
    let b_token_symbol = bytes_to_string(&result[4].candidate_value);
    let b_token_decimals = bytes_to_u8(&result[5].candidate_value);
    let active_bin_details_serialised = result[6].candidate_value.clone();
    let prev_bin_details_serialised = result[7].candidate_value.clone();
    let next_bin_details_serialised = result[8].candidate_value.clone();

    let a_price = get_a_token_price_from_bin_id(
        active_bin_id as f64,
        pair_bin_step as f64,
        a_token_decimals as i32,
        b_token_decimals as i32,
    );

    let b_price = 1.0 / a_price;

    let a_reserve: f64 = format_units(a_reserve_u256, a_token_decimals)
        .context("Failed to format a reserve")?
        .parse()
        .context("Failed to parse a reserve")?;

    let b_reserve: f64 = format_units(b_reserve_u256, b_token_decimals)
        .context("Failed to format b reserve")?
        .parse()
        .context("Failed to parse b reserve")?;

    let active_a_reserve: f64;
    let active_b_reserve: f64;

    if active_bin_details_serialised.is_empty() {
        active_a_reserve = 0.0;
        active_b_reserve = 0.0;
    } else {
        let mut args = Args::from_bytes(active_bin_details_serialised);

        let active_a_reserve_u256 = args
            .next_u256()
            .context("Failed to get a reserve of active bin")?;
        let active_b_reserve_u256 = args
            .next_u256()
            .context("Failed to get b reserve of active bin")?;

        active_a_reserve = format_units(active_a_reserve_u256, a_token_decimals)
            .context("Failed to format a reserve of active bin")?
            .parse()
            .context("Failed to parse a reserve of active bin")?;

        active_b_reserve = format_units(active_b_reserve_u256, b_token_decimals)
            .context("Failed to format b reserve of active bin")?
            .parse()
            .context("Failed to parse b reserve of active bin")?;
    }

    let prev_a_reserve: f64;
    let prev_b_reserve: f64;

    if prev_bin_details_serialised.is_empty() {
        prev_a_reserve = 0.0;
        prev_b_reserve = 0.0;
    } else {
        let mut args = Args::from_bytes(prev_bin_details_serialised);

        let prev_a_reserve_u256 = args.next_u256().context("Failed to get prev a reserve")?;
        let prev_b_reserve_u256 = args.next_u256().context("Failed to get prev b reserve")?;

        prev_a_reserve = format_units(prev_a_reserve_u256, a_token_decimals)
            .context("Failed to format prev a reserve")?
            .parse()
            .context("Failed to parse prev a reserve")?;

        prev_b_reserve = format_units(prev_b_reserve_u256, b_token_decimals)
            .context("Failed to format prev b reserve")?
            .parse()
            .context("Failed to parse prev b reserve")?;
    }

    let next_a_reserve: f64;
    let next_b_reserve: f64;

    if next_bin_details_serialised.is_empty() {
        next_a_reserve = 0.0;
        next_b_reserve = 0.0;
    } else {
        let mut args = Args::from_bytes(next_bin_details_serialised);

        let next_a_reserve_u256 = args.next_u256().context("Failed to get next a reserve")?;
        let next_b_reserve_u256 = args.next_u256().context("Failed to get next b reserve")?;

        next_a_reserve = format_units(next_a_reserve_u256, a_token_decimals)
            .context("Failed to format next a reserve")?
            .parse()
            .context("Failed to parse next a reserve")?;

        next_b_reserve = format_units(next_b_reserve_u256, b_token_decimals)
            .context("Failed to format next b reserve")?
            .parse()
            .context("Failed to parse next b reserve")?;
    }

    let mut bins = BTreeMap::new();

    // Insert the active bin to the bins map
    bins.insert(
        active_bin_id,
        Bin {
            id: active_bin_id,
            a_reserve: active_a_reserve,
            b_reserve: active_b_reserve,
        },
    );

    // Insert the prev and next bins to the bins map also
    bins.insert(
        prev_bin_id,
        Bin {
            id: prev_bin_id,
            a_reserve: prev_a_reserve,
            b_reserve: prev_b_reserve,
        },
    );

    bins.insert(
        next_bin_id,
        Bin {
            id: next_bin_id,
            a_reserve: next_a_reserve,
            b_reserve: next_b_reserve,
        },
    );

    // info!(
    //     "LOAD: Bins details of pool {}: {:?}",
    //     pool_address,
    //     bins.values()
    // );

    Ok(Pool {
        address: pool_address.to_string(),
        a_token: Token {
            address: a_token_address,
            name: a_token_name,
            symbol: a_token_symbol,
            decimals: a_token_decimals,
            dex: DEX::Dusa,
        },
        b_token: Token {
            address: b_token_address,
            name: b_token_name,
            symbol: b_token_symbol,
            decimals: b_token_decimals,
            dex: DEX::Dusa,
        },
        a_reserve,
        b_reserve,
        dex: DEX::Dusa,
        swap_fee: 0.0,
        a_price,
        b_price,
        active_bin_id,
        bin_step: pair_bin_step,
        bins,
    })
}

pub async fn update_pool_data(pool: &Pool, grpc_client: &mut PublicGrpcClient) -> Result<Pool> {
    let pool_address = pool.address.clone();
    let pair_info_storage_key = ReadStorageKey {
        smart_contract_address: pool_address.to_string(),
        key: "PAIR_INFORMATION".to_string(),
    };

    let fees_parameters_storage_key = ReadStorageKey {
        smart_contract_address: pool_address.to_string(),
        key: "FEES_PARAMETERS".to_string(),
    };

    let storage_keys = vec![pair_info_storage_key, fees_parameters_storage_key];

    let result = grpc_client
        .read_storage_key(storage_keys)
        .await
        .context("Failed to read storage keys from dusa tokens")?;

    let pair_info_serialised = result[0].candidate_value.clone();
    let fees_parameters_serialised = result[1].candidate_value.clone();

    let a_token_decimals = pool.a_token.decimals;
    let b_token_decimals = pool.b_token.decimals;

    // Deserialize the pair info
    let mut args = Args::from_bytes(pair_info_serialised);
    let active_bin_id = args.next_u32().context("Failed to get activeId")?;
    let a_reserve_u256 = args.next_u256().context("Failed to get a reserve")?;
    let b_reserve_u256 = args.next_u256().context("Failed to get b reserve")?;

    let a_reserve: f64 = format_units(a_reserve_u256, a_token_decimals)
        .context("Failed to format a reserve")?
        .parse()
        .context("Failed to parse a reserve")?;

    let b_reserve: f64 = format_units(b_reserve_u256, b_token_decimals)
        .context("Failed to format b reserve")?
        .parse()
        .context("Failed to parse b reserve")?;

    // Deserialize the fees parameters
    let mut args = Args::from_bytes(fees_parameters_serialised);
    let pair_bin_step = args.next_u32().context("Failed to get pair bin step")?;

    let a_price = get_a_token_price_from_bin_id(
        active_bin_id as f64,
        pair_bin_step as f64,
        pool.a_token.decimals as i32,
        pool.b_token.decimals as i32,
    );

    let b_price = 1.0 / a_price;

    // Storage that returns the active_bin_detaiils
    let active_bin_details_storage_key = ReadStorageKey {
        smart_contract_address: pool_address.to_string(),
        key: format!("bin::{}", active_bin_id),
    };

    let prev_bin_id = active_bin_id - 1;
    let next_bin_id = active_bin_id + 1;

    // Storage that returns the prev bin details
    let prev_bin_details_storage_key = ReadStorageKey {
        smart_contract_address: pool_address.to_string(),
        key: format!("bin::{}", prev_bin_id),
    };

    // Storage that returns the next bin details
    let next_bin_details_storage_key = ReadStorageKey {
        smart_contract_address: pool_address.to_string(),
        key: format!("bin::{}", next_bin_id),
    };

    let storage_keys = vec![
        active_bin_details_storage_key,
        prev_bin_details_storage_key,
        next_bin_details_storage_key,
    ];

    // Read the storage keys from the massa client
    let result = grpc_client
        .read_storage_key(storage_keys)
        .await
        .context("Failed to read storage keys from dusa pool")?;

    let active_bin_details_serialised = result[0].candidate_value.clone();
    let prev_bin_details_serialised = result[1].candidate_value.clone();
    let next_bin_details_serialised = result[2].candidate_value.clone();

    let active_a_reserve: f64;
    let active_b_reserve: f64;
    let prev_a_reserve: f64;
    let prev_b_reserve: f64;
    let next_a_reserve: f64;
    let next_b_reserve: f64;

    if active_bin_details_serialised.is_empty() {
        active_a_reserve = 0.0;
        active_b_reserve = 0.0;
    } else {
        let mut args = Args::from_bytes(active_bin_details_serialised);

        let active_a_reserve_u256 = args
            .next_u256()
            .context("Failed to get a reserve of active bin")?;
        let active_b_reserve_u256 = args
            .next_u256()
            .context("Failed to get b reserve of active bin")?;

        active_a_reserve = format_units(active_a_reserve_u256, pool.a_token.decimals)
            .context("Failed to format a reserve of active bin")?
            .parse()
            .context("Failed to parse a reserve of active bin")?;

        active_b_reserve = format_units(active_b_reserve_u256, pool.b_token.decimals)
            .context("Failed to format b reserve of active bin")?
            .parse()
            .context("Failed to parse b reserve of active bin")?;
    }

    if prev_bin_details_serialised.is_empty() {
        prev_a_reserve = 0.0;
        prev_b_reserve = 0.0;
    } else {
        let mut args = Args::from_bytes(prev_bin_details_serialised);

        let prev_a_reserve_u256 = args
            .next_u256()
            .context("Failed to get prev a reserve of active bin")?;
        let prev_b_reserve_u256 = args
            .next_u256()
            .context("Failed to get prev b reserve of active bin")?;

        prev_a_reserve = format_units(prev_a_reserve_u256, pool.a_token.decimals)
            .context("Failed to format prev a reserve of active bin")?
            .parse()
            .context("Failed to parse prev a reserve of active bin")?;
        prev_b_reserve = format_units(prev_b_reserve_u256, pool.b_token.decimals)
            .context("Failed to format prev b reserve of active bin")?
            .parse()
            .context("Failed to parse prev b reserve of active bin")?;
    }

    if next_bin_details_serialised.is_empty() {
        next_a_reserve = 0.0;
        next_b_reserve = 0.0;
    } else {
        let mut args = Args::from_bytes(next_bin_details_serialised);

        let next_a_reserve_u256 = args
            .next_u256()
            .context("Failed to get next a reserve of active bin")?;
        let next_b_reserve_u256 = args
            .next_u256()
            .context("Failed to get next b reserve of active bin")?;

        next_a_reserve = format_units(next_a_reserve_u256, pool.a_token.decimals)
            .context("Failed to format next a reserve of active bin")?
            .parse()
            .context("Failed to parse next a reserve of active bin")?;

        next_b_reserve = format_units(next_b_reserve_u256, pool.b_token.decimals)
            .context("Failed to format next b reserve of active bin")?
            .parse()
            .context("Failed to parse next b reserve of active bin")?;
    }

    // Create a new bins map containing only the active bin to ensure accurate, up-to-date liquidity information
    // This prevents stale data from inactive bins from being carried forward
    let mut updated_bins = BTreeMap::new();

    updated_bins.insert(
        active_bin_id,
        Bin {
            id: active_bin_id,
            a_reserve: active_a_reserve,
            b_reserve: active_b_reserve,
        },
    );

    // Insert the prev and next bins to the bins map also
    updated_bins.insert(
        prev_bin_id,
        Bin {
            id: prev_bin_id,
            a_reserve: prev_a_reserve,
            b_reserve: prev_b_reserve,
        },
    );

    updated_bins.insert(
        next_bin_id,
        Bin {
            id: next_bin_id,
            a_reserve: next_a_reserve,
            b_reserve: next_b_reserve,
        },
    );

    // info!(
    //     "UPDATE: Bins details of pool {}: {:?}",
    //     pool_address,
    //     updated_bins.values()
    // );

    let mut updated_pool = pool.clone();

    updated_pool.a_price = a_price;
    updated_pool.b_price = b_price;
    updated_pool.active_bin_id = active_bin_id;
    updated_pool.bin_step = pair_bin_step;
    updated_pool.a_reserve = a_reserve;
    updated_pool.b_reserve = b_reserve;
    updated_pool.bins = updated_bins;

    Ok(updated_pool)
}

/// Get the amount of tokens that will be received when swapping a given amount of tokens
pub async fn get_onchain_swap_out_estimation(
    pool: &Pool,
    amount_in: f64,
    is_a_in: bool,
    grpc_client: &mut PublicGrpcClient,
) -> Result<f64> {
    // info!(
    //     "Getting dusa on chanin swap out estimation for pool: {} , token_in_address: {}, amount_in: {}, token_out_address: {}",
    //     pool.address, pool.a_token.address, amount_in, pool.b_token.address
    // );

    let mut massa_client = grpc_client.client.clone();

    let (token_in, token_out) = if is_a_in {
        (&pool.a_token, &pool.b_token)
    } else {
        (&pool.b_token, &pool.a_token)
    };

    let parsed_amount_in: U256 = parse_units(&amount_in.to_string(), token_in.decimals)
        .context("Failed to parse amount in")?
        .try_into()
        .context("Failed to convert parsed units to U256")?;

    let mut args = Args::new();

    args.add_string(&pool.address);
    args.add_u256(parsed_amount_in);
    args.add_bool(is_a_in);

    let request = ExecuteReadOnlyCallRequest {
        call: Some(ReadOnlyExecutionCall {
            target: Some(Target::FunctionCall(FunctionCall {
                target_address: DUSA_SWAP_ROUTER_ADDRESS.to_string(),
                target_function: "getSwapOut".to_string(),
                parameter: args.serialize(),
                coins: None,
            })),
            caller_address: Some(DEFAULT_WALLET_ADDRESS.to_string()),
            fee: Some(NativeAmount {
                mantissa: 10000000,
                scale: 9,
            }),
            max_gas: MAX_GAS_CALL,
            call_stack: vec![],
        }),
    };

    let response = massa_client.execute_read_only_call(request).await?;

    let execution_query_response = response.into_inner().output.unwrap_or_default();

    let amount_out_u256 = bytes_to_u256(&execution_query_response.call_result);

    // Format the amount out to f64
    let amount_out: f64 = format_units(amount_out_u256, token_out.decimals)
        .context("Failed to format amount out after estimation dusa")?
        .parse()
        .context("Failed to parse amount out as f64 after estimation dusa")?;

    // debug!(
    //     "Dusa Estimate amount out for pool: {} , token_in_address: {}, amount_in: {}, token_out_address: {}, amount_out: {}",
    //     pool.address, token_in.address, amount_in, token_out.address, amount_out
    // );

    Ok(amount_out)
}
