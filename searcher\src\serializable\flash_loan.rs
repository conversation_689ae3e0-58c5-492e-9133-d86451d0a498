use anyhow::{Context, Result, bail};
use byteorder::{<PERSON><PERSON><PERSON><PERSON>, ReadBytesExt};
use rust_massa_web3::{
    alloy_primitives::U256,
    basic_elements::args::{Args, BYTES_256_OFFSET, Serializable},
};
use std::io::{<PERSON>urs<PERSON>, <PERSON>};

#[derive(Debug, PartialEq, Clone)]
pub struct FlashLoan {
    pub pool_address: String,
    pub token_address: String,
    pub amount: U256,
}

impl Serializable for FlashLoan {
    fn serialize(&self) -> Vec<u8> {
        // We can use a temporary Args instance to leverage its add_* methods
        // or serialize manually using WriteBytesExt. Using Args is convenient here.
        let mut temp_args = Args::new(); // Assuming Args is in super::args
        // If Args is in the same module, just Args::new()

        temp_args.add_string(&self.pool_address);
        temp_args.add_string(&self.token_address);
        temp_args.add_u256(self.amount);

        temp_args.serialize() // This returns the Vec<u8> from the temp_args
    }

    fn deserialize(data: &[u8], offset: usize) -> Result<(Self, usize)> {
        if offset > data.len() {
            bail!(
                "Initial offset {} is out of bounds for data length {}",
                offset,
                data.len()
            );
        }

        // Use a cursor over the relevant part of the data slice
        let mut cursor = Cursor::new(&data[offset..]);

        // Helper to read a string from the cursor
        // Assumes string is length-prefixed (u32) then UTF-8 bytes
        let read_string_from_cursor = |c: &mut Cursor<&[u8]>| -> Result<String> {
            let len = c
                .read_u32::<LittleEndian>()
                .context("Failed to read string length")? as usize;
            // Ensure cursor has enough data for the string content
            if (c.position() as usize + len) > c.get_ref().len() {
                bail!(
                    "Not enough data in cursor for string content of length {} (already read {} bytes from cursor, total slice len {})",
                    len,
                    c.position(),
                    c.get_ref().len()
                );
            }
            let mut string_buf = vec![0u8; len];
            c.read_exact(&mut string_buf)
                .context("Failed to read string content")?;
            String::from_utf8(string_buf).context("Failed to decode UTF-8 string")
        };

        let pool_address = read_string_from_cursor(&mut cursor).with_context(|| {
            format!(
                "Deserializing pool_address from data at original offset {}",
                offset
            )
        })?;

        let token_address = read_string_from_cursor(&mut cursor).with_context(|| {
            format!(
                "Deserializing token_address from data at original offset {}",
                offset
            )
        })?;

        // U256 (32 bytes)
        if (cursor.position() as usize + BYTES_256_OFFSET) > cursor.get_ref().len() {
            bail!(
                "Not enough data in cursor for U256 (amount_in) (already read {} bytes from cursor, total slice len {})",
                cursor.position(),
                cursor.get_ref().len()
            );
        }

        let mut amount_in_bytes = [0u8; BYTES_256_OFFSET];
        cursor.read_exact(&mut amount_in_bytes).with_context(|| {
            format!(
                "Deserializing amount_in (U256) from data at original offset {}",
                offset
            )
        })?;

        let amount_in = U256::from_le_bytes(amount_in_bytes);

        let bytes_read = cursor.position() as usize;

        Ok((
            FlashLoan {
                pool_address,
                token_address,
                amount: amount_in,
            },
            offset + bytes_read, // New offset in the original data slice
        ))
    }
}
