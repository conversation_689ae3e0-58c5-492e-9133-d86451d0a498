use crate::types::{LnEpsilon, Pool, TokenMatrix};
use anyhow::Result;
use sprs::{CsMat, TriMat};
use std::collections::HashMap;
use std::error::Error;
use std::f64::INFINITY; // Used to represent infinite distances. // Trait for error types.

const EPSILON: f64 = 1e-10;

// Build the sparse matrix from the pools hash map
pub fn build_sparse_matrix(pools: &HashMap<String, Pool>) -> TokenMatrix {
    let mut matrix = TokenMatrix::default();

    // Step 1: Add each token to the matrix
    for pool in pools.values() {
        matrix.add_token(pool.a_token.clone());
        matrix.add_token(pool.b_token.clone());
    }

    // Step 2: Create the matrix's sparse matrix
    let n = matrix.token_indices.len();
    let mut triplet = TriMat::new((n, n));

    // Step 3: Populate matrix with log(exchange rates)
    for pool in pools.values() {
        let i = matrix.token_indices[&format!("{}-{:?}", pool.a_token.address, pool.a_token.dex)];
        let j = matrix.token_indices[&format!("{}-{:?}", pool.b_token.address, pool.b_token.dex)];

        let rate_a_to_b = pool.a_price;

        let rate_b_to_a = pool.b_price;

        let ln_rate_a_to_b = -rate_a_to_b.ln_epsilon();
        let ln_rate_b_to_a = -rate_b_to_a.ln_epsilon();
        // let ln_rate_a_to_b = -(rate_a_to_b.ln());
        // let ln_rate_b_to_a = -(rate_b_to_a.ln());

        triplet.add_triplet(i, j, ln_rate_a_to_b);
        triplet.add_triplet(j, i, ln_rate_b_to_a);
    }

    matrix.sparse_matrix = triplet;

    matrix
}

// Define a specific error type for this function.
// This helps in clearly indicating why the function might fail (e.g., invalid input).
// The function signature's bare `Result` implies `std::result::Result<T, E>` where `E` is this type.
#[derive(Debug)]
pub enum NegativeCycleError {
    InvalidStartNode, // Indicates that the provided start node index is out of the valid range.
}

// Implement the Display trait to allow printing the error in a user-friendly format.
impl std::fmt::Display for NegativeCycleError {
    fn fmt(&self, f: &mut std::fmt::Formatter) -> std::fmt::Result {
        match self {
            NegativeCycleError::InvalidStartNode => write!(f, "Invalid start node index"),
        }
    }
}

impl Error for NegativeCycleError {}

pub fn detect_negative_cycle(matrix: &CsMat<f64>) -> Result<Vec<usize>, NegativeCycleError> {
    let n = matrix.rows();
    if n == 0 {
        return Ok(Vec::new()); // No negative cycle found.
    }
    // dp[k][v] is the minimum cost to reach vertex v in exactly k edges.
    let mut dp = vec![vec![INFINITY; n]; n + 1];
    // pred[k][v] stores the predecessor of v when reaching it in k steps.
    let mut pred = vec![vec![None; n]; n + 1];

    // Base case: With 0 edges, cost for every vertex is 0.
    for v in 0..n {
        dp[0][v] = 0.0;
    }

    // Fill dp table for steps k = 1..=n.
    for k in 1..=n {
        // Initialize dp[k] with previous values.
        for v in 0..n {
            dp[k][v] = dp[k - 1][v];
            pred[k][v] = pred[k - 1][v];
        }
        // Relax all edges.
        // Iterate over non-zero entries: each edge (u, v) with weight.
        for (&weight, (u, v)) in matrix.iter() {
            if dp[k - 1][u] < INFINITY && dp[k - 1][u] + weight < dp[k][v] - EPSILON {
                dp[k][v] = dp[k - 1][u] + weight;
                pred[k][v] = Some(u);
            }
        }
    }

    // Karp's method: For each vertex v, compute the maximum value over k = 0..n-1 of
    // { (dp[n][v] - dp[k][v]) / (n - k) }.
    // Then, the minimum of these maximum values over v is the minimum mean cycle.
    let mut min_mean = INFINITY;
    let mut cycle_v = None;
    for v in 0..n {
        let mut max_avg = -INFINITY;
        for k in 0..n {
            if dp[n][v] < INFINITY && dp[k][v] < INFINITY {
                let avg = (dp[n][v] - dp[k][v]) / ((n - k) as f64);
                if avg > max_avg {
                    max_avg = avg;
                }
            }
        }
        if max_avg < min_mean {
            min_mean = max_avg;
            cycle_v = Some(v);
        }
    }

    // If the minimum mean is nonnegative (or infinite), no negative cycle exists.
    if min_mean >= 0.0 || min_mean == INFINITY {
        return Ok(Vec::new()); // No negative cycle found.
    }

    // Reconstruct a cycle from the vertex with minimum mean.
    // We use the predecessor information from the final iteration (dp[n]).
    // First convert pred[n]: Vec<Option<usize>> into a Vec<usize> with sentinel usize::MAX.
    let pred_last: Vec<usize> = pred[n]
        .iter()
        .map(|&opt| opt.unwrap_or(usize::MAX))
        .collect();
    // Start from the selected vertex.
    let v0 = cycle_v.unwrap();
    let mut cycle_node = v0;
    // Walk back n steps to ensure we land in the cycle.
    for _ in 0..n {
        if let Some(u) = pred[n][cycle_node] {
            cycle_node = u;
        } else {
            break;
        }
    }
    // Reconstruct the cycle path using our helper.
    let cycle_path = reconstruct_negative_cycle_path(cycle_node, &pred_last, n);

    // display the cycle path
    let cycle_path_str = cycle_path
        .iter()
        .map(|&node| format!("{} -> ", node))
        .collect::<String>();
    println!("Karp’s Cycle path before: {}", cycle_path_str);

    Ok(cycle_path)
}

// Helper function to reconstruct the path of a negative cycle once it's detected.
// This function is called with a node (`start_node_v`) that was involved in a distance
// relaxation during the V-th pass of the Bellman-Ford algorithm. Such a node is on or
// reachable from a negative cycle that is itself reachable from the source.
// The function traces back from `start_node_v` using the stored predecessor information
// until it encounters a node that has already been visited within this traceback.
// The sequence of nodes from the first visit of the repeated node up to the second visit
// (in the order encountered during the traceback) forms the negative cycle nodes in reverse
// traversal order.
// The function returns a vector representing the cycle path in traversal order,
// typically ending with the starting node to explicitly show the cycle loop (e.g., [A, B, C, A]).
// Note: This specific implementation logic for reconstructing the cycle path using
// predecessor traceback is a standard technique in graph algorithms but is not explicitly
// detailed in the provided source materials. It's an extension built upon the basic Bellman-Ford algorithm.
fn reconstruct_negative_cycle_path(
    start_node_v: usize,
    predecessors: &[usize],
    num_vertices: usize,
) -> Vec<usize> {
    // `path_trace` stores the sequence of nodes visited while tracing back from `start_node_v`.
    let mut path_trace: Vec<usize> = Vec::new();
    // `visited_order` maps a node to its index in `path_trace`. This is used to quickly detect
    // if a node has been visited before in the current traceback and to find the start of the cycle within the trace.
    let mut visited_order: HashMap<usize, usize> = HashMap::new(); // node -> index in path_trace

    let mut current = start_node_v;

    // Trace back predecessors from `start_node_v`.
    // Since a negative cycle was detected affecting `start_node_v`, the predecessor chain
    // starting from `start_node_v` must eventually lead into and loop within a negative cycle.
    // We trace up to `num_vertices` steps. In a graph with `num_vertices` vertices, any simple path
    // has at most `num_vertices - 1` edges. Tracing `num_vertices` steps back from an affected node
    // is guaranteed to land on a node within the cycle if it's reachable and the predecessors are correct.
    // If a cycle exists and is reachable from the source and involves `start_node_v`, the predecessor
    // chain from `start_node_v` must eventually form a loop.
    for i in 0..num_vertices {
        // Trace up to V steps.
        // Check if the current node has already been visited in this traceback path.
        if let Some(&first_visit_index) = visited_order.get(&current) {
            // A repeated node is found! This node `current` is part of a negative cycle.
            // The nodes forming the cycle are the sequence in `path_trace` starting from the index
            // of the *first* visit to `current` (`first_visit_index`), followed by `current` itself.
            // Example Trace: v -> A -> B -> C -> A. path_trace = [v, A, B, C]. visited_order={v:0, A:1, B:2, C:3}.
            // When `current` is the second `A` (at trace step i=4), `visited_order.get(&current)` finds `A` at index 1.
            // `first_visit_index = 1`.
            // The nodes in `path_trace` from index 1 onwards are `[A, B, C]`. These are the nodes *in* the cycle,
            // in the order they appear after entering the cycle from the traceback perspective (which is reverse traversal order).
            // The actual cycle traversal order is A -> B -> C -> A.
            // We need to return the cycle nodes in traversal order. The segment `path_trace[first_visit_index..]`
            // contains the nodes from the first occurrence of the cycle node up to the node just before the repeat,
            // in reverse traversal order.
            // Let's collect the nodes from `path_trace[first_visit_index]` up to the *end* of `path_trace`.
            let mut cycle_nodes = path_trace[first_visit_index..].to_vec();
            // The `current` node is the repeated node that closes the cycle. Add it to the end
            // of the list to form the cycle path representation [node1, node2, ..., nodeK, node1].
            cycle_nodes.push(current);

            // The `cycle_nodes` vector now contains the path [A, B, C, A] in the example. This correctly represents the cycle.
            return cycle_nodes; // Return the reconstructed cycle path.
        }

        // If the current node is not repeated in the trace path, record it and its index in the traceback sequence.
        visited_order.insert(current, i);
        path_trace.push(current);

        // Move to the predecessor of the current node to continue tracing back.
        let next = predecessors[current];

        // If we reach a node with no recorded predecessor (indicated by `usize::MAX`) before finding a loop,
        // it means this traceback path ended (likely at the source node, assuming the source's predecessor is never set or is the sentinel).
        // If a negative cycle was indeed detected (which implies it's reachable from the source), the predecessor
        // chain from an affected node *should* eventually form a loop if the cycle is reachable via standard paths.
        // This scenario (hitting MAX without finding a loop) could potentially happen in complex graphs or if
        // the cycle involves the source itself and its predecessor is the sentinel.
        // In such cases, we return an empty vector to signal that reconstruction failed along this specific traceback path.
        if next == usize::MAX {
            return Vec::new(); // Reconstruction failed: Hit the end of the predecessor chain.
        }

        current = next; // Move to the predecessor node for the next step in the traceback.
    }

    // If the loop completes after `num_vertices` steps without finding a repeated node,
    // it strongly suggests an issue with the graph structure, the correctness of the predecessor
    // information, or the traceback logic itself. In theory, if a V-th pass update occurs,
    // a cycle should be found within V steps of tracing back from an affected node.
    Vec::new() // Reconstruction failed: Traceback limit reached without finding a loop.
}
