import {
  Args,
  NoArg,
  Result,
  Serializable,
  byteToU8,
  stringToBytes,
} from '@massalabs/as-types';
import { Address, Context, Storage, call } from '@massalabs/massa-as-sdk';
import { u256 } from 'as-bignum/assembly/integer/u256';
import { MRC20Wrapper } from '@massalabs/sc-standards/assembly/contracts/MRC20/wrapper';

const STORAGE_BYTE_COST = 100_000;
const STORAGE_PREFIX_LENGTH = 4;
const BALANCE_KEY_PREFIX_LENGTH = 7;

export class IMRC20 extends MRC20<PERSON>rapper implements Serializable {
  constructor(origin: Address = new Address()) {
    super(origin);
  }

  serialize(): StaticArray<u8> {
    return this._origin.serialize();
  }

  deserialize(data: StaticArray<u8>, offset: i32): Result<i32> {
    return this._origin.deserialize(data, offset);
  }

  
}
