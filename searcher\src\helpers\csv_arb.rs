use std::fs::{OpenOptions, metadata};

use anyhow::Result;
use csv::WriterBuilder;
use serde::Serialize;

use crate::state::START_TIMESTAMP;

#[derive(Serialize)]
pub struct ArbLogEntry {
    pub timestamp: String,
    pub status: String,
    pub start_amount: f64,
    pub end_amount: f64,
    pub end_percent: f64,
    pub estimated_out: f64,
    pub estim_percent: f64,
    pub pools_count: u64,
    pub cycle_path: String,
    pub operation_id: String,
    pub error_msg: String,
}

pub fn log_arb_result_to_csv(entry: ArbLogEntry) -> Result<()> {
    let file_path = format!("arb_history/arb_{}.csv", START_TIMESTAMP.timestamp());

    // Check if file exists
    let file_exists = metadata(&file_path).is_ok();

    let file = OpenOptions::new()
        .create(true)
        .append(true)
        .open(file_path)?;

    let mut writer = WriterBuilder::new()
        .has_headers(!file_exists) // write headers if file doesn't exist
        .from_writer(file);

    writer.serialize(entry)?;
    writer.flush()?;

    Ok(())
}
