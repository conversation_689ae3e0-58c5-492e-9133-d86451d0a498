2025-07-01T00:00:10.319068Z  INFO quedge::executor::allownace: Increasing allowance from AU12Yd4kCcsizeeTEK9AZyBnuJNZ1cpp99XfCZgzS77ZKnwTFMpVE to AS1VJvdvuU5AZFFKWLD2yxyHsS1CE65imQxQUShqNEf4BqZ2ZHLT for token: WMAS
2025-07-01T00:00:26.146891Z  INFO quedge::executor::allownace: Increased all needed allowance to use token: WMAS
2025-07-01T00:00:26.147085Z  INFO quedge: Increased Needed max allowance for all tokens SUCCESSFULLY!
2025-07-01T00:00:26.148480Z  INFO quedge: Allowed start tokens: ["AS12FW5Rs5YN2zdpEnqwj4iHUUPt9R4Eqjq2qtpJFNKW3mn33RuLU"]
2025-07-01T00:00:26.148662Z  INFO quedge: Exporting the sparse matrix to a png file...
2025-07-01T00:00:26.309267Z  INFO quedge: Exported the sparse matrix to a png file successfully!
2025-07-01T00:00:26.309581Z  INFO quedge::spawns::swap_fetcher: Swap event fetcher working in candidate state
2025-07-01T00:00:26.309670Z DEBUG quedge::helpers: Detecting arbitrage cycles...
2025-07-01T00:00:26.309900Z DEBUG quedge::helpers: Arbitrage cycles found (3 total):
2025-07-01T00:00:26.310047Z DEBUG quedge::helpers: Cycle path Inisde Simulation Task:
2025-07-01T00:00:26.310137Z DEBUG quedge::helpers::display: Path: ["USDC-EagleFi", "USDC-Dusa", "WMAS-Dusa", "WMAS-EagleFi", "USDC-EagleFi"]
2025-07-01T00:00:26.310240Z DEBUG quedge::helpers: Cycle path Inisde Simulation Task:
2025-07-01T00:00:26.310326Z DEBUG quedge::helpers::display: Path: ["WMAS-EagleFi", "USDC-EagleFi", "USDC-Dusa", "WMAS-Dusa", "WMAS-EagleFi"]
2025-07-01T00:00:26.310430Z DEBUG quedge::helpers: Cycle path Inisde Simulation Task:
2025-07-01T00:00:26.310517Z DEBUG quedge::helpers::display: Path: ["WMAS-Dusa", "WMAS-EagleFi", "USDC-EagleFi", "USDC-Dusa", "WMAS-Dusa"]
2025-07-01T00:00:26.310674Z  INFO quedge::helpers: Default start amount for token AS12FW5Rs5YN2zdpEnqwj4iHUUPt9R4Eqjq2qtpJFNKW3mn33RuLU is 10
2025-07-01T00:00:26.310776Z  INFO quedge::helpers: Offchain Amount out after Offchain simulation: 17.640870209276052
2025-07-01T00:00:26.310865Z  INFO quedge::helpers: Offchain Earning percentage: 76.40870209276052
2025-07-01T00:00:26.311086Z  INFO quedge::helpers: Default start amount for token AS12FW5Rs5YN2zdpEnqwj4iHUUPt9R4Eqjq2qtpJFNKW3mn33RuLU is 10
2025-07-01T00:00:26.311183Z  INFO quedge::helpers: Offchain Amount out after Offchain simulation: 17.640870209276052
2025-07-01T00:00:26.311280Z  INFO quedge::helpers: Offchain Earning percentage: 76.40870209276052
2025-07-01T00:00:26.449113Z  INFO quedge::spawns::swap_fetcher: Start waitiing for an arbitrage opportunity...
2025-07-01T00:00:26.466434Z DEBUG quedge::spawns::balance_notifier: Balance of AU12Yd4kCcsizeeTEK9AZyBnuJNZ1cpp99XfCZgzS77ZKnwTFMpVE is 104422.512238641
2025-07-01T00:00:26.469658Z  INFO quedge::helpers: Amount out after Onchain simulation: 87.23847287
2025-07-01T00:00:26.469772Z  INFO quedge::helpers: Earning percentage: 74.47694573999999
2025-07-01T00:00:26.472216Z  INFO quedge::helpers: Amount out after Onchain simulation: 87.23847287
2025-07-01T00:00:26.472323Z  INFO quedge::helpers: Earning percentage: 74.47694573999999
2025-07-01T00:00:26.472464Z  INFO quedge::helpers: Found 2 valid cycles
2025-07-01T00:00:26.472647Z  INFO quedge::executor: Executing arbitrage with start amount 50 for cycle path: ["WMAS-Dusa", "WMAS-EagleFi", "USDC-EagleFi", "USDC-Dusa", "WMAS-Dusa"]...
2025-07-01T00:00:42.360800Z  INFO quedge::spawns::swap_fetcher: Found 2 Swap events from the operation O123yMk9kZLTpaJmbk3t5YfxhuZo22hSUmnyzgD4eAzn62og9ys. Processing them...
2025-07-01T00:00:42.360967Z  INFO quedge::spawns::swap_fetcher: New detected swap on pool: AS12dny2u8p6DmcPnYurnzqrAG7kMjYUcSD6bDCdJfbYeadYqXaL5
2025-07-01T00:00:42.520365Z ERROR quedge: Error in detect_and_execute_arb at the start: Failed to parse amount out to U256
2025-07-01T00:00:42.521373Z  INFO quedge::strategies::bellman_ford_matrix: Updating sparse matrix by pool: Pool { address: "AS12dny2u8p6DmcPnYurnzqrAG7kMjYUcSD6bDCdJfbYeadYqXaL5", a_token: Token { address: "AS12N76WPYB3QNYKGhV2jZuQs1djdhNJLQgnm7m52pHWecvvj1fCQ", name: "USD Coin", symbol: "USDC", decimals: 6, dex: EagleFi }, b_token: Token { address: "AS12FW5Rs5YN2zdpEnqwj4iHUUPt9R4Eqjq2qtpJFNKW3mn33RuLU", name: "Wrapped Massa", symbol: "WMAS", decimals: 9, dex: EagleFi }, a_reserve: 3990.217346, b_reserve: 4446.852771648, dex: EagleFi, swap_fee: 0.3, a_price: 1.1144387350492964, b_price: 0.8973126727830093, active_bin_id: 0, bin_step: 0, bins: {} }
2025-07-01T00:00:42.521512Z  WARN quedge::spawns::swap_fetcher: Skipping arbitrage detection after updating the sparse matrix because of an order from the events stream
2025-07-01T00:00:42.521611Z  INFO quedge::spawns::swap_fetcher: Swap event processed successfully
2025-07-01T00:00:42.521705Z DEBUG quedge::spawns::swap_fetcher: Processing last event of the operation O123yMk9kZLTpaJmbk3t5YfxhuZo22hSUmnyzgD4eAzn62og9ys...
2025-07-01T00:00:42.521835Z  WARN quedge::spawns::swap_fetcher: Pool AS1VJvdvuU5AZFFKWLD2yxyHsS1CE65imQxQUShqNEf4BqZ2ZHLT not found in the pools hash map
2025-07-01T00:00:42.521916Z  WARN quedge::spawns::swap_fetcher: Pool AS1fewbVp4FMTEJH81e1BNaH9B3SmeE7sNpZQ2QqwP1wGBzLKJGF not found in the pools hash map
2025-07-01T00:00:42.521996Z  WARN quedge::spawns::swap_fetcher: Pool AS1VJvdvuU5AZFFKWLD2yxyHsS1CE65imQxQUShqNEf4BqZ2ZHLT not found in the pools hash map
2025-07-01T00:00:42.522069Z  WARN quedge::spawns::swap_fetcher: Pool AS1XqtvX3rz2RWbnqLfaYVKEjM3VS5pny9yKDdXcmJ5C1vrcLEFd not found in the pools hash map
2025-07-01T00:00:42.522158Z  INFO quedge::spawns::swap_fetcher: New detected swap on pool: AS112Wdy9pM4fvLNLHQXyf7uam9waMPdG5ekr4vxCyQHPkrMMPPY
2025-07-01T00:00:42.844653Z  INFO quedge::strategies::bellman_ford_matrix: Updating sparse matrix by pool: Pool { address: "AS112Wdy9pM4fvLNLHQXyf7uam9waMPdG5ekr4vxCyQHPkrMMPPY", a_token: Token { address: "AS12FW5Rs5YN2zdpEnqwj4iHUUPt9R4Eqjq2qtpJFNKW3mn33RuLU", name: "Wrapped Massa", symbol: "WMAS", decimals: 9, dex: Dusa }, b_token: Token { address: "AS12N76WPYB3QNYKGhV2jZuQs1djdhNJLQgnm7m52pHWecvvj1fCQ", name: "USD Coin", symbol: "USDC", decimals: 6, dex: Dusa }, a_reserve: 12749.647984112, b_reserve: 5119.309551, dex: Dusa, swap_fee: 0.0, a_price: 0.5175336123449099, b_price: 1.9322416479754183, active_bin_id: 8384821, bin_step: 20, bins: {8384820: Bin { id: 8384820, a_reserve: 0.0, b_reserve: 255.062031 }, 8384821: Bin { id: 8384821, a_reserve: 403.968971787, b_reserve: 46.504633 }, 8384822: Bin { id: 8384822, a_reserve: 493.827160493, b_reserve: 0.0 }} }
2025-07-01T00:00:42.844849Z  INFO quedge::spawns::swap_fetcher: Swap event processed successfully
2025-07-01T00:00:42.845076Z DEBUG quedge::helpers: Detecting arbitrage cycles...
2025-07-01T00:00:42.845265Z DEBUG quedge::helpers: Arbitrage cycles found (3 total):
2025-07-01T00:00:42.845393Z DEBUG quedge::helpers: Cycle path Inisde Simulation Task:
2025-07-01T00:00:42.845480Z DEBUG quedge::helpers::display: Path: ["USDC-EagleFi", "USDC-Dusa", "WMAS-Dusa", "WMAS-EagleFi", "USDC-EagleFi"]
2025-07-01T00:00:42.845654Z DEBUG quedge::helpers: Cycle path Inisde Simulation Task:
2025-07-01T00:00:42.845772Z DEBUG quedge::helpers::display: Path: ["WMAS-EagleFi", "USDC-EagleFi", "USDC-Dusa", "WMAS-Dusa", "WMAS-EagleFi"]
2025-07-01T00:00:42.845943Z DEBUG quedge::helpers: Cycle path Inisde Simulation Task:
2025-07-01T00:00:42.846081Z DEBUG quedge::helpers::display: Path: ["WMAS-Dusa", "WMAS-EagleFi", "USDC-EagleFi", "USDC-Dusa", "WMAS-Dusa"]
2025-07-01T00:00:42.846251Z  INFO quedge::helpers: Default start amount for token AS12FW5Rs5YN2zdpEnqwj4iHUUPt9R4Eqjq2qtpJFNKW3mn33RuLU is 10
2025-07-01T00:00:42.846367Z  INFO quedge::helpers: Offchain Amount out after Offchain simulation: 17.247564782908107
2025-07-01T00:00:42.846460Z  INFO quedge::helpers: Offchain Earning percentage: 72.47564782908107
2025-07-01T00:00:42.846674Z  INFO quedge::helpers: Default start amount for token AS12FW5Rs5YN2zdpEnqwj4iHUUPt9R4Eqjq2qtpJFNKW3mn33RuLU is 10
2025-07-01T00:00:42.846760Z  INFO quedge::helpers: Offchain Amount out after Offchain simulation: 17.247564782908107
2025-07-01T00:00:42.846845Z  INFO quedge::helpers: Offchain Earning percentage: 72.47564782908107
2025-07-01T00:00:42.999112Z  INFO quedge::helpers: Amount out after Onchain simulation: 85.302055648
2025-07-01T00:00:42.999289Z  INFO quedge::helpers: Earning percentage: 70.60411129600001
2025-07-01T00:00:43.004329Z  INFO quedge::helpers: Amount out after Onchain simulation: 85.302055648
2025-07-01T00:00:43.004438Z  INFO quedge::helpers: Earning percentage: 70.60411129600001
2025-07-01T00:00:43.004636Z  INFO quedge::helpers: Found 2 valid cycles
2025-07-01T00:00:43.004799Z  INFO quedge::executor: Executing arbitrage with start amount 50 for cycle path: ["WMAS-Dusa", "WMAS-EagleFi", "USDC-EagleFi", "USDC-Dusa", "WMAS-Dusa"]...
2025-07-01T00:00:58.165022Z  WARN quedge::spawns::swap_fetcher: Operation O12TbzuJ6wrTrMZKwsRWUzmqaHQUcMsq5CKUaFPUyk7c8nr2ofMh is not successful, skipping
2025-07-01T00:00:58.322464Z DEBUG quedge::executor: Showing all operation events data for operation id: O12TbzuJ6wrTrMZKwsRWUzmqaHQUcMsq5CKUaFPUyk7c8nr2ofMh ...
2025-07-01T00:00:58.322621Z DEBUG quedge::executor: Event Data: "ArbRoutes inside execuwithFlmashLoan: Router Address: AS12JfpytS54Pq2Pi5ZP9dZ8kxe21TmwBpoiQuMNvAgB3bSVsCs9k\nPool Address: AS12dny2u8p6DmcPnYurnzqrAG7kMjYUcSD6bDCdJfbYeadYqXaL5\nToken In Address: AS12FW5Rs5YN2zdpEnqwj4iHUUPt9R4Eqjq2qtpJFNKW3mn33RuLU\nToken Out Address: AS12N76WPYB3QNYKGhV2jZuQs1djdhNJLQgnm7m52pHWecvvj1fCQ\nAmount In: 50000000000\nAmount Out Min: 1\nBin Step: 0\nTo: AS1VJvdvuU5AZFFKWLD2yxyHsS1CE65imQxQUShqNEf4BqZ2ZHLT\nDeadline: 1751328643004\nIs Dusa: false\nCoins To Use: 10000000\nSwap Params: \n,Router Address: AS1XqtvX3rz2RWbnqLfaYVKEjM3VS5pny9yKDdXcmJ5C1vrcLEFd\nPool Address: AS112Wdy9pM4fvLNLHQXyf7uam9waMPdG5ekr4vxCyQHPkrMMPPY\nToken In Address: AS12N76WPYB3QNYKGhV2jZuQs1djdhNJLQgnm7m52pHWecvvj1fCQ\nToken Out Address: AS12FW5Rs5YN2zdpEnqwj4iHUUPt9R4Eqjq2qtpJFNKW3mn33RuLU\nAmount In: 50000000000\nAmount Out Min: 1\nBin Step: 20\nTo: AS1VJvdvuU5AZFFKWLD2yxyHsS1CE65imQxQUShqNEf4BqZ2ZHLT\nDeadline: 1751328643004\nIs Dusa: true\nCoins To Use: 1000000\nSwap Params: \n"
2025-07-01T00:00:58.322752Z DEBUG quedge::executor: Event Data: "{\"massa_execution_error\":\"Runtime error: runtime error when executing operation O12TbzuJ6wrTrMZKwsRWUzmqaHQUcMsq5CKUaFPUyk7c8nr2ofMh: VM Error in CallSC context: Depth error: Runtime error: error transferring 0.02 coins from AS1VJvdvuU5AZFFKWLD2yxyHsS1CE65imQxQUShqNEf4BqZ2ZHLT to AS1fewbVp4FMTEJH81e1BNaH9B3SmeE7sNpZQ2QqwP1wGBzLKJGF: Runtime error: failed to transfer 0.02 coins from spending address AS1VJvdvuU5AZFFKWLD2yxyHsS1CE65imQxQUShqNEf4BqZ2ZHLT due to insufficient balance 0.0038\"}"
2025-07-01T00:00:58.323933Z ERROR quedge::spawns::swap_fetcher: Error in detect_and_execute_arb: Failed to execute arbitrage. operation_id: O12TbzuJ6wrTrMZKwsRWUzmqaHQUcMsq5CKUaFPUyk7c8nr2ofMh, operation_status: 2,  Cycle path: ["WMAS-Dusa", "WMAS-EagleFi", "USDC-EagleFi", "USDC-Dusa", "WMAS-Dusa"], simul amount out: 85.302055648, Last event data: {"massa_execution_error":"Runtime error: runtime error when executing operation O12TbzuJ6wrTrMZKwsRWUzmqaHQUcMsq5CKUaFPUyk7c8nr2ofMh: VM Error in CallSC context: Depth error: Runtime error: error transferring 0.02 coins from AS1VJvdvuU5AZFFKWLD2yxyHsS1CE65imQxQUShqNEf4BqZ2ZHLT to AS1fewbVp4FMTEJH81e1BNaH9B3SmeE7sNpZQ2QqwP1wGBzLKJGF: Runtime error: failed to transfer 0.02 coins from spending address AS1VJvdvuU5AZFFKWLD2yxyHsS1CE65imQxQUShqNEf4BqZ2ZHLT due to insufficient balance 0.0038"}
2025-07-01T00:02:27.184382Z  INFO quedge: Logger initialized Successfully
2025-07-01T00:02:27.977472Z  INFO quedge: Increasing max allowance for all tokens....
2025-07-01T00:02:27.977597Z  INFO quedge::executor::allownace: Starting to increase all allowance for token: USDC...
2025-07-01T00:02:28.126761Z DEBUG quedge::executor::allownace: Allowance from AS1VJvdvuU5AZFFKWLD2yxyHsS1CE65imQxQUShqNEf4BqZ2ZHLT to AS12JfpytS54Pq2Pi5ZP9dZ8kxe21TmwBpoiQuMNvAgB3bSVsCs9k for token AS12N76WPYB3QNYKGhV2jZuQs1djdhNJLQgnm7m52pHWecvvj1fCQ is 115792089237316195423570985008687907853269984665640564039457584007913129639935
2025-07-01T00:02:28.126915Z  INFO quedge::executor::allownace: Already max allowance for arb contract
2025-07-01T00:02:28.280407Z DEBUG quedge::executor::allownace: Allowance from AS1VJvdvuU5AZFFKWLD2yxyHsS1CE65imQxQUShqNEf4BqZ2ZHLT to AS1XqtvX3rz2RWbnqLfaYVKEjM3VS5pny9yKDdXcmJ5C1vrcLEFd for token AS12N76WPYB3QNYKGhV2jZuQs1djdhNJLQgnm7m52pHWecvvj1fCQ is 115792089237316195423570985008687907853269984665640564039457584007913084400614
2025-07-01T00:02:28.280535Z  INFO quedge::executor::allownace: Already max allowance for arb contract
2025-07-01T00:02:28.440688Z  INFO quedge::executor::allownace: Increasing allowance from AU12Yd4kCcsizeeTEK9AZyBnuJNZ1cpp99XfCZgzS77ZKnwTFMpVE to AS1VJvdvuU5AZFFKWLD2yxyHsS1CE65imQxQUShqNEf4BqZ2ZHLT for token: USDC
2025-07-01T00:02:56.869636Z  INFO quedge: Logger initialized Successfully
2025-07-01T00:02:57.646419Z  INFO quedge: Allowed start tokens: ["AS12FW5Rs5YN2zdpEnqwj4iHUUPt9R4Eqjq2qtpJFNKW3mn33RuLU"]
2025-07-01T00:02:57.646598Z  INFO quedge: Exporting the sparse matrix to a png file...
2025-07-01T00:02:57.786204Z  INFO quedge: Exported the sparse matrix to a png file successfully!
2025-07-01T00:02:57.786568Z  INFO quedge::spawns::swap_fetcher: Swap event fetcher working in candidate state
2025-07-01T00:02:57.786663Z DEBUG quedge::helpers: Detecting arbitrage cycles...
2025-07-01T00:02:57.786952Z DEBUG quedge::helpers: Arbitrage cycles found (3 total):
2025-07-01T00:02:57.787161Z DEBUG quedge::helpers: Cycle path Inisde Simulation Task:
2025-07-01T00:02:57.787298Z DEBUG quedge::helpers::display: Path: ["WMAS-Dusa", "WMAS-EagleFi", "USDC-EagleFi", "USDC-Dusa", "WMAS-Dusa"]
2025-07-01T00:02:57.787464Z DEBUG quedge::helpers: Cycle path Inisde Simulation Task:
2025-07-01T00:02:57.787584Z DEBUG quedge::helpers::display: Path: ["USDC-Dusa", "WMAS-Dusa", "WMAS-EagleFi", "USDC-EagleFi", "USDC-Dusa"]
2025-07-01T00:02:57.787749Z DEBUG quedge::helpers: Cycle path Inisde Simulation Task:
2025-07-01T00:02:57.787846Z  INFO quedge::helpers: Default start amount for token AS12FW5Rs5YN2zdpEnqwj4iHUUPt9R4Eqjq2qtpJFNKW3mn33RuLU is 10
2025-07-01T00:02:57.787926Z DEBUG quedge::helpers::display: Path: ["USDC-EagleFi", "USDC-Dusa", "WMAS-Dusa", "WMAS-EagleFi", "USDC-EagleFi"]
2025-07-01T00:02:57.788029Z  INFO quedge::helpers: Offchain Amount out after Offchain simulation: 17.247564782908107
2025-07-01T00:02:57.788173Z  INFO quedge::helpers: Offchain Earning percentage: 72.47564782908107
2025-07-01T00:02:57.927948Z  INFO quedge::spawns::swap_fetcher: Start waitiing for an arbitrage opportunity...
2025-07-01T00:02:57.946576Z DEBUG quedge::spawns::balance_notifier: Balance of AU12Yd4kCcsizeeTEK9AZyBnuJNZ1cpp99XfCZgzS77ZKnwTFMpVE is 104422.472238641
2025-07-01T00:02:57.954331Z  INFO quedge::helpers: Amount out after Onchain simulation: 85.302055648
2025-07-01T00:02:57.954462Z  INFO quedge::helpers: Earning percentage: 70.60411129600001
2025-07-01T00:02:57.954612Z  INFO quedge::helpers: Found 1 valid cycles
2025-07-01T00:02:57.954775Z  INFO quedge::executor: Executing arbitrage with start amount 50 for cycle path: ["WMAS-Dusa", "WMAS-EagleFi", "USDC-EagleFi", "USDC-Dusa", "WMAS-Dusa"]...
2025-07-01T00:03:06.166187Z  WARN quedge::spawns::swap_fetcher: Operation O1BovcbYV95fcxXXygbeDmrUut5iVtAtUMZ2DoMnTjr2yAgj6ma is not successful, skipping
2025-07-01T00:03:06.317596Z ERROR quedge: Error in detect_and_execute_arb at the start: Failed to execute arbitrage. operation_id: O1BovcbYV95fcxXXygbeDmrUut5iVtAtUMZ2DoMnTjr2yAgj6ma, operation_status: 2,  Cycle path: ["WMAS-Dusa", "WMAS-EagleFi", "USDC-EagleFi", "USDC-Dusa", "WMAS-Dusa"], simul amount out: 85.302055648, Last event data: {"massa_execution_error":"Runtime error: runtime error when executing operation O1BovcbYV95fcxXXygbeDmrUut5iVtAtUMZ2DoMnTjr2yAgj6ma: VM Error in CallSC context: Depth error: Runtime error: error transferring 0.01 coins from AS1VJvdvuU5AZFFKWLD2yxyHsS1CE65imQxQUShqNEf4BqZ2ZHLT to AS12JfpytS54Pq2Pi5ZP9dZ8kxe21TmwBpoiQuMNvAgB3bSVsCs9k: Runtime error: failed to transfer 0.01 coins from spending address AS1VJvdvuU5AZFFKWLD2yxyHsS1CE65imQxQUShqNEf4BqZ2ZHLT due to insufficient balance 0.0038"}
2025-07-01T00:04:31.330349Z  INFO quedge: Logger initialized Successfully
2025-07-01T00:04:32.113914Z  INFO quedge: Allowed start tokens: ["AS12FW5Rs5YN2zdpEnqwj4iHUUPt9R4Eqjq2qtpJFNKW3mn33RuLU"]
2025-07-01T00:04:32.114111Z  INFO quedge: Exporting the sparse matrix to a png file...
2025-07-01T00:04:32.249261Z  INFO quedge: Exported the sparse matrix to a png file successfully!
2025-07-01T00:04:32.249588Z  INFO quedge::spawns::swap_fetcher: Swap event fetcher working in candidate state
2025-07-01T00:04:32.249714Z DEBUG quedge::helpers: Detecting arbitrage cycles...
2025-07-01T00:04:32.249977Z DEBUG quedge::helpers: Arbitrage cycles found (4 total):
2025-07-01T00:04:32.250177Z DEBUG quedge::helpers: Cycle path Inisde Simulation Task:
2025-07-01T00:04:32.250323Z DEBUG quedge::helpers::display: Path: ["USDC-EagleFi", "USDC-Dusa", "WMAS-Dusa", "WMAS-EagleFi", "USDC-EagleFi"]
2025-07-01T00:04:32.250443Z DEBUG quedge::helpers: Cycle path Inisde Simulation Task:
2025-07-01T00:04:32.250544Z DEBUG quedge::helpers::display: Path: ["WMAS-EagleFi", "USDC-EagleFi", "USDC-Dusa", "WMAS-Dusa", "WMAS-EagleFi"]
2025-07-01T00:04:32.250659Z DEBUG quedge::helpers: Cycle path Inisde Simulation Task:
2025-07-01T00:04:32.250738Z DEBUG quedge::helpers::display: Path: ["USDC-Dusa", "WMAS-Dusa", "WMAS-EagleFi", "USDC-EagleFi", "USDC-Dusa"]
2025-07-01T00:04:32.250863Z DEBUG quedge::helpers: Cycle path Inisde Simulation Task:
2025-07-01T00:04:32.250932Z  INFO quedge::helpers: Default start amount for token AS12FW5Rs5YN2zdpEnqwj4iHUUPt9R4Eqjq2qtpJFNKW3mn33RuLU is 10
2025-07-01T00:04:32.250997Z DEBUG quedge::helpers::display: Path: ["WMAS-Dusa", "WMAS-EagleFi", "USDC-EagleFi", "USDC-Dusa", "WMAS-Dusa"]
2025-07-01T00:04:32.251064Z  INFO quedge::helpers: Offchain Amount out after Offchain simulation: 17.247564782908107
2025-07-01T00:04:32.251162Z  INFO quedge::helpers: Offchain Earning percentage: 72.47564782908107
2025-07-01T00:04:32.251227Z  INFO quedge::helpers: Default start amount for token AS12FW5Rs5YN2zdpEnqwj4iHUUPt9R4Eqjq2qtpJFNKW3mn33RuLU is 10
2025-07-01T00:04:32.251313Z  INFO quedge::helpers: Offchain Amount out after Offchain simulation: 17.247564782908107
2025-07-01T00:04:32.251406Z  INFO quedge::helpers: Offchain Earning percentage: 72.47564782908107
2025-07-01T00:04:32.394340Z  INFO quedge::spawns::swap_fetcher: Start waitiing for an arbitrage opportunity...
2025-07-01T00:04:32.411686Z DEBUG quedge::spawns::balance_notifier: Balance of AU12Yd4kCcsizeeTEK9AZyBnuJNZ1cpp99XfCZgzS77ZKnwTFMpVE is 104422.462238641
2025-07-01T00:04:32.416484Z  INFO quedge::helpers: Amount out after Onchain simulation: 85.302055648
2025-07-01T00:04:32.416599Z  INFO quedge::helpers: Earning percentage: 70.60411129600001
2025-07-01T00:04:32.419487Z  INFO quedge::helpers: Amount out after Onchain simulation: 85.302055648
2025-07-01T00:04:32.419588Z  INFO quedge::helpers: Earning percentage: 70.60411129600001
2025-07-01T00:04:32.419740Z  INFO quedge::helpers: Found 2 valid cycles
2025-07-01T00:04:32.420040Z  INFO quedge::executor: Executing arbitrage with start amount 50 for cycle path: ["WMAS-Dusa", "WMAS-EagleFi", "USDC-EagleFi", "USDC-Dusa", "WMAS-Dusa"]...
2025-07-01T00:04:42.364395Z  INFO quedge::spawns::swap_fetcher: Found 2 Swap events from the operation O12pWsRaUwQJfnXehm3sEvKembc14fa9PNiHA9k5yKjtUeC6pLN5. Processing them...
2025-07-01T00:04:42.364553Z  INFO quedge::spawns::swap_fetcher: New detected swap on pool: AS12dny2u8p6DmcPnYurnzqrAG7kMjYUcSD6bDCdJfbYeadYqXaL5
2025-07-01T00:04:42.554836Z  INFO quedge::strategies::bellman_ford_matrix: Updating sparse matrix by pool: Pool { address: "AS12dny2u8p6DmcPnYurnzqrAG7kMjYUcSD6bDCdJfbYeadYqXaL5", a_token: Token { address: "AS12N76WPYB3QNYKGhV2jZuQs1djdhNJLQgnm7m52pHWecvvj1fCQ", name: "USD Coin", symbol: "USDC", decimals: 6, dex: EagleFi }, b_token: Token { address: "AS12FW5Rs5YN2zdpEnqwj4iHUUPt9R4Eqjq2qtpJFNKW3mn33RuLU", name: "Wrapped Massa", symbol: "WMAS", decimals: 9, dex: EagleFi }, a_reserve: 3945.982194, b_reserve: 4496.815271648, dex: EagleFi, swap_fee: 0.3, a_price: 1.1395934017354563, b_price: 0.877505958245394, active_bin_id: 0, bin_step: 0, bins: {} }
2025-07-01T00:04:42.554949Z ERROR quedge: Error in detect_and_execute_arb at the start: Failed to parse amount out to U256
2025-07-01T00:04:42.555036Z  WARN quedge::spawns::swap_fetcher: Skipping arbitrage detection after updating the sparse matrix because of an order from the events stream
2025-07-01T00:04:42.555170Z  INFO quedge::spawns::swap_fetcher: Swap event processed successfully
2025-07-01T00:04:42.555288Z DEBUG quedge::spawns::swap_fetcher: Processing last event of the operation O12pWsRaUwQJfnXehm3sEvKembc14fa9PNiHA9k5yKjtUeC6pLN5...
2025-07-01T00:04:42.555414Z  WARN quedge::spawns::swap_fetcher: Pool AS1VJvdvuU5AZFFKWLD2yxyHsS1CE65imQxQUShqNEf4BqZ2ZHLT not found in the pools hash map
2025-07-01T00:04:42.555508Z  WARN quedge::spawns::swap_fetcher: Pool AS1fewbVp4FMTEJH81e1BNaH9B3SmeE7sNpZQ2QqwP1wGBzLKJGF not found in the pools hash map
2025-07-01T00:04:42.555597Z  WARN quedge::spawns::swap_fetcher: Pool AS1VJvdvuU5AZFFKWLD2yxyHsS1CE65imQxQUShqNEf4BqZ2ZHLT not found in the pools hash map
2025-07-01T00:04:42.555696Z  WARN quedge::spawns::swap_fetcher: Pool AS1XqtvX3rz2RWbnqLfaYVKEjM3VS5pny9yKDdXcmJ5C1vrcLEFd not found in the pools hash map
2025-07-01T00:04:42.555787Z  INFO quedge::spawns::swap_fetcher: New detected swap on pool: AS112Wdy9pM4fvLNLHQXyf7uam9waMPdG5ekr4vxCyQHPkrMMPPY
2025-07-01T00:04:42.872919Z  INFO quedge::strategies::bellman_ford_matrix: Updating sparse matrix by pool: Pool { address: "AS112Wdy9pM4fvLNLHQXyf7uam9waMPdG5ekr4vxCyQHPkrMMPPY", a_token: Token { address: "AS12FW5Rs5YN2zdpEnqwj4iHUUPt9R4Eqjq2qtpJFNKW3mn33RuLU", name: "Wrapped Massa", symbol: "WMAS", decimals: 9, dex: Dusa }, b_token: Token { address: "AS12N76WPYB3QNYKGhV2jZuQs1djdhNJLQgnm7m52pHWecvvj1fCQ", name: "USD Coin", symbol: "USDC", decimals: 6, dex: Dusa }, a_reserve: 12664.345928464, b_reserve: 5163.456232, dex: Dusa, swap_fee: 0.0, a_price: 0.5175336123449099, b_price: 1.9322416479754183, active_bin_id: 8384821, bin_step: 20, bins: {8384820: Bin { id: 8384820, a_reserve: 0.0, b_reserve: 255.062031 }, 8384821: Bin { id: 8384821, a_reserve: 318.666916139, b_reserve: 90.651314 }, 8384822: Bin { id: 8384822, a_reserve: 493.827160493, b_reserve: 0.0 }} }
2025-07-01T00:04:42.873169Z  INFO quedge::spawns::swap_fetcher: Swap event processed successfully
2025-07-01T00:04:42.873403Z DEBUG quedge::helpers: Detecting arbitrage cycles...
2025-07-01T00:04:42.873588Z DEBUG quedge::helpers: Arbitrage cycles found (4 total):
2025-07-01T00:04:42.873712Z DEBUG quedge::helpers: Cycle path Inisde Simulation Task:
2025-07-01T00:04:42.873820Z DEBUG quedge::helpers::display: Path: ["USDC-EagleFi", "USDC-Dusa", "WMAS-Dusa", "WMAS-EagleFi", "USDC-EagleFi"]
2025-07-01T00:04:42.873927Z DEBUG quedge::helpers: Cycle path Inisde Simulation Task:
2025-07-01T00:04:42.874033Z DEBUG quedge::helpers::display: Path: ["WMAS-EagleFi", "USDC-EagleFi", "USDC-Dusa", "WMAS-Dusa", "WMAS-EagleFi"]
2025-07-01T00:04:42.874150Z DEBUG quedge::helpers: Cycle path Inisde Simulation Task:
2025-07-01T00:04:42.874231Z DEBUG quedge::helpers::display: Path: ["USDC-Dusa", "WMAS-Dusa", "WMAS-EagleFi", "USDC-EagleFi", "USDC-Dusa"]
2025-07-01T00:04:42.874344Z DEBUG quedge::helpers: Cycle path Inisde Simulation Task:
2025-07-01T00:04:42.874421Z  INFO quedge::helpers: Default start amount for token AS12FW5Rs5YN2zdpEnqwj4iHUUPt9R4Eqjq2qtpJFNKW3mn33RuLU is 10
2025-07-01T00:04:42.874487Z DEBUG quedge::helpers::display: Path: ["WMAS-Dusa", "WMAS-EagleFi", "USDC-EagleFi", "USDC-Dusa", "WMAS-Dusa"]
2025-07-01T00:04:42.874560Z  INFO quedge::helpers: Offchain Amount out after Offchain simulation: 16.867272137028714
2025-07-01T00:04:42.874640Z  INFO quedge::helpers: Default start amount for token AS12FW5Rs5YN2zdpEnqwj4iHUUPt9R4Eqjq2qtpJFNKW3mn33RuLU is 10
2025-07-01T00:04:42.874710Z  INFO quedge::helpers: Offchain Earning percentage: 68.67272137028714
2025-07-01T00:04:42.874775Z  INFO quedge::helpers: Offchain Amount out after Offchain simulation: 16.867272137028714
2025-07-01T00:04:42.874861Z  INFO quedge::helpers: Offchain Earning percentage: 68.67272137028714
2025-07-01T00:04:43.044731Z  INFO quedge::helpers: Amount out after Onchain simulation: 83.429427519
2025-07-01T00:04:43.044857Z  INFO quedge::helpers: Earning percentage: 66.858855038
2025-07-01T00:04:43.045333Z  INFO quedge::helpers: Amount out after Onchain simulation: 83.429427519
2025-07-01T00:04:43.045458Z  INFO quedge::helpers: Earning percentage: 66.858855038
2025-07-01T00:04:43.045625Z  INFO quedge::helpers: Found 2 valid cycles
2025-07-01T00:04:43.045789Z  INFO quedge::executor: Executing arbitrage with start amount 50 for cycle path: ["WMAS-Dusa", "WMAS-EagleFi", "USDC-EagleFi", "USDC-Dusa", "WMAS-Dusa"]...
2025-07-01T00:04:58.349776Z  INFO quedge::spawns::swap_fetcher: Found 2 Swap events from the operation O1LfQj3w7xQ8Wks9ZHKiUL2Wxm7EbqHCnQ5mT3zQh9tb3FKyeZP. Processing them...
2025-07-01T00:04:58.349943Z  INFO quedge::spawns::swap_fetcher: New detected swap on pool: AS12dny2u8p6DmcPnYurnzqrAG7kMjYUcSD6bDCdJfbYeadYqXaL5
2025-07-01T00:04:58.498224Z  INFO quedge::strategies::bellman_ford_matrix: Updating sparse matrix by pool: Pool { address: "AS12dny2u8p6DmcPnYurnzqrAG7kMjYUcSD6bDCdJfbYeadYqXaL5", a_token: Token { address: "AS12N76WPYB3QNYKGhV2jZuQs1djdhNJLQgnm7m52pHWecvvj1fCQ", name: "USD Coin", symbol: "USDC", decimals: 6, dex: EagleFi }, b_token: Token { address: "AS12FW5Rs5YN2zdpEnqwj4iHUUPt9R4Eqjq2qtpJFNKW3mn33RuLU", name: "Wrapped Massa", symbol: "WMAS", decimals: 9, dex: EagleFi }, a_reserve: 3902.718132, b_reserve: 4546.777771648, dex: EagleFi, swap_fee: 0.3, a_price: 1.165028479604276, b_price: 0.8583481155238959, active_bin_id: 0, bin_step: 0, bins: {} }
2025-07-01T00:04:58.498388Z  WARN quedge::spawns::swap_fetcher: Skipping arbitrage detection after updating the sparse matrix because of an order from the events stream
2025-07-01T00:04:58.498526Z  INFO quedge::spawns::swap_fetcher: Swap event processed successfully
2025-07-01T00:04:58.498657Z DEBUG quedge::spawns::swap_fetcher: Processing last event of the operation O1LfQj3w7xQ8Wks9ZHKiUL2Wxm7EbqHCnQ5mT3zQh9tb3FKyeZP...
2025-07-01T00:04:58.498772Z  WARN quedge::spawns::swap_fetcher: Pool AS1VJvdvuU5AZFFKWLD2yxyHsS1CE65imQxQUShqNEf4BqZ2ZHLT not found in the pools hash map
2025-07-01T00:04:58.498886Z  WARN quedge::spawns::swap_fetcher: Pool AS1fewbVp4FMTEJH81e1BNaH9B3SmeE7sNpZQ2QqwP1wGBzLKJGF not found in the pools hash map
2025-07-01T00:04:58.499005Z  WARN quedge::spawns::swap_fetcher: Pool AS1VJvdvuU5AZFFKWLD2yxyHsS1CE65imQxQUShqNEf4BqZ2ZHLT not found in the pools hash map
2025-07-01T00:04:58.499105Z  WARN quedge::spawns::swap_fetcher: Pool AS1XqtvX3rz2RWbnqLfaYVKEjM3VS5pny9yKDdXcmJ5C1vrcLEFd not found in the pools hash map
2025-07-01T00:04:58.499208Z  INFO quedge::spawns::swap_fetcher: New detected swap on pool: AS112Wdy9pM4fvLNLHQXyf7uam9waMPdG5ekr4vxCyQHPkrMMPPY
2025-07-01T00:04:58.501987Z ERROR quedge::spawns::swap_fetcher: Error in detect_and_execute_arb: Failed to parse amount out to U256
2025-07-01T00:04:58.811633Z  INFO quedge::strategies::bellman_ford_matrix: Updating sparse matrix by pool: Pool { address: "AS112Wdy9pM4fvLNLHQXyf7uam9waMPdG5ekr4vxCyQHPkrMMPPY", a_token: Token { address: "AS12FW5Rs5YN2zdpEnqwj4iHUUPt9R4Eqjq2qtpJFNKW3mn33RuLU", name: "Wrapped Massa", symbol: "WMAS", decimals: 9, dex: Dusa }, b_token: Token { address: "AS12N76WPYB3QNYKGhV2jZuQs1djdhNJLQgnm7m52pHWecvvj1fCQ", name: "USD Coin", symbol: "USDC", decimals: 6, dex: Dusa }, a_reserve: 12580.916500945, b_reserve: 5206.633765, dex: Dusa, swap_fee: 0.0, a_price: 0.5175336123449099, b_price: 1.9322416479754183, active_bin_id: 8384821, bin_step: 20, bins: {8384820: Bin { id: 8384820, a_reserve: 0.0, b_reserve: 255.062031 }, 8384821: Bin { id: 8384821, a_reserve: 235.23748862, b_reserve: 133.828847 }, 8384822: Bin { id: 8384822, a_reserve: 493.827160493, b_reserve: 0.0 }} }
2025-07-01T00:04:58.811859Z  INFO quedge::spawns::swap_fetcher: Swap event processed successfully
2025-07-01T00:04:58.812133Z DEBUG quedge::helpers: Detecting arbitrage cycles...
2025-07-01T00:04:58.812358Z DEBUG quedge::helpers: Arbitrage cycles found (4 total):
2025-07-01T00:04:58.812499Z DEBUG quedge::helpers: Cycle path Inisde Simulation Task:
2025-07-01T00:04:58.812620Z DEBUG quedge::helpers::display: Path: ["USDC-EagleFi", "USDC-Dusa", "WMAS-Dusa", "WMAS-EagleFi", "USDC-EagleFi"]
2025-07-01T00:04:58.812781Z DEBUG quedge::helpers: Cycle path Inisde Simulation Task:
2025-07-01T00:04:58.812896Z DEBUG quedge::helpers::display: Path: ["WMAS-EagleFi", "USDC-EagleFi", "USDC-Dusa", "WMAS-Dusa", "WMAS-EagleFi"]
2025-07-01T00:04:58.813047Z DEBUG quedge::helpers: Cycle path Inisde Simulation Task:
2025-07-01T00:04:58.813140Z  INFO quedge::helpers: Default start amount for token AS12FW5Rs5YN2zdpEnqwj4iHUUPt9R4Eqjq2qtpJFNKW3mn33RuLU is 10
2025-07-01T00:04:58.813228Z DEBUG quedge::helpers::display: Path: ["USDC-Dusa", "WMAS-Dusa", "WMAS-EagleFi", "USDC-EagleFi", "USDC-Dusa"]
2025-07-01T00:04:58.813326Z  INFO quedge::helpers: Offchain Amount out after Offchain simulation: 16.499424388142604
2025-07-01T00:04:58.813415Z DEBUG quedge::helpers: Cycle path Inisde Simulation Task:
2025-07-01T00:04:58.813510Z  INFO quedge::helpers: Offchain Earning percentage: 64.99424388142604
2025-07-01T00:04:58.813607Z DEBUG quedge::helpers::display: Path: ["WMAS-Dusa", "WMAS-EagleFi", "USDC-EagleFi", "USDC-Dusa", "WMAS-Dusa"]
2025-07-01T00:04:58.813799Z  INFO quedge::helpers: Default start amount for token AS12FW5Rs5YN2zdpEnqwj4iHUUPt9R4Eqjq2qtpJFNKW3mn33RuLU is 10
2025-07-01T00:04:58.813921Z  INFO quedge::helpers: Offchain Amount out after Offchain simulation: 16.499424388142604
2025-07-01T00:04:58.814037Z  INFO quedge::helpers: Offchain Earning percentage: 64.99424388142604
2025-07-01T00:04:58.965368Z  INFO quedge::helpers: Amount out after Onchain simulation: 81.617817649
2025-07-01T00:04:58.965496Z  INFO quedge::helpers: Earning percentage: 63.235635298000005
2025-07-01T00:04:58.966400Z  INFO quedge::helpers: Amount out after Onchain simulation: 81.617817649
2025-07-01T00:04:58.966502Z  INFO quedge::helpers: Earning percentage: 63.235635298000005
2025-07-01T00:04:58.966649Z  INFO quedge::helpers: Found 2 valid cycles
2025-07-01T00:04:58.966822Z  INFO quedge::executor: Executing arbitrage with start amount 50 for cycle path: ["WMAS-Dusa", "WMAS-EagleFi", "USDC-EagleFi", "USDC-Dusa", "WMAS-Dusa"]...
2025-07-01T00:05:14.380668Z  INFO quedge::spawns::swap_fetcher: Found 2 Swap events from the operation O1UpgwQzAmovVBySceoDdMDRWJNjexZFr2yTif5Kuez9eUPGxQZ. Processing them...
2025-07-01T00:05:14.380811Z  INFO quedge::spawns::swap_fetcher: New detected swap on pool: AS12dny2u8p6DmcPnYurnzqrAG7kMjYUcSD6bDCdJfbYeadYqXaL5
2025-07-01T00:05:14.541240Z  INFO quedge::strategies::bellman_ford_matrix: Updating sparse matrix by pool: Pool { address: "AS12dny2u8p6DmcPnYurnzqrAG7kMjYUcSD6bDCdJfbYeadYqXaL5", a_token: Token { address: "AS12N76WPYB3QNYKGhV2jZuQs1djdhNJLQgnm7m52pHWecvvj1fCQ", name: "USD Coin", symbol: "USDC", decimals: 6, dex: EagleFi }, b_token: Token { address: "AS12FW5Rs5YN2zdpEnqwj4iHUUPt9R4Eqjq2qtpJFNKW3mn33RuLU", name: "Wrapped Massa", symbol: "WMAS", decimals: 9, dex: EagleFi }, a_reserve: 3860.393518, b_reserve: 4596.740271648, dex: EagleFi, swap_fee: 0.3, a_price: 1.1907439617786655, b_price: 0.8398111030571651, active_bin_id: 0, bin_step: 0, bins: {} }
2025-07-01T00:05:14.541335Z ERROR quedge::spawns::swap_fetcher: Error in detect_and_execute_arb: Failed to parse amount out to U256
2025-07-01T00:05:14.541435Z  WARN quedge::spawns::swap_fetcher: Skipping arbitrage detection after updating the sparse matrix because of an order from the events stream
2025-07-01T00:05:14.541541Z  INFO quedge::spawns::swap_fetcher: Swap event processed successfully
2025-07-01T00:05:14.541642Z DEBUG quedge::spawns::swap_fetcher: Processing last event of the operation O1UpgwQzAmovVBySceoDdMDRWJNjexZFr2yTif5Kuez9eUPGxQZ...
2025-07-01T00:05:14.541753Z  WARN quedge::spawns::swap_fetcher: Pool AS1VJvdvuU5AZFFKWLD2yxyHsS1CE65imQxQUShqNEf4BqZ2ZHLT not found in the pools hash map
2025-07-01T00:05:14.541844Z  WARN quedge::spawns::swap_fetcher: Pool AS1fewbVp4FMTEJH81e1BNaH9B3SmeE7sNpZQ2QqwP1wGBzLKJGF not found in the pools hash map
2025-07-01T00:05:14.541923Z  WARN quedge::spawns::swap_fetcher: Pool AS1VJvdvuU5AZFFKWLD2yxyHsS1CE65imQxQUShqNEf4BqZ2ZHLT not found in the pools hash map
2025-07-01T00:05:14.542007Z  WARN quedge::spawns::swap_fetcher: Pool AS1XqtvX3rz2RWbnqLfaYVKEjM3VS5pny9yKDdXcmJ5C1vrcLEFd not found in the pools hash map
2025-07-01T00:05:14.542092Z  INFO quedge::spawns::swap_fetcher: New detected swap on pool: AS112Wdy9pM4fvLNLHQXyf7uam9waMPdG5ekr4vxCyQHPkrMMPPY
2025-07-01T00:05:14.849720Z  INFO quedge::strategies::bellman_ford_matrix: Updating sparse matrix by pool: Pool { address: "AS112Wdy9pM4fvLNLHQXyf7uam9waMPdG5ekr4vxCyQHPkrMMPPY", a_token: Token { address: "AS12FW5Rs5YN2zdpEnqwj4iHUUPt9R4Eqjq2qtpJFNKW3mn33RuLU", name: "Wrapped Massa", symbol: "WMAS", decimals: 9, dex: Dusa }, b_token: Token { address: "AS12N76WPYB3QNYKGhV2jZuQs1djdhNJLQgnm7m52pHWecvvj1fCQ", name: "USD Coin", symbol: "USDC", decimals: 6, dex: Dusa }, a_reserve: 12499.298683296, b_reserve: 5248.873729, dex: Dusa, swap_fee: 0.0, a_price: 0.5175336123449099, b_price: 1.9322416479754183, active_bin_id: 8384821, bin_step: 20, bins: {8384820: Bin { id: 8384820, a_reserve: 0.0, b_reserve: 255.062031 }, 8384821: Bin { id: 8384821, a_reserve: 153.619670971, b_reserve: 176.068811 }, 8384822: Bin { id: 8384822, a_reserve: 493.827160493, b_reserve: 0.0 }} }
2025-07-01T00:05:14.849923Z  INFO quedge::spawns::swap_fetcher: Swap event processed successfully
2025-07-01T00:05:14.850148Z DEBUG quedge::helpers: Detecting arbitrage cycles...
2025-07-01T00:05:14.850366Z DEBUG quedge::helpers: Arbitrage cycles found (4 total):
2025-07-01T00:05:14.850521Z DEBUG quedge::helpers: Cycle path Inisde Simulation Task:
2025-07-01T00:05:14.850635Z DEBUG quedge::helpers::display: Path: ["USDC-EagleFi", "USDC-Dusa", "WMAS-Dusa", "WMAS-EagleFi", "USDC-EagleFi"]
2025-07-01T00:05:14.850735Z DEBUG quedge::helpers: Cycle path Inisde Simulation Task:
2025-07-01T00:05:14.850817Z DEBUG quedge::helpers::display: Path: ["WMAS-EagleFi", "USDC-EagleFi", "USDC-Dusa", "WMAS-Dusa", "WMAS-EagleFi"]
2025-07-01T00:05:14.850930Z DEBUG quedge::helpers: Cycle path Inisde Simulation Task:
2025-07-01T00:05:14.851012Z DEBUG quedge::helpers::display: Path: ["USDC-Dusa", "WMAS-Dusa", "WMAS-EagleFi", "USDC-EagleFi", "USDC-Dusa"]
2025-07-01T00:05:14.851113Z DEBUG quedge::helpers: Cycle path Inisde Simulation Task:
2025-07-01T00:05:14.851207Z  INFO quedge::helpers: Default start amount for token AS12FW5Rs5YN2zdpEnqwj4iHUUPt9R4Eqjq2qtpJFNKW3mn33RuLU is 10
2025-07-01T00:05:14.851332Z DEBUG quedge::helpers::display: Path: ["WMAS-Dusa", "WMAS-EagleFi", "USDC-EagleFi", "USDC-Dusa", "WMAS-Dusa"]
2025-07-01T00:05:14.851429Z  INFO quedge::helpers: Offchain Amount out after Offchain simulation: 16.143484298165628
2025-07-01T00:05:14.851561Z  INFO quedge::helpers: Default start amount for token AS12FW5Rs5YN2zdpEnqwj4iHUUPt9R4Eqjq2qtpJFNKW3mn33RuLU is 10
2025-07-01T00:05:14.851630Z  INFO quedge::helpers: Offchain Earning percentage: 61.43484298165628
2025-07-01T00:05:14.851740Z  INFO quedge::helpers: Offchain Amount out after Offchain simulation: 16.143484298165628
2025-07-01T00:05:14.851832Z  INFO quedge::helpers: Offchain Earning percentage: 61.43484298165628
2025-07-01T00:05:15.009993Z  INFO quedge::helpers: Amount out after Onchain simulation: 79.864602054
2025-07-01T00:05:15.010188Z  INFO quedge::helpers: Earning percentage: 59.729204108
2025-07-01T00:05:15.014112Z  INFO quedge::helpers: Amount out after Onchain simulation: 79.864602054
2025-07-01T00:05:15.014215Z  INFO quedge::helpers: Earning percentage: 59.729204108
2025-07-01T00:05:15.014394Z  INFO quedge::helpers: Found 2 valid cycles
2025-07-01T00:05:15.014532Z  INFO quedge::executor: Executing arbitrage with start amount 50 for cycle path: ["WMAS-Dusa", "WMAS-EagleFi", "USDC-EagleFi", "USDC-Dusa", "WMAS-Dusa"]...
2025-07-01T00:05:30.354665Z  INFO quedge::spawns::swap_fetcher: Found 2 Swap events from the operation O1N9SeUeX4ktXuJeaXrYaCJ75KGGBqfCT8nQZv2LjwCFWZQ9VVj. Processing them...
2025-07-01T00:05:30.354816Z  INFO quedge::spawns::swap_fetcher: New detected swap on pool: AS12dny2u8p6DmcPnYurnzqrAG7kMjYUcSD6bDCdJfbYeadYqXaL5
2025-07-01T00:05:30.393921Z ERROR quedge::spawns::swap_fetcher: Error in detect_and_execute_arb: Failed to parse amount out to U256
2025-07-01T00:05:30.505263Z  INFO quedge::strategies::bellman_ford_matrix: Updating sparse matrix by pool: Pool { address: "AS12dny2u8p6DmcPnYurnzqrAG7kMjYUcSD6bDCdJfbYeadYqXaL5", a_token: Token { address: "AS12N76WPYB3QNYKGhV2jZuQs1djdhNJLQgnm7m52pHWecvvj1fCQ", name: "USD Coin", symbol: "USDC", decimals: 6, dex: EagleFi }, b_token: Token { address: "AS12FW5Rs5YN2zdpEnqwj4iHUUPt9R4Eqjq2qtpJFNKW3mn33RuLU", name: "Wrapped Massa", symbol: "WMAS", decimals: 9, dex: EagleFi }, a_reserve: 3818.978071, b_reserve: 4646.702771648, dex: EagleFi, swap_fee: 0.3, a_price: 1.2167398411982135, b_price: 0.8218683782189841, active_bin_id: 0, bin_step: 0, bins: {} }
2025-07-01T00:05:30.505416Z  WARN quedge::spawns::swap_fetcher: Skipping arbitrage detection after updating the sparse matrix because of an order from the events stream
2025-07-01T00:05:30.505508Z  INFO quedge::spawns::swap_fetcher: Swap event processed successfully
2025-07-01T00:05:30.505591Z DEBUG quedge::spawns::swap_fetcher: Processing last event of the operation O1N9SeUeX4ktXuJeaXrYaCJ75KGGBqfCT8nQZv2LjwCFWZQ9VVj...
2025-07-01T00:05:30.505684Z  WARN quedge::spawns::swap_fetcher: Pool AS1VJvdvuU5AZFFKWLD2yxyHsS1CE65imQxQUShqNEf4BqZ2ZHLT not found in the pools hash map
2025-07-01T00:05:30.505779Z  WARN quedge::spawns::swap_fetcher: Pool AS1fewbVp4FMTEJH81e1BNaH9B3SmeE7sNpZQ2QqwP1wGBzLKJGF not found in the pools hash map
2025-07-01T00:05:30.505875Z  WARN quedge::spawns::swap_fetcher: Pool AS1VJvdvuU5AZFFKWLD2yxyHsS1CE65imQxQUShqNEf4BqZ2ZHLT not found in the pools hash map
2025-07-01T00:05:30.505977Z  WARN quedge::spawns::swap_fetcher: Pool AS1XqtvX3rz2RWbnqLfaYVKEjM3VS5pny9yKDdXcmJ5C1vrcLEFd not found in the pools hash map
2025-07-01T00:05:30.506071Z  INFO quedge::spawns::swap_fetcher: New detected swap on pool: AS112Wdy9pM4fvLNLHQXyf7uam9waMPdG5ekr4vxCyQHPkrMMPPY
2025-07-01T00:05:30.808651Z  INFO quedge::strategies::bellman_ford_matrix: Updating sparse matrix by pool: Pool { address: "AS112Wdy9pM4fvLNLHQXyf7uam9waMPdG5ekr4vxCyQHPkrMMPPY", a_token: Token { address: "AS12FW5Rs5YN2zdpEnqwj4iHUUPt9R4Eqjq2qtpJFNKW3mn33RuLU", name: "Wrapped Massa", symbol: "WMAS", decimals: 9, dex: Dusa }, b_token: Token { address: "AS12N76WPYB3QNYKGhV2jZuQs1djdhNJLQgnm7m52pHWecvvj1fCQ", name: "USD Coin", symbol: "USDC", decimals: 6, dex: Dusa }, a_reserve: 12419.434081242, b_reserve: 5290.206345, dex: Dusa, swap_fee: 0.0, a_price: 0.5175336123449099, b_price: 1.9322416479754183, active_bin_id: 8384821, bin_step: 20, bins: {8384820: Bin { id: 8384820, a_reserve: 0.0, b_reserve: 255.062031 }, 8384821: Bin { id: 8384821, a_reserve: 73.755068917, b_reserve: 217.401427 }, 8384822: Bin { id: 8384822, a_reserve: 493.827160493, b_reserve: 0.0 }} }
2025-07-01T00:05:30.808870Z  INFO quedge::spawns::swap_fetcher: Swap event processed successfully
2025-07-01T00:05:30.809087Z DEBUG quedge::helpers: Detecting arbitrage cycles...
2025-07-01T00:05:30.809276Z DEBUG quedge::helpers: Arbitrage cycles found (4 total):
2025-07-01T00:05:30.809436Z DEBUG quedge::helpers: Cycle path Inisde Simulation Task:
2025-07-01T00:05:30.809559Z DEBUG quedge::helpers::display: Path: ["USDC-EagleFi", "USDC-Dusa", "WMAS-Dusa", "WMAS-EagleFi", "USDC-EagleFi"]
2025-07-01T00:05:30.809730Z DEBUG quedge::helpers: Cycle path Inisde Simulation Task:
2025-07-01T00:05:30.809842Z DEBUG quedge::helpers::display: Path: ["WMAS-EagleFi", "USDC-EagleFi", "USDC-Dusa", "WMAS-Dusa", "WMAS-EagleFi"]
2025-07-01T00:05:30.809979Z DEBUG quedge::helpers: Cycle path Inisde Simulation Task:
2025-07-01T00:05:30.810066Z DEBUG quedge::helpers::display: Path: ["USDC-Dusa", "WMAS-Dusa", "WMAS-EagleFi", "USDC-EagleFi", "USDC-Dusa"]
2025-07-01T00:05:30.810171Z DEBUG quedge::helpers: Cycle path Inisde Simulation Task:
2025-07-01T00:05:30.810240Z  INFO quedge::helpers: Default start amount for token AS12FW5Rs5YN2zdpEnqwj4iHUUPt9R4Eqjq2qtpJFNKW3mn33RuLU is 10
2025-07-01T00:05:30.810318Z DEBUG quedge::helpers::display: Path: ["WMAS-Dusa", "WMAS-EagleFi", "USDC-EagleFi", "USDC-Dusa", "WMAS-Dusa"]
2025-07-01T00:05:30.810404Z  INFO quedge::helpers: Offchain Amount out after Offchain simulation: 15.798943316008193
2025-07-01T00:05:30.810482Z  INFO quedge::helpers: Default start amount for token AS12FW5Rs5YN2zdpEnqwj4iHUUPt9R4Eqjq2qtpJFNKW3mn33RuLU is 10
2025-07-01T00:05:30.810551Z  INFO quedge::helpers: Offchain Earning percentage: 57.98943316008194
2025-07-01T00:05:30.810631Z  INFO quedge::helpers: Offchain Amount out after Offchain simulation: 15.798943316008193
2025-07-01T00:05:30.810739Z  INFO quedge::helpers: Offchain Earning percentage: 57.98943316008194
2025-07-01T00:05:30.971270Z  INFO quedge::helpers: Amount out after Onchain simulation: 78.158450937
2025-07-01T00:05:30.971399Z  INFO quedge::helpers: Earning percentage: 56.31690187399999
2025-07-01T00:05:30.973034Z  INFO quedge::helpers: Amount out after Onchain simulation: 78.158450937
2025-07-01T00:05:30.973126Z  INFO quedge::helpers: Earning percentage: 56.31690187399999
2025-07-01T00:05:30.973276Z  INFO quedge::helpers: Found 2 valid cycles
2025-07-01T00:05:30.973472Z  INFO quedge::executor: Executing arbitrage with start amount 50 for cycle path: ["WMAS-Dusa", "WMAS-EagleFi", "USDC-EagleFi", "USDC-Dusa", "WMAS-Dusa"]...
2025-07-01T00:05:46.229919Z  INFO quedge::spawns::swap_fetcher: Found 3 Swap events from the operation O12EfVDY8keTshahPA5i7ryw5PGiZmkTsrwTJDiEU7tydb4L55Xi. Processing them...
2025-07-01T00:05:46.230076Z  INFO quedge::spawns::swap_fetcher: New detected swap on pool: AS12dny2u8p6DmcPnYurnzqrAG7kMjYUcSD6bDCdJfbYeadYqXaL5
2025-07-01T00:05:46.380733Z  INFO quedge::strategies::bellman_ford_matrix: Updating sparse matrix by pool: Pool { address: "AS12dny2u8p6DmcPnYurnzqrAG7kMjYUcSD6bDCdJfbYeadYqXaL5", a_token: Token { address: "AS12N76WPYB3QNYKGhV2jZuQs1djdhNJLQgnm7m52pHWecvvj1fCQ", name: "USD Coin", symbol: "USDC", decimals: 6, dex: EagleFi }, b_token: Token { address: "AS12FW5Rs5YN2zdpEnqwj4iHUUPt9R4Eqjq2qtpJFNKW3mn33RuLU", name: "Wrapped Massa", symbol: "WMAS", decimals: 9, dex: EagleFi }, a_reserve: 3778.442797, b_reserve: 4696.665271648, dex: EagleFi, swap_fee: 0.3, a_price: 1.2430161111283855, b_price: 0.804494801835046, active_bin_id: 0, bin_step: 0, bins: {} }
2025-07-01T00:05:46.380888Z  WARN quedge::spawns::swap_fetcher: Skipping arbitrage detection after updating the sparse matrix because of an order from the events stream
2025-07-01T00:05:46.380993Z  INFO quedge::spawns::swap_fetcher: Swap event processed successfully
2025-07-01T00:05:46.381117Z  INFO quedge::spawns::swap_fetcher: New detected swap on pool: AS112Wdy9pM4fvLNLHQXyf7uam9waMPdG5ekr4vxCyQHPkrMMPPY
2025-07-01T00:05:46.523050Z ERROR quedge::spawns::swap_fetcher: Error in detect_and_execute_arb: Failed to parse amount out to U256
2025-07-01T00:05:46.704716Z  INFO quedge::strategies::bellman_ford_matrix: Updating sparse matrix by pool: Pool { address: "AS112Wdy9pM4fvLNLHQXyf7uam9waMPdG5ekr4vxCyQHPkrMMPPY", a_token: Token { address: "AS12FW5Rs5YN2zdpEnqwj4iHUUPt9R4Eqjq2qtpJFNKW3mn33RuLU", name: "Wrapped Massa", symbol: "WMAS", decimals: 9, dex: Dusa }, b_token: Token { address: "AS12N76WPYB3QNYKGhV2jZuQs1djdhNJLQgnm7m52pHWecvvj1fCQ", name: "USD Coin", symbol: "USDC", decimals: 6, dex: Dusa }, a_reserve: 12341.275630305, b_reserve: 5330.660529, dex: Dusa, swap_fee: 0.0, a_price: 0.5185686795695997, b_price: 1.9283848782189803, active_bin_id: 8384822, bin_step: 20, bins: {8384821: Bin { id: 8384821, a_reserve: 0.0, b_reserve: 255.572155 }, 8384822: Bin { id: 8384822, a_reserve: 489.423778473, b_reserve: 2.283456 }, 8384823: Bin { id: 8384823, a_reserve: 493.827160493, b_reserve: 0.0 }} }
2025-07-01T00:05:46.704840Z  WARN quedge::spawns::swap_fetcher: Skipping arbitrage detection after updating the sparse matrix because of an order from the events stream
2025-07-01T00:05:46.704933Z  INFO quedge::spawns::swap_fetcher: Swap event processed successfully
2025-07-01T00:05:46.705013Z DEBUG quedge::spawns::swap_fetcher: Processing last event of the operation O12EfVDY8keTshahPA5i7ryw5PGiZmkTsrwTJDiEU7tydb4L55Xi...
2025-07-01T00:05:46.705116Z  WARN quedge::spawns::swap_fetcher: Pool AS1VJvdvuU5AZFFKWLD2yxyHsS1CE65imQxQUShqNEf4BqZ2ZHLT not found in the pools hash map
2025-07-01T00:05:46.705202Z  WARN quedge::spawns::swap_fetcher: Pool AS1fewbVp4FMTEJH81e1BNaH9B3SmeE7sNpZQ2QqwP1wGBzLKJGF not found in the pools hash map
2025-07-01T00:05:46.705295Z  WARN quedge::spawns::swap_fetcher: Pool AS1VJvdvuU5AZFFKWLD2yxyHsS1CE65imQxQUShqNEf4BqZ2ZHLT not found in the pools hash map
2025-07-01T00:05:46.705390Z  WARN quedge::spawns::swap_fetcher: Pool AS1XqtvX3rz2RWbnqLfaYVKEjM3VS5pny9yKDdXcmJ5C1vrcLEFd not found in the pools hash map
2025-07-01T00:05:46.705481Z  INFO quedge::spawns::swap_fetcher: New detected swap on pool: AS112Wdy9pM4fvLNLHQXyf7uam9waMPdG5ekr4vxCyQHPkrMMPPY
2025-07-01T00:05:47.025548Z  INFO quedge::strategies::bellman_ford_matrix: Updating sparse matrix by pool: Pool { address: "AS112Wdy9pM4fvLNLHQXyf7uam9waMPdG5ekr4vxCyQHPkrMMPPY", a_token: Token { address: "AS12FW5Rs5YN2zdpEnqwj4iHUUPt9R4Eqjq2qtpJFNKW3mn33RuLU", name: "Wrapped Massa", symbol: "WMAS", decimals: 9, dex: Dusa }, b_token: Token { address: "AS12N76WPYB3QNYKGhV2jZuQs1djdhNJLQgnm7m52pHWecvvj1fCQ", name: "USD Coin", symbol: "USDC", decimals: 6, dex: Dusa }, a_reserve: 12341.275630305, b_reserve: 5330.660529, dex: Dusa, swap_fee: 0.0, a_price: 0.5185686795695997, b_price: 1.9283848782189803, active_bin_id: 8384822, bin_step: 20, bins: {8384821: Bin { id: 8384821, a_reserve: 0.0, b_reserve: 255.572155 }, 8384822: Bin { id: 8384822, a_reserve: 489.423778473, b_reserve: 2.283456 }, 8384823: Bin { id: 8384823, a_reserve: 493.827160493, b_reserve: 0.0 }} }
2025-07-01T00:05:47.025735Z  INFO quedge::spawns::swap_fetcher: Swap event processed successfully
2025-07-01T00:05:47.025935Z DEBUG quedge::helpers: Detecting arbitrage cycles...
2025-07-01T00:05:47.026122Z DEBUG quedge::helpers: Arbitrage cycles found (4 total):
2025-07-01T00:05:47.026247Z DEBUG quedge::helpers: Cycle path Inisde Simulation Task:
2025-07-01T00:05:47.026366Z DEBUG quedge::helpers::display: Path: ["USDC-EagleFi", "USDC-Dusa", "WMAS-Dusa", "WMAS-EagleFi", "USDC-EagleFi"]
2025-07-01T00:05:47.026495Z DEBUG quedge::helpers: Cycle path Inisde Simulation Task:
2025-07-01T00:05:47.026601Z DEBUG quedge::helpers::display: Path: ["WMAS-EagleFi", "USDC-EagleFi", "USDC-Dusa", "WMAS-Dusa", "WMAS-EagleFi"]
2025-07-01T00:05:47.026736Z DEBUG quedge::helpers: Cycle path Inisde Simulation Task:
2025-07-01T00:05:47.026816Z  INFO quedge::helpers: Default start amount for token AS12FW5Rs5YN2zdpEnqwj4iHUUPt9R4Eqjq2qtpJFNKW3mn33RuLU is 10
2025-07-01T00:05:47.026895Z DEBUG quedge::helpers::display: Path: ["USDC-Dusa", "WMAS-Dusa", "WMAS-EagleFi", "USDC-EagleFi", "USDC-Dusa"]
2025-07-01T00:05:47.026985Z  INFO quedge::helpers: Offchain Amount out after Offchain simulation: 15.43445085037953
2025-07-01T00:05:47.027076Z DEBUG quedge::helpers: Cycle path Inisde Simulation Task:
2025-07-01T00:05:47.027157Z  INFO quedge::helpers: Offchain Earning percentage: 54.34450850379531
2025-07-01T00:05:47.027246Z DEBUG quedge::helpers::display: Path: ["WMAS-Dusa", "WMAS-EagleFi", "USDC-EagleFi", "USDC-Dusa", "WMAS-Dusa"]
2025-07-01T00:05:47.027386Z  INFO quedge::helpers: Default start amount for token AS12FW5Rs5YN2zdpEnqwj4iHUUPt9R4Eqjq2qtpJFNKW3mn33RuLU is 10
2025-07-01T00:05:47.027520Z  INFO quedge::helpers: Offchain Amount out after Offchain simulation: 15.43445085037953
2025-07-01T00:05:47.027646Z  INFO quedge::helpers: Offchain Earning percentage: 54.34450850379531
2025-07-01T00:05:47.183900Z  INFO quedge::helpers: Amount out after Onchain simulation: 76.37080788
2025-07-01T00:05:47.184087Z  INFO quedge::helpers: Earning percentage: 52.74161576
2025-07-01T00:05:47.187610Z  INFO quedge::helpers: Amount out after Onchain simulation: 76.37080788
2025-07-01T00:05:47.187738Z  INFO quedge::helpers: Earning percentage: 52.74161576
2025-07-01T00:05:47.187907Z  INFO quedge::helpers: Found 2 valid cycles
2025-07-01T00:05:47.188071Z  INFO quedge::executor: Executing arbitrage with start amount 50 for cycle path: ["WMAS-Dusa", "WMAS-EagleFi", "USDC-EagleFi", "USDC-Dusa", "WMAS-Dusa"]...
2025-07-01T00:06:02.481644Z  INFO quedge::spawns::swap_fetcher: Found 2 Swap events from the operation O12ABa1zMSeuZBm8wboSFrgcQPEnbjYBuS6q7t9Af7kxMXFKyhjF. Processing them...
2025-07-01T00:06:02.481832Z  INFO quedge::spawns::swap_fetcher: New detected swap on pool: AS12dny2u8p6DmcPnYurnzqrAG7kMjYUcSD6bDCdJfbYeadYqXaL5
2025-07-01T00:06:02.599284Z ERROR quedge::spawns::swap_fetcher: Error in detect_and_execute_arb: Failed to parse amount out to U256
2025-07-01T00:06:02.632524Z  INFO quedge::strategies::bellman_ford_matrix: Updating sparse matrix by pool: Pool { address: "AS12dny2u8p6DmcPnYurnzqrAG7kMjYUcSD6bDCdJfbYeadYqXaL5", a_token: Token { address: "AS12N76WPYB3QNYKGhV2jZuQs1djdhNJLQgnm7m52pHWecvvj1fCQ", name: "USD Coin", symbol: "USDC", decimals: 6, dex: EagleFi }, b_token: Token { address: "AS12FW5Rs5YN2zdpEnqwj4iHUUPt9R4Eqjq2qtpJFNKW3mn33RuLU", name: "Wrapped Massa", symbol: "WMAS", decimals: 9, dex: EagleFi }, a_reserve: 3738.759922, b_reserve: 4746.627771648, dex: EagleFi, swap_fee: 0.3, a_price: 1.2695727649473825, b_price: 0.7876665502047416, active_bin_id: 0, bin_step: 0, bins: {} }
2025-07-01T00:06:02.632696Z  WARN quedge::spawns::swap_fetcher: Skipping arbitrage detection after updating the sparse matrix because of an order from the events stream
2025-07-01T00:06:02.632815Z  INFO quedge::spawns::swap_fetcher: Swap event processed successfully
2025-07-01T00:06:02.632933Z DEBUG quedge::spawns::swap_fetcher: Processing last event of the operation O12ABa1zMSeuZBm8wboSFrgcQPEnbjYBuS6q7t9Af7kxMXFKyhjF...
2025-07-01T00:06:02.633069Z  WARN quedge::spawns::swap_fetcher: Pool AS1VJvdvuU5AZFFKWLD2yxyHsS1CE65imQxQUShqNEf4BqZ2ZHLT not found in the pools hash map
2025-07-01T00:06:02.633193Z  WARN quedge::spawns::swap_fetcher: Pool AS1fewbVp4FMTEJH81e1BNaH9B3SmeE7sNpZQ2QqwP1wGBzLKJGF not found in the pools hash map
2025-07-01T00:06:02.633302Z  WARN quedge::spawns::swap_fetcher: Pool AS1VJvdvuU5AZFFKWLD2yxyHsS1CE65imQxQUShqNEf4BqZ2ZHLT not found in the pools hash map
2025-07-01T00:06:02.633397Z  WARN quedge::spawns::swap_fetcher: Pool AS1XqtvX3rz2RWbnqLfaYVKEjM3VS5pny9yKDdXcmJ5C1vrcLEFd not found in the pools hash map
2025-07-01T00:06:02.633509Z  INFO quedge::spawns::swap_fetcher: New detected swap on pool: AS112Wdy9pM4fvLNLHQXyf7uam9waMPdG5ekr4vxCyQHPkrMMPPY
2025-07-01T00:06:02.948923Z  INFO quedge::strategies::bellman_ford_matrix: Updating sparse matrix by pool: Pool { address: "AS112Wdy9pM4fvLNLHQXyf7uam9waMPdG5ekr4vxCyQHPkrMMPPY", a_token: Token { address: "AS12FW5Rs5YN2zdpEnqwj4iHUUPt9R4Eqjq2qtpJFNKW3mn33RuLU", name: "Wrapped Massa", symbol: "WMAS", decimals: 9, dex: Dusa }, b_token: Token { address: "AS12N76WPYB3QNYKGhV2jZuQs1djdhNJLQgnm7m52pHWecvvj1fCQ", name: "USD Coin", symbol: "USDC", decimals: 6, dex: Dusa }, a_reserve: 12264.904822425, b_reserve: 5370.264038, dex: Dusa, swap_fee: 0.0, a_price: 0.5185686795695997, b_price: 1.9283848782189803, active_bin_id: 8384822, bin_step: 20, bins: {8384821: Bin { id: 8384821, a_reserve: 0.0, b_reserve: 255.572155 }, 8384822: Bin { id: 8384822, a_reserve: 413.052970593, b_reserve: 41.886965 }, 8384823: Bin { id: 8384823, a_reserve: 493.827160493, b_reserve: 0.0 }} }
2025-07-01T00:06:02.949138Z  INFO quedge::spawns::swap_fetcher: Swap event processed successfully
2025-07-01T00:06:02.949355Z DEBUG quedge::helpers: Detecting arbitrage cycles...
2025-07-01T00:06:02.949560Z DEBUG quedge::helpers: Arbitrage cycles found (4 total):
2025-07-01T00:06:02.949682Z DEBUG quedge::helpers: Cycle path Inisde Simulation Task:
2025-07-01T00:06:02.949800Z DEBUG quedge::helpers::display: Path: ["USDC-EagleFi", "USDC-Dusa", "WMAS-Dusa", "WMAS-EagleFi", "USDC-EagleFi"]
2025-07-01T00:06:02.949938Z DEBUG quedge::helpers: Cycle path Inisde Simulation Task:
2025-07-01T00:06:02.950057Z DEBUG quedge::helpers::display: Path: ["WMAS-EagleFi", "USDC-EagleFi", "USDC-Dusa", "WMAS-Dusa", "WMAS-EagleFi"]
2025-07-01T00:06:02.950207Z DEBUG quedge::helpers: Cycle path Inisde Simulation Task:
2025-07-01T00:06:02.950316Z DEBUG quedge::helpers::display: Path: ["USDC-Dusa", "WMAS-Dusa", "WMAS-EagleFi", "USDC-EagleFi", "USDC-Dusa"]
2025-07-01T00:06:02.950523Z DEBUG quedge::helpers: Cycle path Inisde Simulation Task:
2025-07-01T00:06:02.950609Z  INFO quedge::helpers: Default start amount for token AS12FW5Rs5YN2zdpEnqwj4iHUUPt9R4Eqjq2qtpJFNKW3mn33RuLU is 10
2025-07-01T00:06:02.950699Z DEBUG quedge::helpers::display: Path: ["WMAS-Dusa", "WMAS-EagleFi", "USDC-EagleFi", "USDC-Dusa", "WMAS-Dusa"]
2025-07-01T00:06:02.950811Z  INFO quedge::helpers: Offchain Amount out after Offchain simulation: 15.111933229735548
2025-07-01T00:06:02.950907Z  INFO quedge::helpers: Default start amount for token AS12FW5Rs5YN2zdpEnqwj4iHUUPt9R4Eqjq2qtpJFNKW3mn33RuLU is 10
2025-07-01T00:06:02.951001Z  INFO quedge::helpers: Offchain Earning percentage: 51.11933229735548
2025-07-01T00:06:02.951091Z  INFO quedge::helpers: Offchain Amount out after Offchain simulation: 15.111933229735548
2025-07-01T00:06:02.951224Z  INFO quedge::helpers: Offchain Earning percentage: 51.11933229735548
2025-07-01T00:06:03.119785Z  INFO quedge::helpers: Amount out after Onchain simulation: 74.781564193
2025-07-01T00:06:03.119930Z  INFO quedge::helpers: Amount out after Onchain simulation: 74.781564193
2025-07-01T00:06:03.120037Z  INFO quedge::helpers: Earning percentage: 49.56312838599999
2025-07-01T00:06:03.120128Z  INFO quedge::helpers: Earning percentage: 49.56312838599999
2025-07-01T00:06:03.120286Z  INFO quedge::helpers: Found 2 valid cycles
2025-07-01T00:06:03.120534Z  INFO quedge::executor: Executing arbitrage with start amount 50 for cycle path: ["WMAS-Dusa", "WMAS-EagleFi", "USDC-EagleFi", "USDC-Dusa", "WMAS-Dusa"]...
2025-07-01T00:06:18.359105Z  INFO quedge::spawns::swap_fetcher: Found 2 Swap events from the operation O1dUb7v7u8ANfrPeQ9Tg8DCg8GyqfrWbXtLb7VKSBRpfjf4dNdW. Processing them...
2025-07-01T00:06:18.359259Z  INFO quedge::spawns::swap_fetcher: New detected swap on pool: AS12dny2u8p6DmcPnYurnzqrAG7kMjYUcSD6bDCdJfbYeadYqXaL5
2025-07-01T00:06:18.519506Z  INFO quedge::strategies::bellman_ford_matrix: Updating sparse matrix by pool: Pool { address: "AS12dny2u8p6DmcPnYurnzqrAG7kMjYUcSD6bDCdJfbYeadYqXaL5", a_token: Token { address: "AS12N76WPYB3QNYKGhV2jZuQs1djdhNJLQgnm7m52pHWecvvj1fCQ", name: "USD Coin", symbol: "USDC", decimals: 6, dex: EagleFi }, b_token: Token { address: "AS12FW5Rs5YN2zdpEnqwj4iHUUPt9R4Eqjq2qtpJFNKW3mn33RuLU", name: "Wrapped Massa", symbol: "WMAS", decimals: 9, dex: EagleFi }, a_reserve: 3699.90283, b_reserve: 4796.590271648, dex: EagleFi, swap_fee: 0.3, a_price: 1.2964097956183354, b_price: 0.7713610336637732, active_bin_id: 0, bin_step: 0, bins: {} }
2025-07-01T00:06:18.519669Z  WARN quedge::spawns::swap_fetcher: Skipping arbitrage detection after updating the sparse matrix because of an order from the events stream
2025-07-01T00:06:18.519789Z  INFO quedge::spawns::swap_fetcher: Swap event processed successfully
2025-07-01T00:06:18.519892Z DEBUG quedge::spawns::swap_fetcher: Processing last event of the operation O1dUb7v7u8ANfrPeQ9Tg8DCg8GyqfrWbXtLb7VKSBRpfjf4dNdW...
2025-07-01T00:06:18.520009Z  WARN quedge::spawns::swap_fetcher: Pool AS1VJvdvuU5AZFFKWLD2yxyHsS1CE65imQxQUShqNEf4BqZ2ZHLT not found in the pools hash map
2025-07-01T00:06:18.520113Z  WARN quedge::spawns::swap_fetcher: Pool AS1fewbVp4FMTEJH81e1BNaH9B3SmeE7sNpZQ2QqwP1wGBzLKJGF not found in the pools hash map
2025-07-01T00:06:18.520220Z  WARN quedge::spawns::swap_fetcher: Pool AS1VJvdvuU5AZFFKWLD2yxyHsS1CE65imQxQUShqNEf4BqZ2ZHLT not found in the pools hash map
2025-07-01T00:06:18.520335Z  WARN quedge::spawns::swap_fetcher: Pool AS1XqtvX3rz2RWbnqLfaYVKEjM3VS5pny9yKDdXcmJ5C1vrcLEFd not found in the pools hash map
2025-07-01T00:06:18.520466Z  INFO quedge::spawns::swap_fetcher: New detected swap on pool: AS112Wdy9pM4fvLNLHQXyf7uam9waMPdG5ekr4vxCyQHPkrMMPPY
2025-07-01T00:06:18.545545Z ERROR quedge::spawns::swap_fetcher: Error in detect_and_execute_arb: Failed to parse amount out to U256
2025-07-01T00:06:18.839370Z  INFO quedge::strategies::bellman_ford_matrix: Updating sparse matrix by pool: Pool { address: "AS112Wdy9pM4fvLNLHQXyf7uam9waMPdG5ekr4vxCyQHPkrMMPPY", a_token: Token { address: "AS12FW5Rs5YN2zdpEnqwj4iHUUPt9R4Eqjq2qtpJFNKW3mn33RuLU", name: "Wrapped Massa", symbol: "WMAS", decimals: 9, dex: Dusa }, b_token: Token { address: "AS12N76WPYB3QNYKGhV2jZuQs1djdhNJLQgnm7m52pHWecvvj1fCQ", name: "USD Coin", symbol: "USDC", decimals: 6, dex: Dusa }, a_reserve: 12190.123258232, b_reserve: 5409.043415, dex: Dusa, swap_fee: 0.0, a_price: 0.5185686795695997, b_price: 1.9283848782189803, active_bin_id: 8384822, bin_step: 20, bins: {8384821: Bin { id: 8384821, a_reserve: 0.0, b_reserve: 255.572155 }, 8384822: Bin { id: 8384822, a_reserve: 338.2714064, b_reserve: 80.666342 }, 8384823: Bin { id: 8384823, a_reserve: 493.827160493, b_reserve: 0.0 }} }
2025-07-01T00:06:18.839589Z  INFO quedge::spawns::swap_fetcher: Swap event processed successfully
2025-07-01T00:06:18.839817Z DEBUG quedge::helpers: Detecting arbitrage cycles...
2025-07-01T00:06:18.839990Z DEBUG quedge::helpers: Arbitrage cycles found (4 total):
2025-07-01T00:06:18.840113Z DEBUG quedge::helpers: Cycle path Inisde Simulation Task:
2025-07-01T00:06:18.840235Z DEBUG quedge::helpers::display: Path: ["USDC-EagleFi", "USDC-Dusa", "WMAS-Dusa", "WMAS-EagleFi", "USDC-EagleFi"]
2025-07-01T00:06:18.840423Z DEBUG quedge::helpers: Cycle path Inisde Simulation Task:
2025-07-01T00:06:18.840543Z DEBUG quedge::helpers::display: Path: ["WMAS-EagleFi", "USDC-EagleFi", "USDC-Dusa", "WMAS-Dusa", "WMAS-EagleFi"]
2025-07-01T00:06:18.840706Z DEBUG quedge::helpers: Cycle path Inisde Simulation Task:
2025-07-01T00:06:18.840800Z  INFO quedge::helpers: Default start amount for token AS12FW5Rs5YN2zdpEnqwj4iHUUPt9R4Eqjq2qtpJFNKW3mn33RuLU is 10
2025-07-01T00:06:18.840888Z DEBUG quedge::helpers::display: Path: ["USDC-Dusa", "WMAS-Dusa", "WMAS-EagleFi", "USDC-EagleFi", "USDC-Dusa"]
2025-07-01T00:06:18.840993Z  INFO quedge::helpers: Offchain Amount out after Offchain simulation: 14.799423613194202
2025-07-01T00:06:18.841077Z DEBUG quedge::helpers: Cycle path Inisde Simulation Task:
2025-07-01T00:06:18.841160Z  INFO quedge::helpers: Offchain Earning percentage: 47.99423613194202
2025-07-01T00:06:18.841242Z DEBUG quedge::helpers::display: Path: ["WMAS-Dusa", "WMAS-EagleFi", "USDC-EagleFi", "USDC-Dusa", "WMAS-Dusa"]
2025-07-01T00:06:18.841424Z  INFO quedge::helpers: Default start amount for token AS12FW5Rs5YN2zdpEnqwj4iHUUPt9R4Eqjq2qtpJFNKW3mn33RuLU is 10
2025-07-01T00:06:18.841534Z  INFO quedge::helpers: Offchain Amount out after Offchain simulation: 14.799423613194202
2025-07-01T00:06:18.841634Z  INFO quedge::helpers: Offchain Earning percentage: 47.99423613194202
2025-07-01T00:06:19.003430Z  INFO quedge::helpers: Amount out after Onchain simulation: 73.241438398
2025-07-01T00:06:19.003573Z  INFO quedge::helpers: Earning percentage: 46.482876796
2025-07-01T00:06:19.007338Z  INFO quedge::helpers: Amount out after Onchain simulation: 73.241438398
2025-07-01T00:06:19.007479Z  INFO quedge::helpers: Earning percentage: 46.482876796
2025-07-01T00:06:19.007659Z  INFO quedge::helpers: Found 2 valid cycles
2025-07-01T00:06:19.007858Z  INFO quedge::executor: Executing arbitrage with start amount 50 for cycle path: ["WMAS-Dusa", "WMAS-EagleFi", "USDC-EagleFi", "USDC-Dusa", "WMAS-Dusa"]...
2025-07-01T00:06:34.365651Z  INFO quedge::spawns::swap_fetcher: Found 2 Swap events from the operation O1xeW95u8EKBtGf3RjtKi7vguWxQ9LeK4XXApNwsDZnoNv8xnSL. Processing them...
2025-07-01T00:06:34.365838Z  INFO quedge::spawns::swap_fetcher: New detected swap on pool: AS12dny2u8p6DmcPnYurnzqrAG7kMjYUcSD6bDCdJfbYeadYqXaL5
2025-07-01T00:06:34.515703Z ERROR quedge::spawns::swap_fetcher: Error in detect_and_execute_arb: Failed to parse amount out to U256
2025-07-01T00:06:34.523645Z  INFO quedge::strategies::bellman_ford_matrix: Updating sparse matrix by pool: Pool { address: "AS12dny2u8p6DmcPnYurnzqrAG7kMjYUcSD6bDCdJfbYeadYqXaL5", a_token: Token { address: "AS12N76WPYB3QNYKGhV2jZuQs1djdhNJLQgnm7m52pHWecvvj1fCQ", name: "USD Coin", symbol: "USDC", decimals: 6, dex: EagleFi }, b_token: Token { address: "AS12FW5Rs5YN2zdpEnqwj4iHUUPt9R4Eqjq2qtpJFNKW3mn33RuLU", name: "Wrapped Massa", symbol: "WMAS", decimals: 9, dex: EagleFi }, a_reserve: 3661.846, b_reserve: 4846.552771648, dex: EagleFi, swap_fee: 0.3, a_price: 1.3235271968422484, b_price: 0.7555568199776028, active_bin_id: 0, bin_step: 0, bins: {} }
2025-07-01T00:06:34.523820Z  WARN quedge::spawns::swap_fetcher: Skipping arbitrage detection after updating the sparse matrix because of an order from the events stream
2025-07-01T00:06:34.523986Z  INFO quedge::spawns::swap_fetcher: Swap event processed successfully
2025-07-01T00:06:34.524158Z DEBUG quedge::spawns::swap_fetcher: Processing last event of the operation O1xeW95u8EKBtGf3RjtKi7vguWxQ9LeK4XXApNwsDZnoNv8xnSL...
2025-07-01T00:06:34.524285Z  WARN quedge::spawns::swap_fetcher: Pool AS1VJvdvuU5AZFFKWLD2yxyHsS1CE65imQxQUShqNEf4BqZ2ZHLT not found in the pools hash map
2025-07-01T00:06:34.524406Z  WARN quedge::spawns::swap_fetcher: Pool AS1fewbVp4FMTEJH81e1BNaH9B3SmeE7sNpZQ2QqwP1wGBzLKJGF not found in the pools hash map
2025-07-01T00:06:34.524528Z  WARN quedge::spawns::swap_fetcher: Pool AS1VJvdvuU5AZFFKWLD2yxyHsS1CE65imQxQUShqNEf4BqZ2ZHLT not found in the pools hash map
2025-07-01T00:06:34.524652Z  WARN quedge::spawns::swap_fetcher: Pool AS1XqtvX3rz2RWbnqLfaYVKEjM3VS5pny9yKDdXcmJ5C1vrcLEFd not found in the pools hash map
2025-07-01T00:06:34.524806Z  INFO quedge::spawns::swap_fetcher: New detected swap on pool: AS112Wdy9pM4fvLNLHQXyf7uam9waMPdG5ekr4vxCyQHPkrMMPPY
2025-07-01T00:06:34.838905Z  INFO quedge::strategies::bellman_ford_matrix: Updating sparse matrix by pool: Pool { address: "AS112Wdy9pM4fvLNLHQXyf7uam9waMPdG5ekr4vxCyQHPkrMMPPY", a_token: Token { address: "AS12FW5Rs5YN2zdpEnqwj4iHUUPt9R4Eqjq2qtpJFNKW3mn33RuLU", name: "Wrapped Massa", symbol: "WMAS", decimals: 9, dex: Dusa }, b_token: Token { address: "AS12N76WPYB3QNYKGhV2jZuQs1djdhNJLQgnm7m52pHWecvvj1fCQ", name: "USD Coin", symbol: "USDC", decimals: 6, dex: Dusa }, a_reserve: 12116.881819834, b_reserve: 5447.024131, dex: Dusa, swap_fee: 0.0, a_price: 0.5185686795695997, b_price: 1.9283848782189803, active_bin_id: 8384822, bin_step: 20, bins: {8384821: Bin { id: 8384821, a_reserve: 0.0, b_reserve: 255.572155 }, 8384822: Bin { id: 8384822, a_reserve: 265.029968002, b_reserve: 118.647058 }, 8384823: Bin { id: 8384823, a_reserve: 493.827160493, b_reserve: 0.0 }} }
2025-07-01T00:06:34.839149Z  INFO quedge::spawns::swap_fetcher: Swap event processed successfully
2025-07-01T00:06:34.839387Z DEBUG quedge::helpers: Detecting arbitrage cycles...
2025-07-01T00:06:34.839582Z DEBUG quedge::helpers: Arbitrage cycles found (4 total):
2025-07-01T00:06:34.839710Z DEBUG quedge::helpers: Cycle path Inisde Simulation Task:
2025-07-01T00:06:34.839823Z DEBUG quedge::helpers::display: Path: ["USDC-EagleFi", "USDC-Dusa", "WMAS-Dusa", "WMAS-EagleFi", "USDC-EagleFi"]
2025-07-01T00:06:34.839937Z DEBUG quedge::helpers: Cycle path Inisde Simulation Task:
2025-07-01T00:06:34.840039Z DEBUG quedge::helpers::display: Path: ["WMAS-EagleFi", "USDC-EagleFi", "USDC-Dusa", "WMAS-Dusa", "WMAS-EagleFi"]
2025-07-01T00:06:34.840158Z DEBUG quedge::helpers: Cycle path Inisde Simulation Task:
2025-07-01T00:06:34.840263Z DEBUG quedge::helpers::display: Path: ["USDC-Dusa", "WMAS-Dusa", "WMAS-EagleFi", "USDC-EagleFi", "USDC-Dusa"]
2025-07-01T00:06:34.840400Z DEBUG quedge::helpers: Cycle path Inisde Simulation Task:
2025-07-01T00:06:34.840497Z  INFO quedge::helpers: Default start amount for token AS12FW5Rs5YN2zdpEnqwj4iHUUPt9R4Eqjq2qtpJFNKW3mn33RuLU is 10
2025-07-01T00:06:34.840608Z DEBUG quedge::helpers::display: Path: ["WMAS-Dusa", "WMAS-EagleFi", "USDC-EagleFi", "USDC-Dusa", "WMAS-Dusa"]
2025-07-01T00:06:34.840704Z  INFO quedge::helpers: Offchain Amount out after Offchain simulation: 14.496512089902826
2025-07-01T00:06:34.840801Z  INFO quedge::helpers: Default start amount for token AS12FW5Rs5YN2zdpEnqwj4iHUUPt9R4Eqjq2qtpJFNKW3mn33RuLU is 10
2025-07-01T00:06:34.840899Z  INFO quedge::helpers: Offchain Earning percentage: 44.96512089902826
2025-07-01T00:06:34.840992Z  INFO quedge::helpers: Offchain Amount out after Offchain simulation: 14.496512089902826
2025-07-01T00:06:34.841141Z  INFO quedge::helpers: Offchain Earning percentage: 44.96512089902826
2025-07-01T00:06:35.017037Z  INFO quedge::helpers: Amount out after Onchain simulation: 71.748421117
2025-07-01T00:06:35.017152Z  INFO quedge::helpers: Amount out after Onchain simulation: 71.748421117
2025-07-01T00:06:35.017246Z  INFO quedge::helpers: Earning percentage: 43.49684223400001
2025-07-01T00:06:35.017333Z  INFO quedge::helpers: Earning percentage: 43.49684223400001
2025-07-01T00:06:35.017476Z  INFO quedge::helpers: Found 2 valid cycles
2025-07-01T00:06:35.017639Z  INFO quedge::executor: Executing arbitrage with start amount 50 for cycle path: ["WMAS-Dusa", "WMAS-EagleFi", "USDC-EagleFi", "USDC-Dusa", "WMAS-Dusa"]...
2025-07-01T00:06:50.352450Z  INFO quedge::spawns::swap_fetcher: Found 2 Swap events from the operation O12UbYSiR6obvAvxU1vHntaJcixUKMQNSe1c1hhdRmwQsR9CA69G. Processing them...
2025-07-01T00:06:50.352598Z  INFO quedge::spawns::swap_fetcher: New detected swap on pool: AS12dny2u8p6DmcPnYurnzqrAG7kMjYUcSD6bDCdJfbYeadYqXaL5
2025-07-01T00:06:50.388117Z ERROR quedge::spawns::swap_fetcher: Error in detect_and_execute_arb: Failed to parse amount out to U256
2025-07-01T00:06:50.507463Z  INFO quedge::strategies::bellman_ford_matrix: Updating sparse matrix by pool: Pool { address: "AS12dny2u8p6DmcPnYurnzqrAG7kMjYUcSD6bDCdJfbYeadYqXaL5", a_token: Token { address: "AS12N76WPYB3QNYKGhV2jZuQs1djdhNJLQgnm7m52pHWecvvj1fCQ", name: "USD Coin", symbol: "USDC", decimals: 6, dex: EagleFi }, b_token: Token { address: "AS12FW5Rs5YN2zdpEnqwj4iHUUPt9R4Eqjq2qtpJFNKW3mn33RuLU", name: "Wrapped Massa", symbol: "WMAS", decimals: 9, dex: EagleFi }, a_reserve: 3624.564953, b_reserve: 4896.515271648, dex: EagleFi, swap_fee: 0.3, a_price: 1.350924962068958, b_price: 0.7402335644671839, active_bin_id: 0, bin_step: 0, bins: {} }
2025-07-01T00:06:50.507584Z  WARN quedge::spawns::swap_fetcher: Skipping arbitrage detection after updating the sparse matrix because of an order from the events stream
2025-07-01T00:06:50.507661Z  INFO quedge::spawns::swap_fetcher: Swap event processed successfully
2025-07-01T00:06:50.507738Z DEBUG quedge::spawns::swap_fetcher: Processing last event of the operation O12UbYSiR6obvAvxU1vHntaJcixUKMQNSe1c1hhdRmwQsR9CA69G...
2025-07-01T00:06:50.507811Z  WARN quedge::spawns::swap_fetcher: Pool AS1VJvdvuU5AZFFKWLD2yxyHsS1CE65imQxQUShqNEf4BqZ2ZHLT not found in the pools hash map
2025-07-01T00:06:50.507889Z  WARN quedge::spawns::swap_fetcher: Pool AS1fewbVp4FMTEJH81e1BNaH9B3SmeE7sNpZQ2QqwP1wGBzLKJGF not found in the pools hash map
2025-07-01T00:06:50.507960Z  WARN quedge::spawns::swap_fetcher: Pool AS1VJvdvuU5AZFFKWLD2yxyHsS1CE65imQxQUShqNEf4BqZ2ZHLT not found in the pools hash map
2025-07-01T00:06:50.508032Z  WARN quedge::spawns::swap_fetcher: Pool AS1XqtvX3rz2RWbnqLfaYVKEjM3VS5pny9yKDdXcmJ5C1vrcLEFd not found in the pools hash map
2025-07-01T00:06:50.508112Z  INFO quedge::spawns::swap_fetcher: New detected swap on pool: AS112Wdy9pM4fvLNLHQXyf7uam9waMPdG5ekr4vxCyQHPkrMMPPY
2025-07-01T00:06:50.816065Z  INFO quedge::strategies::bellman_ford_matrix: Updating sparse matrix by pool: Pool { address: "AS112Wdy9pM4fvLNLHQXyf7uam9waMPdG5ekr4vxCyQHPkrMMPPY", a_token: Token { address: "AS12FW5Rs5YN2zdpEnqwj4iHUUPt9R4Eqjq2qtpJFNKW3mn33RuLU", name: "Wrapped Massa", symbol: "WMAS", decimals: 9, dex: Dusa }, b_token: Token { address: "AS12N76WPYB3QNYKGhV2jZuQs1djdhNJLQgnm7m52pHWecvvj1fCQ", name: "USD Coin", symbol: "USDC", decimals: 6, dex: Dusa }, a_reserve: 12045.133398717, b_reserve: 5484.230615, dex: Dusa, swap_fee: 0.0, a_price: 0.5185686795695997, b_price: 1.9283848782189803, active_bin_id: 8384822, bin_step: 20, bins: {8384821: Bin { id: 8384821, a_reserve: 0.0, b_reserve: 255.572155 }, 8384822: Bin { id: 8384822, a_reserve: 193.281546885, b_reserve: 155.853542 }, 8384823: Bin { id: 8384823, a_reserve: 493.827160493, b_reserve: 0.0 }} }
2025-07-01T00:06:50.816318Z  INFO quedge::spawns::swap_fetcher: Swap event processed successfully
2025-07-01T00:06:50.816529Z DEBUG quedge::helpers: Detecting arbitrage cycles...
2025-07-01T00:06:50.816694Z DEBUG quedge::helpers: Arbitrage cycles found (4 total):
2025-07-01T00:06:50.816799Z DEBUG quedge::helpers: Cycle path Inisde Simulation Task:
2025-07-01T00:06:50.816890Z DEBUG quedge::helpers::display: Path: ["USDC-EagleFi", "USDC-Dusa", "WMAS-Dusa", "WMAS-EagleFi", "USDC-EagleFi"]
2025-07-01T00:06:50.816981Z DEBUG quedge::helpers: Cycle path Inisde Simulation Task:
2025-07-01T00:06:50.817051Z DEBUG quedge::helpers::display: Path: ["WMAS-EagleFi", "USDC-EagleFi", "USDC-Dusa", "WMAS-Dusa", "WMAS-EagleFi"]
2025-07-01T00:06:50.817177Z DEBUG quedge::helpers: Cycle path Inisde Simulation Task:
2025-07-01T00:06:50.817254Z DEBUG quedge::helpers::display: Path: ["USDC-Dusa", "WMAS-Dusa", "WMAS-EagleFi", "USDC-EagleFi", "USDC-Dusa"]
2025-07-01T00:06:50.817378Z DEBUG quedge::helpers: Cycle path Inisde Simulation Task:
2025-07-01T00:06:50.817480Z  INFO quedge::helpers: Default start amount for token AS12FW5Rs5YN2zdpEnqwj4iHUUPt9R4Eqjq2qtpJFNKW3mn33RuLU is 10
2025-07-01T00:06:50.817551Z DEBUG quedge::helpers::display: Path: ["WMAS-Dusa", "WMAS-EagleFi", "USDC-EagleFi", "USDC-Dusa", "WMAS-Dusa"]
2025-07-01T00:06:50.817663Z  INFO quedge::helpers: Offchain Amount out after Offchain simulation: 14.202809528480667
2025-07-01T00:06:50.817735Z  INFO quedge::helpers: Default start amount for token AS12FW5Rs5YN2zdpEnqwj4iHUUPt9R4Eqjq2qtpJFNKW3mn33RuLU is 10
2025-07-01T00:06:50.817805Z  INFO quedge::helpers: Offchain Earning percentage: 42.028095284806675
2025-07-01T00:06:50.817871Z  INFO quedge::helpers: Offchain Amount out after Offchain simulation: 14.202809528480667
2025-07-01T00:06:50.817971Z  INFO quedge::helpers: Offchain Earning percentage: 42.028095284806675
2025-07-01T00:06:50.978148Z  INFO quedge::helpers: Amount out after Onchain simulation: 70.300612891
2025-07-01T00:06:50.978260Z  INFO quedge::helpers: Earning percentage: 40.601225782
2025-07-01T00:06:50.986207Z  INFO quedge::helpers: Amount out after Onchain simulation: 70.300612891
2025-07-01T00:06:50.986344Z  INFO quedge::helpers: Earning percentage: 40.601225782
2025-07-01T00:06:50.986527Z  INFO quedge::helpers: Found 2 valid cycles
2025-07-01T00:06:50.986702Z  INFO quedge::executor: Executing arbitrage with start amount 50 for cycle path: ["WMAS-Dusa", "WMAS-EagleFi", "USDC-EagleFi", "USDC-Dusa", "WMAS-Dusa"]...
2025-07-01T00:07:06.355120Z  INFO quedge::spawns::swap_fetcher: Found 2 Swap events from the operation O1d5HjAanamTHWSDhjtN2XQV5jtZxL4eEWG6yrFazNQauH54er7. Processing them...
2025-07-01T00:07:06.355281Z  INFO quedge::spawns::swap_fetcher: New detected swap on pool: AS12dny2u8p6DmcPnYurnzqrAG7kMjYUcSD6bDCdJfbYeadYqXaL5
2025-07-01T00:07:06.522210Z  INFO quedge::strategies::bellman_ford_matrix: Updating sparse matrix by pool: Pool { address: "AS12dny2u8p6DmcPnYurnzqrAG7kMjYUcSD6bDCdJfbYeadYqXaL5", a_token: Token { address: "AS12N76WPYB3QNYKGhV2jZuQs1djdhNJLQgnm7m52pHWecvvj1fCQ", name: "USD Coin", symbol: "USDC", decimals: 6, dex: EagleFi }, b_token: Token { address: "AS12FW5Rs5YN2zdpEnqwj4iHUUPt9R4Eqjq2qtpJFNKW3mn33RuLU", name: "Wrapped Massa", symbol: "WMAS", decimals: 9, dex: EagleFi }, a_reserve: 3588.036199, b_reserve: 4946.477771648, dex: EagleFi, swap_fee: 0.3, a_price: 1.3786030846139743, b_price: 0.7253719443693339, active_bin_id: 0, bin_step: 0, bins: {} }
2025-07-01T00:07:06.522356Z  WARN quedge::spawns::swap_fetcher: Skipping arbitrage detection after updating the sparse matrix because of an order from the events stream
2025-07-01T00:07:06.522450Z  INFO quedge::spawns::swap_fetcher: Swap event processed successfully
2025-07-01T00:07:06.522539Z DEBUG quedge::spawns::swap_fetcher: Processing last event of the operation O1d5HjAanamTHWSDhjtN2XQV5jtZxL4eEWG6yrFazNQauH54er7...
2025-07-01T00:07:06.522620Z  WARN quedge::spawns::swap_fetcher: Pool AS1VJvdvuU5AZFFKWLD2yxyHsS1CE65imQxQUShqNEf4BqZ2ZHLT not found in the pools hash map
2025-07-01T00:07:06.522693Z  WARN quedge::spawns::swap_fetcher: Pool AS1fewbVp4FMTEJH81e1BNaH9B3SmeE7sNpZQ2QqwP1wGBzLKJGF not found in the pools hash map
2025-07-01T00:07:06.522769Z  WARN quedge::spawns::swap_fetcher: Pool AS1VJvdvuU5AZFFKWLD2yxyHsS1CE65imQxQUShqNEf4BqZ2ZHLT not found in the pools hash map
2025-07-01T00:07:06.522846Z  WARN quedge::spawns::swap_fetcher: Pool AS1XqtvX3rz2RWbnqLfaYVKEjM3VS5pny9yKDdXcmJ5C1vrcLEFd not found in the pools hash map
2025-07-01T00:07:06.522933Z  INFO quedge::spawns::swap_fetcher: New detected swap on pool: AS112Wdy9pM4fvLNLHQXyf7uam9waMPdG5ekr4vxCyQHPkrMMPPY
2025-07-01T00:07:06.536079Z ERROR quedge::spawns::swap_fetcher: Error in detect_and_execute_arb: Failed to parse amount out to U256
2025-07-01T00:07:06.837590Z  INFO quedge::strategies::bellman_ford_matrix: Updating sparse matrix by pool: Pool { address: "AS112Wdy9pM4fvLNLHQXyf7uam9waMPdG5ekr4vxCyQHPkrMMPPY", a_token: Token { address: "AS12FW5Rs5YN2zdpEnqwj4iHUUPt9R4Eqjq2qtpJFNKW3mn33RuLU", name: "Wrapped Massa", symbol: "WMAS", decimals: 9, dex: Dusa }, b_token: Token { address: "AS12N76WPYB3QNYKGhV2jZuQs1djdhNJLQgnm7m52pHWecvvj1fCQ", name: "USD Coin", symbol: "USDC", decimals: 6, dex: Dusa }, a_reserve: 11974.832785826, b_reserve: 5520.686311, dex: Dusa, swap_fee: 0.0, a_price: 0.5185686795695997, b_price: 1.9283848782189803, active_bin_id: 8384822, bin_step: 20, bins: {8384821: Bin { id: 8384821, a_reserve: 0.0, b_reserve: 255.572155 }, 8384822: Bin { id: 8384822, a_reserve: 122.980933994, b_reserve: 192.309238 }, 8384823: Bin { id: 8384823, a_reserve: 493.827160493, b_reserve: 0.0 }} }
2025-07-01T00:07:06.837771Z  INFO quedge::spawns::swap_fetcher: Swap event processed successfully
2025-07-01T00:07:06.838017Z DEBUG quedge::helpers: Detecting arbitrage cycles...
2025-07-01T00:07:06.838228Z DEBUG quedge::helpers: Arbitrage cycles found (4 total):
2025-07-01T00:07:06.838359Z DEBUG quedge::helpers: Cycle path Inisde Simulation Task:
2025-07-01T00:07:06.838486Z DEBUG quedge::helpers::display: Path: ["USDC-EagleFi", "USDC-Dusa", "WMAS-Dusa", "WMAS-EagleFi", "USDC-EagleFi"]
2025-07-01T00:07:06.838608Z DEBUG quedge::helpers: Cycle path Inisde Simulation Task:
2025-07-01T00:07:06.838702Z DEBUG quedge::helpers::display: Path: ["WMAS-EagleFi", "USDC-EagleFi", "USDC-Dusa", "WMAS-Dusa", "WMAS-EagleFi"]
2025-07-01T00:07:06.838806Z DEBUG quedge::helpers: Cycle path Inisde Simulation Task:
2025-07-01T00:07:06.838880Z DEBUG quedge::helpers::display: Path: ["USDC-Dusa", "WMAS-Dusa", "WMAS-EagleFi", "USDC-EagleFi", "USDC-Dusa"]
2025-07-01T00:07:06.838985Z DEBUG quedge::helpers: Cycle path Inisde Simulation Task:
2025-07-01T00:07:06.839063Z  INFO quedge::helpers: Default start amount for token AS12FW5Rs5YN2zdpEnqwj4iHUUPt9R4Eqjq2qtpJFNKW3mn33RuLU is 10
2025-07-01T00:07:06.839142Z DEBUG quedge::helpers::display: Path: ["WMAS-Dusa", "WMAS-EagleFi", "USDC-EagleFi", "USDC-Dusa", "WMAS-Dusa"]
2025-07-01T00:07:06.839221Z  INFO quedge::helpers: Offchain Amount out after Offchain simulation: 13.917946324115643
2025-07-01T00:07:06.839303Z  INFO quedge::helpers: Default start amount for token AS12FW5Rs5YN2zdpEnqwj4iHUUPt9R4Eqjq2qtpJFNKW3mn33RuLU is 10
2025-07-01T00:07:06.839377Z  INFO quedge::helpers: Offchain Earning percentage: 39.179463241156434
2025-07-01T00:07:06.839449Z  INFO quedge::helpers: Offchain Amount out after Offchain simulation: 13.917946324115643
2025-07-01T00:07:06.839564Z  INFO quedge::helpers: Offchain Earning percentage: 39.179463241156434
2025-07-01T00:07:07.000573Z  INFO quedge::helpers: Amount out after Onchain simulation: 68.896204895
2025-07-01T00:07:07.000705Z  INFO quedge::helpers: Earning percentage: 37.792409789999994
2025-07-01T00:07:07.004283Z  INFO quedge::helpers: Amount out after Onchain simulation: 68.896204895
2025-07-01T00:07:07.004451Z  INFO quedge::helpers: Earning percentage: 37.792409789999994
2025-07-01T00:07:07.004678Z  INFO quedge::helpers: Found 2 valid cycles
2025-07-01T00:07:07.004902Z  INFO quedge::executor: Executing arbitrage with start amount 50 for cycle path: ["WMAS-Dusa", "WMAS-EagleFi", "USDC-EagleFi", "USDC-Dusa", "WMAS-Dusa"]...
2025-07-01T00:07:22.399370Z  INFO quedge::spawns::swap_fetcher: Found 2 Swap events from the operation O121KYYtd3LQqrsT8rQUyXTS2FpJaB3uxcJpwmVXzT8xK2yHAfBQ. Processing them...
2025-07-01T00:07:22.399495Z  INFO quedge::spawns::swap_fetcher: New detected swap on pool: AS12dny2u8p6DmcPnYurnzqrAG7kMjYUcSD6bDCdJfbYeadYqXaL5
2025-07-01T00:07:23.176772Z ERROR quedge::spawns::swap_fetcher: Error in detect_and_execute_arb: Failed to parse amount out to U256
2025-07-01T00:07:24.177410Z  INFO quedge::strategies::bellman_ford_matrix: Updating sparse matrix by pool: Pool { address: "AS12dny2u8p6DmcPnYurnzqrAG7kMjYUcSD6bDCdJfbYeadYqXaL5", a_token: Token { address: "AS12N76WPYB3QNYKGhV2jZuQs1djdhNJLQgnm7m52pHWecvvj1fCQ", name: "USD Coin", symbol: "USDC", decimals: 6, dex: EagleFi }, b_token: Token { address: "AS12FW5Rs5YN2zdpEnqwj4iHUUPt9R4Eqjq2qtpJFNKW3mn33RuLU", name: "Wrapped Massa", symbol: "WMAS", decimals: 9, dex: EagleFi }, a_reserve: 3552.237186, b_reserve: 4996.440271648, dex: EagleFi, swap_fee: 0.3, a_price: 1.4065615582596405, b_price: 0.7109535975356208, active_bin_id: 0, bin_step: 0, bins: {} }
2025-07-01T00:07:24.177573Z  WARN quedge::spawns::swap_fetcher: Skipping arbitrage detection after updating the sparse matrix because of an order from the events stream
2025-07-01T00:07:24.177679Z  INFO quedge::spawns::swap_fetcher: Swap event processed successfully
2025-07-01T00:07:24.177763Z DEBUG quedge::spawns::swap_fetcher: Processing last event of the operation O121KYYtd3LQqrsT8rQUyXTS2FpJaB3uxcJpwmVXzT8xK2yHAfBQ...
2025-07-01T00:07:24.177841Z  WARN quedge::spawns::swap_fetcher: Pool AS1VJvdvuU5AZFFKWLD2yxyHsS1CE65imQxQUShqNEf4BqZ2ZHLT not found in the pools hash map
2025-07-01T00:07:24.177914Z  WARN quedge::spawns::swap_fetcher: Pool AS1fewbVp4FMTEJH81e1BNaH9B3SmeE7sNpZQ2QqwP1wGBzLKJGF not found in the pools hash map
2025-07-01T00:07:24.177987Z  WARN quedge::spawns::swap_fetcher: Pool AS1VJvdvuU5AZFFKWLD2yxyHsS1CE65imQxQUShqNEf4BqZ2ZHLT not found in the pools hash map
2025-07-01T00:07:24.178085Z  WARN quedge::spawns::swap_fetcher: Pool AS1XqtvX3rz2RWbnqLfaYVKEjM3VS5pny9yKDdXcmJ5C1vrcLEFd not found in the pools hash map
2025-07-01T00:07:24.178173Z  INFO quedge::spawns::swap_fetcher: New detected swap on pool: AS112Wdy9pM4fvLNLHQXyf7uam9waMPdG5ekr4vxCyQHPkrMMPPY
2025-07-01T00:07:25.469881Z  INFO quedge::strategies::bellman_ford_matrix: Updating sparse matrix by pool: Pool { address: "AS112Wdy9pM4fvLNLHQXyf7uam9waMPdG5ekr4vxCyQHPkrMMPPY", a_token: Token { address: "AS12FW5Rs5YN2zdpEnqwj4iHUUPt9R4Eqjq2qtpJFNKW3mn33RuLU", name: "Wrapped Massa", symbol: "WMAS", decimals: 9, dex: Dusa }, b_token: Token { address: "AS12N76WPYB3QNYKGhV2jZuQs1djdhNJLQgnm7m52pHWecvvj1fCQ", name: "USD Coin", symbol: "USDC", decimals: 6, dex: Dusa }, a_reserve: 11905.936580931, b_reserve: 5556.413725, dex: Dusa, swap_fee: 0.0, a_price: 0.5185686795695997, b_price: 1.9283848782189803, active_bin_id: 8384822, bin_step: 20, bins: {8384821: Bin { id: 8384821, a_reserve: 0.0, b_reserve: 255.572155 }, 8384822: Bin { id: 8384822, a_reserve: 54.084729099, b_reserve: 228.036652 }, 8384823: Bin { id: 8384823, a_reserve: 493.827160493, b_reserve: 0.0 }} }
2025-07-01T00:07:25.470064Z  INFO quedge::spawns::swap_fetcher: Swap event processed successfully
2025-07-01T00:07:25.470435Z DEBUG quedge::helpers: Detecting arbitrage cycles...
2025-07-01T00:07:25.470587Z DEBUG quedge::helpers: Arbitrage cycles found (4 total):
2025-07-01T00:07:25.470693Z DEBUG quedge::helpers: Cycle path Inisde Simulation Task:
2025-07-01T00:07:25.470783Z DEBUG quedge::helpers::display: Path: ["USDC-EagleFi", "USDC-Dusa", "WMAS-Dusa", "WMAS-EagleFi", "USDC-EagleFi"]
2025-07-01T00:07:25.470890Z DEBUG quedge::helpers: Cycle path Inisde Simulation Task:
2025-07-01T00:07:25.470966Z DEBUG quedge::helpers::display: Path: ["WMAS-EagleFi", "USDC-EagleFi", "USDC-Dusa", "WMAS-Dusa", "WMAS-EagleFi"]
2025-07-01T00:07:25.471080Z DEBUG quedge::helpers: Cycle path Inisde Simulation Task:
2025-07-01T00:07:25.471158Z DEBUG quedge::helpers::display: Path: ["USDC-Dusa", "WMAS-Dusa", "WMAS-EagleFi", "USDC-EagleFi", "USDC-Dusa"]
2025-07-01T00:07:25.471251Z DEBUG quedge::helpers: Cycle path Inisde Simulation Task:
2025-07-01T00:07:25.471336Z  INFO quedge::helpers: Default start amount for token AS12FW5Rs5YN2zdpEnqwj4iHUUPt9R4Eqjq2qtpJFNKW3mn33RuLU is 10
2025-07-01T00:07:25.471408Z DEBUG quedge::helpers::display: Path: ["WMAS-Dusa", "WMAS-EagleFi", "USDC-EagleFi", "USDC-Dusa", "WMAS-Dusa"]
2025-07-01T00:07:25.471494Z  INFO quedge::helpers: Offchain Amount out after Offchain simulation: 13.641571228363238
2025-07-01T00:07:25.471571Z  INFO quedge::helpers: Default start amount for token AS12FW5Rs5YN2zdpEnqwj4iHUUPt9R4Eqjq2qtpJFNKW3mn33RuLU is 10
2025-07-01T00:07:25.471641Z  INFO quedge::helpers: Offchain Earning percentage: 36.41571228363239
2025-07-01T00:07:25.471706Z  INFO quedge::helpers: Offchain Amount out after Offchain simulation: 13.641571228363238
2025-07-01T00:07:25.471775Z  INFO quedge::helpers: Offchain Earning percentage: 36.41571228363239
2025-07-01T00:07:25.936968Z  INFO quedge::helpers: Amount out after Onchain simulation: 67.506526493
2025-07-01T00:07:25.937100Z  INFO quedge::helpers: Earning percentage: 35.01305298599999
2025-07-01T00:07:25.958625Z  INFO quedge::helpers: Amount out after Onchain simulation: 67.506526493
2025-07-01T00:07:25.958750Z  INFO quedge::helpers: Earning percentage: 35.01305298599999
2025-07-01T00:07:25.958888Z  INFO quedge::helpers: Found 2 valid cycles
2025-07-01T00:07:25.959034Z  INFO quedge::executor: Executing arbitrage with start amount 50 for cycle path: ["WMAS-Dusa", "WMAS-EagleFi", "USDC-EagleFi", "USDC-Dusa", "WMAS-Dusa"]...
2025-07-01T00:07:38.405431Z  INFO quedge::spawns::swap_fetcher: Found 3 Swap events from the operation O13YhKhQVmvzgJLPtvM9g7Kydd94oU9Yrr7irNFPjfrHB6sAZWa. Processing them...
2025-07-01T00:07:38.405591Z  INFO quedge::spawns::swap_fetcher: New detected swap on pool: AS12dny2u8p6DmcPnYurnzqrAG7kMjYUcSD6bDCdJfbYeadYqXaL5
2025-07-01T00:07:38.524589Z ERROR quedge::spawns::swap_fetcher: Error in detect_and_execute_arb: Failed to parse amount out to U256
2025-07-01T00:07:38.557268Z  INFO quedge::strategies::bellman_ford_matrix: Updating sparse matrix by pool: Pool { address: "AS12dny2u8p6DmcPnYurnzqrAG7kMjYUcSD6bDCdJfbYeadYqXaL5", a_token: Token { address: "AS12N76WPYB3QNYKGhV2jZuQs1djdhNJLQgnm7m52pHWecvvj1fCQ", name: "USD Coin", symbol: "USDC", decimals: 6, dex: EagleFi }, b_token: Token { address: "AS12FW5Rs5YN2zdpEnqwj4iHUUPt9R4Eqjq2qtpJFNKW3mn33RuLU", name: "Wrapped Massa", symbol: "WMAS", decimals: 9, dex: EagleFi }, a_reserve: 3517.146256, b_reserve: 5046.402771648, dex: EagleFi, swap_fee: 0.3, a_price: 1.4348003763105381, b_price: 0.6969610661598872, active_bin_id: 0, bin_step: 0, bins: {} }
2025-07-01T00:07:38.557420Z  WARN quedge::spawns::swap_fetcher: Skipping arbitrage detection after updating the sparse matrix because of an order from the events stream
2025-07-01T00:07:38.557503Z  INFO quedge::spawns::swap_fetcher: Swap event processed successfully
2025-07-01T00:07:38.557592Z  INFO quedge::spawns::swap_fetcher: New detected swap on pool: AS112Wdy9pM4fvLNLHQXyf7uam9waMPdG5ekr4vxCyQHPkrMMPPY
2025-07-01T00:07:38.860937Z  INFO quedge::strategies::bellman_ford_matrix: Updating sparse matrix by pool: Pool { address: "AS112Wdy9pM4fvLNLHQXyf7uam9waMPdG5ekr4vxCyQHPkrMMPPY", a_token: Token { address: "AS12FW5Rs5YN2zdpEnqwj4iHUUPt9R4Eqjq2qtpJFNKW3mn33RuLU", name: "Wrapped Massa", symbol: "WMAS", decimals: 9, dex: Dusa }, b_token: Token { address: "AS12N76WPYB3QNYKGhV2jZuQs1djdhNJLQgnm7m52pHWecvvj1fCQ", name: "USD Coin", symbol: "USDC", decimals: 6, dex: Dusa }, a_reserve: 11838.430054438, b_reserve: 5591.434416, dex: Dusa, swap_fee: 0.0, a_price: 0.519605816928739, b_price: 1.9245358066057685, active_bin_id: 8384823, bin_step: 20, bins: {8384822: Bin { id: 8384822, a_reserve: 0.0, b_reserve: 256.083299 }, 8384823: Bin { id: 8384823, a_reserve: 480.405363099, b_reserve: 6.974044 }, 8384824: Bin { id: 8384824, a_reserve: 493.827160493, b_reserve: 0.0 }} }
2025-07-01T00:07:38.861082Z  WARN quedge::spawns::swap_fetcher: Skipping arbitrage detection after updating the sparse matrix because of an order from the events stream
2025-07-01T00:07:38.861179Z  INFO quedge::spawns::swap_fetcher: Swap event processed successfully
2025-07-01T00:07:38.861275Z DEBUG quedge::spawns::swap_fetcher: Processing last event of the operation O13YhKhQVmvzgJLPtvM9g7Kydd94oU9Yrr7irNFPjfrHB6sAZWa...
2025-07-01T00:07:38.861364Z  WARN quedge::spawns::swap_fetcher: Pool AS1VJvdvuU5AZFFKWLD2yxyHsS1CE65imQxQUShqNEf4BqZ2ZHLT not found in the pools hash map
2025-07-01T00:07:38.861450Z  WARN quedge::spawns::swap_fetcher: Pool AS1fewbVp4FMTEJH81e1BNaH9B3SmeE7sNpZQ2QqwP1wGBzLKJGF not found in the pools hash map
2025-07-01T00:07:38.861539Z  WARN quedge::spawns::swap_fetcher: Pool AS1VJvdvuU5AZFFKWLD2yxyHsS1CE65imQxQUShqNEf4BqZ2ZHLT not found in the pools hash map
2025-07-01T00:07:38.861654Z  WARN quedge::spawns::swap_fetcher: Pool AS1XqtvX3rz2RWbnqLfaYVKEjM3VS5pny9yKDdXcmJ5C1vrcLEFd not found in the pools hash map
2025-07-01T00:07:38.861836Z  INFO quedge::spawns::swap_fetcher: New detected swap on pool: AS112Wdy9pM4fvLNLHQXyf7uam9waMPdG5ekr4vxCyQHPkrMMPPY
2025-07-01T00:07:39.159721Z  INFO quedge::strategies::bellman_ford_matrix: Updating sparse matrix by pool: Pool { address: "AS112Wdy9pM4fvLNLHQXyf7uam9waMPdG5ekr4vxCyQHPkrMMPPY", a_token: Token { address: "AS12FW5Rs5YN2zdpEnqwj4iHUUPt9R4Eqjq2qtpJFNKW3mn33RuLU", name: "Wrapped Massa", symbol: "WMAS", decimals: 9, dex: Dusa }, b_token: Token { address: "AS12N76WPYB3QNYKGhV2jZuQs1djdhNJLQgnm7m52pHWecvvj1fCQ", name: "USD Coin", symbol: "USDC", decimals: 6, dex: Dusa }, a_reserve: 11838.430054438, b_reserve: 5591.434416, dex: Dusa, swap_fee: 0.0, a_price: 0.519605816928739, b_price: 1.9245358066057685, active_bin_id: 8384823, bin_step: 20, bins: {8384822: Bin { id: 8384822, a_reserve: 0.0, b_reserve: 256.083299 }, 8384823: Bin { id: 8384823, a_reserve: 480.405363099, b_reserve: 6.974044 }, 8384824: Bin { id: 8384824, a_reserve: 493.827160493, b_reserve: 0.0 }} }
2025-07-01T00:07:39.159906Z  INFO quedge::spawns::swap_fetcher: Swap event processed successfully
2025-07-01T00:07:39.160148Z DEBUG quedge::helpers: Detecting arbitrage cycles...
2025-07-01T00:07:39.160312Z DEBUG quedge::helpers: Arbitrage cycles found (4 total):
2025-07-01T00:07:39.160417Z DEBUG quedge::helpers: Cycle path Inisde Simulation Task:
2025-07-01T00:07:39.160500Z DEBUG quedge::helpers::display: Path: ["USDC-EagleFi", "USDC-Dusa", "WMAS-Dusa", "WMAS-EagleFi", "USDC-EagleFi"]
2025-07-01T00:07:39.160609Z DEBUG quedge::helpers: Cycle path Inisde Simulation Task:
2025-07-01T00:07:39.160690Z DEBUG quedge::helpers::display: Path: ["WMAS-EagleFi", "USDC-EagleFi", "USDC-Dusa", "WMAS-Dusa", "WMAS-EagleFi"]
2025-07-01T00:07:39.160800Z DEBUG quedge::helpers: Cycle path Inisde Simulation Task:
2025-07-01T00:07:39.160878Z DEBUG quedge::helpers::display: Path: ["USDC-Dusa", "WMAS-Dusa", "WMAS-EagleFi", "USDC-EagleFi", "USDC-Dusa"]
2025-07-01T00:07:39.160979Z DEBUG quedge::helpers: Cycle path Inisde Simulation Task:
2025-07-01T00:07:39.161057Z  INFO quedge::helpers: Default start amount for token AS12FW5Rs5YN2zdpEnqwj4iHUUPt9R4Eqjq2qtpJFNKW3mn33RuLU is 10
2025-07-01T00:07:39.161134Z DEBUG quedge::helpers::display: Path: ["WMAS-Dusa", "WMAS-EagleFi", "USDC-EagleFi", "USDC-Dusa", "WMAS-Dusa"]
2025-07-01T00:07:39.161222Z  INFO quedge::helpers: Offchain Amount out after Offchain simulation: 13.346656961016869
2025-07-01T00:07:39.161315Z  INFO quedge::helpers: Default start amount for token AS12FW5Rs5YN2zdpEnqwj4iHUUPt9R4Eqjq2qtpJFNKW3mn33RuLU is 10
2025-07-01T00:07:39.161389Z  INFO quedge::helpers: Offchain Earning percentage: 33.46656961016869
2025-07-01T00:07:39.161453Z  INFO quedge::helpers: Offchain Amount out after Offchain simulation: 13.346656961016869
2025-07-01T00:07:39.161558Z  INFO quedge::helpers: Offchain Earning percentage: 33.46656961016869
2025-07-01T00:07:39.318292Z  INFO quedge::helpers: Amount out after Onchain simulation: 66.078648239
2025-07-01T00:07:39.318415Z  INFO quedge::helpers: Earning percentage: 32.157296478000006
2025-07-01T00:07:39.323430Z  INFO quedge::helpers: Amount out after Onchain simulation: 66.078648239
2025-07-01T00:07:39.323529Z  INFO quedge::helpers: Earning percentage: 32.157296478000006
2025-07-01T00:07:39.323665Z  INFO quedge::helpers: Found 2 valid cycles
2025-07-01T00:07:39.323827Z  INFO quedge::executor: Executing arbitrage with start amount 50 for cycle path: ["WMAS-Dusa", "WMAS-EagleFi", "USDC-EagleFi", "USDC-Dusa", "WMAS-Dusa"]...
2025-07-01T00:07:54.360554Z  INFO quedge::spawns::swap_fetcher: Found 2 Swap events from the operation O1xuq372SA6TTDAHMVXEVSHG9CbzjjXXGvX32hKTvG7JYujikVt. Processing them...
2025-07-01T00:07:54.360671Z  INFO quedge::spawns::swap_fetcher: New detected swap on pool: AS12dny2u8p6DmcPnYurnzqrAG7kMjYUcSD6bDCdJfbYeadYqXaL5
2025-07-01T00:07:54.510789Z  INFO quedge::strategies::bellman_ford_matrix: Updating sparse matrix by pool: Pool { address: "AS12dny2u8p6DmcPnYurnzqrAG7kMjYUcSD6bDCdJfbYeadYqXaL5", a_token: Token { address: "AS12N76WPYB3QNYKGhV2jZuQs1djdhNJLQgnm7m52pHWecvvj1fCQ", name: "USD Coin", symbol: "USDC", decimals: 6, dex: EagleFi }, b_token: Token { address: "AS12FW5Rs5YN2zdpEnqwj4iHUUPt9R4Eqjq2qtpJFNKW3mn33RuLU", name: "Wrapped Massa", symbol: "WMAS", decimals: 9, dex: EagleFi }, a_reserve: 3482.742598, b_reserve: 5096.365271648, dex: EagleFi, swap_fee: 0.3, a_price: 1.4633195328803912, b_price: 0.6833777432271437, active_bin_id: 0, bin_step: 0, bins: {} }
2025-07-01T00:07:54.510948Z  WARN quedge::spawns::swap_fetcher: Skipping arbitrage detection after updating the sparse matrix because of an order from the events stream
2025-07-01T00:07:54.511068Z  INFO quedge::spawns::swap_fetcher: Swap event processed successfully
2025-07-01T00:07:54.511188Z DEBUG quedge::spawns::swap_fetcher: Processing last event of the operation O1xuq372SA6TTDAHMVXEVSHG9CbzjjXXGvX32hKTvG7JYujikVt...
2025-07-01T00:07:54.511297Z  WARN quedge::spawns::swap_fetcher: Pool AS1VJvdvuU5AZFFKWLD2yxyHsS1CE65imQxQUShqNEf4BqZ2ZHLT not found in the pools hash map
2025-07-01T00:07:54.511411Z  WARN quedge::spawns::swap_fetcher: Pool AS1fewbVp4FMTEJH81e1BNaH9B3SmeE7sNpZQ2QqwP1wGBzLKJGF not found in the pools hash map
2025-07-01T00:07:54.511506Z  WARN quedge::spawns::swap_fetcher: Pool AS1VJvdvuU5AZFFKWLD2yxyHsS1CE65imQxQUShqNEf4BqZ2ZHLT not found in the pools hash map
2025-07-01T00:07:54.511602Z  WARN quedge::spawns::swap_fetcher: Pool AS1XqtvX3rz2RWbnqLfaYVKEjM3VS5pny9yKDdXcmJ5C1vrcLEFd not found in the pools hash map
2025-07-01T00:07:54.511697Z  INFO quedge::spawns::swap_fetcher: New detected swap on pool: AS112Wdy9pM4fvLNLHQXyf7uam9waMPdG5ekr4vxCyQHPkrMMPPY
2025-07-01T00:07:54.524982Z ERROR quedge::spawns::swap_fetcher: Error in detect_and_execute_arb: Failed to parse amount out to U256
2025-07-01T00:07:54.812793Z  INFO quedge::strategies::bellman_ford_matrix: Updating sparse matrix by pool: Pool { address: "AS112Wdy9pM4fvLNLHQXyf7uam9waMPdG5ekr4vxCyQHPkrMMPPY", a_token: Token { address: "AS12FW5Rs5YN2zdpEnqwj4iHUUPt9R4Eqjq2qtpJFNKW3mn33RuLU", name: "Wrapped Massa", symbol: "WMAS", decimals: 9, dex: Dusa }, b_token: Token { address: "AS12N76WPYB3QNYKGhV2jZuQs1djdhNJLQgnm7m52pHWecvvj1fCQ", name: "USD Coin", symbol: "USDC", decimals: 6, dex: Dusa }, a_reserve: 11772.351406199, b_reserve: 5625.769266, dex: Dusa, swap_fee: 0.0, a_price: 0.519605816928739, b_price: 1.9245358066057685, active_bin_id: 8384823, bin_step: 20, bins: {8384822: Bin { id: 8384822, a_reserve: 0.0, b_reserve: 256.083299 }, 8384823: Bin { id: 8384823, a_reserve: 414.32671486, b_reserve: 41.308894 }, 8384824: Bin { id: 8384824, a_reserve: 493.827160493, b_reserve: 0.0 }} }
2025-07-01T00:07:54.812969Z  INFO quedge::spawns::swap_fetcher: Swap event processed successfully
2025-07-01T00:07:54.813193Z DEBUG quedge::helpers: Detecting arbitrage cycles...
2025-07-01T00:07:54.813341Z DEBUG quedge::helpers: Arbitrage cycles found (4 total):
2025-07-01T00:07:54.813445Z DEBUG quedge::helpers: Cycle path Inisde Simulation Task:
2025-07-01T00:07:54.813543Z DEBUG quedge::helpers::display: Path: ["USDC-EagleFi", "USDC-Dusa", "WMAS-Dusa", "WMAS-EagleFi", "USDC-EagleFi"]
2025-07-01T00:07:54.813647Z DEBUG quedge::helpers: Cycle path Inisde Simulation Task:
2025-07-01T00:07:54.813731Z DEBUG quedge::helpers::display: Path: ["WMAS-EagleFi", "USDC-EagleFi", "USDC-Dusa", "WMAS-Dusa", "WMAS-EagleFi"]
2025-07-01T00:07:54.813839Z DEBUG quedge::helpers: Cycle path Inisde Simulation Task:
2025-07-01T00:07:54.813928Z DEBUG quedge::helpers::display: Path: ["USDC-Dusa", "WMAS-Dusa", "WMAS-EagleFi", "USDC-EagleFi", "USDC-Dusa"]
2025-07-01T00:07:54.814093Z DEBUG quedge::helpers: Cycle path Inisde Simulation Task:
2025-07-01T00:07:54.814190Z  INFO quedge::helpers: Default start amount for token AS12FW5Rs5YN2zdpEnqwj4iHUUPt9R4Eqjq2qtpJFNKW3mn33RuLU is 10
2025-07-01T00:07:54.814284Z DEBUG quedge::helpers::display: Path: ["WMAS-Dusa", "WMAS-EagleFi", "USDC-EagleFi", "USDC-Dusa", "WMAS-Dusa"]
2025-07-01T00:07:54.814402Z  INFO quedge::helpers: Offchain Amount out after Offchain simulation: 13.086792172932212
2025-07-01T00:07:54.814494Z  INFO quedge::helpers: Default start amount for token AS12FW5Rs5YN2zdpEnqwj4iHUUPt9R4Eqjq2qtpJFNKW3mn33RuLU is 10
2025-07-01T00:07:54.814595Z  INFO quedge::helpers: Offchain Earning percentage: 30.867921729322116
2025-07-01T00:07:54.814714Z  INFO quedge::helpers: Offchain Amount out after Offchain simulation: 13.086792172932212
2025-07-01T00:07:54.814827Z  INFO quedge::helpers: Offchain Earning percentage: 30.867921729322116
2025-07-01T00:07:54.978224Z  INFO quedge::helpers: Amount out after Onchain simulation: 64.797032487
2025-07-01T00:07:54.978307Z  INFO quedge::helpers: Amount out after Onchain simulation: 64.797032487
2025-07-01T00:07:54.978416Z  INFO quedge::helpers: Earning percentage: 29.59406497399999
2025-07-01T00:07:54.978515Z  INFO quedge::helpers: Earning percentage: 29.59406497399999
2025-07-01T00:07:54.978682Z  INFO quedge::helpers: Found 2 valid cycles
2025-07-01T00:07:54.978889Z  INFO quedge::executor: Executing arbitrage with start amount 50 for cycle path: ["WMAS-Dusa", "WMAS-EagleFi", "USDC-EagleFi", "USDC-Dusa", "WMAS-Dusa"]...
2025-07-01T00:08:10.355096Z  INFO quedge::spawns::swap_fetcher: Found 2 Swap events from the operation O16c83mgVWTazwND6T8WxteDHSfjMwa5gBuGKohJFNvBJggXkU3. Processing them...
2025-07-01T00:08:10.355227Z  INFO quedge::spawns::swap_fetcher: New detected swap on pool: AS12dny2u8p6DmcPnYurnzqrAG7kMjYUcSD6bDCdJfbYeadYqXaL5
2025-07-01T00:08:10.404096Z ERROR quedge::spawns::swap_fetcher: Error in detect_and_execute_arb: Failed to parse amount out to U256
2025-07-01T00:08:10.522907Z  INFO quedge::strategies::bellman_ford_matrix: Updating sparse matrix by pool: Pool { address: "AS12dny2u8p6DmcPnYurnzqrAG7kMjYUcSD6bDCdJfbYeadYqXaL5", a_token: Token { address: "AS12N76WPYB3QNYKGhV2jZuQs1djdhNJLQgnm7m52pHWecvvj1fCQ", name: "USD Coin", symbol: "USDC", decimals: 6, dex: EagleFi }, b_token: Token { address: "AS12FW5Rs5YN2zdpEnqwj4iHUUPt9R4Eqjq2qtpJFNKW3mn33RuLU", name: "Wrapped Massa", symbol: "WMAS", decimals: 9, dex: EagleFi }, a_reserve: 3449.00621, b_reserve: 5146.327771648, dex: EagleFi, swap_fee: 0.3, a_price: 1.4921190216262323, b_price: 0.6701878238306478, active_bin_id: 0, bin_step: 0, bins: {} }
2025-07-01T00:08:10.523048Z  WARN quedge::spawns::swap_fetcher: Skipping arbitrage detection after updating the sparse matrix because of an order from the events stream
2025-07-01T00:08:10.523140Z  INFO quedge::spawns::swap_fetcher: Swap event processed successfully
2025-07-01T00:08:10.523234Z DEBUG quedge::spawns::swap_fetcher: Processing last event of the operation O16c83mgVWTazwND6T8WxteDHSfjMwa5gBuGKohJFNvBJggXkU3...
2025-07-01T00:08:10.523330Z  WARN quedge::spawns::swap_fetcher: Pool AS1VJvdvuU5AZFFKWLD2yxyHsS1CE65imQxQUShqNEf4BqZ2ZHLT not found in the pools hash map
2025-07-01T00:08:10.523415Z  WARN quedge::spawns::swap_fetcher: Pool AS1fewbVp4FMTEJH81e1BNaH9B3SmeE7sNpZQ2QqwP1wGBzLKJGF not found in the pools hash map
2025-07-01T00:08:10.523508Z  WARN quedge::spawns::swap_fetcher: Pool AS1VJvdvuU5AZFFKWLD2yxyHsS1CE65imQxQUShqNEf4BqZ2ZHLT not found in the pools hash map
2025-07-01T00:08:10.523620Z  WARN quedge::spawns::swap_fetcher: Pool AS1XqtvX3rz2RWbnqLfaYVKEjM3VS5pny9yKDdXcmJ5C1vrcLEFd not found in the pools hash map
2025-07-01T00:08:10.523723Z  INFO quedge::spawns::swap_fetcher: New detected swap on pool: AS112Wdy9pM4fvLNLHQXyf7uam9waMPdG5ekr4vxCyQHPkrMMPPY
2025-07-01T00:08:10.823339Z  INFO quedge::strategies::bellman_ford_matrix: Updating sparse matrix by pool: Pool { address: "AS112Wdy9pM4fvLNLHQXyf7uam9waMPdG5ekr4vxCyQHPkrMMPPY", a_token: Token { address: "AS12FW5Rs5YN2zdpEnqwj4iHUUPt9R4Eqjq2qtpJFNKW3mn33RuLU", name: "Wrapped Massa", symbol: "WMAS", decimals: 9, dex: Dusa }, b_token: Token { address: "AS12N76WPYB3QNYKGhV2jZuQs1djdhNJLQgnm7m52pHWecvvj1fCQ", name: "USD Coin", symbol: "USDC", decimals: 6, dex: Dusa }, a_reserve: 11707.554373712, b_reserve: 5659.438181, dex: Dusa, swap_fee: 0.0, a_price: 0.519605816928739, b_price: 1.9245358066057685, active_bin_id: 8384823, bin_step: 20, bins: {8384822: Bin { id: 8384822, a_reserve: 0.0, b_reserve: 256.083299 }, 8384823: Bin { id: 8384823, a_reserve: 349.529682373, b_reserve: 74.977809 }, 8384824: Bin { id: 8384824, a_reserve: 493.827160493, b_reserve: 0.0 }} }
2025-07-01T00:08:10.823521Z  INFO quedge::spawns::swap_fetcher: Swap event processed successfully
2025-07-01T00:08:10.823746Z DEBUG quedge::helpers: Detecting arbitrage cycles...
2025-07-01T00:08:10.823965Z DEBUG quedge::helpers: Arbitrage cycles found (4 total):
2025-07-01T00:08:10.824108Z DEBUG quedge::helpers: Cycle path Inisde Simulation Task:
2025-07-01T00:08:10.824221Z DEBUG quedge::helpers::display: Path: ["USDC-EagleFi", "USDC-Dusa", "WMAS-Dusa", "WMAS-EagleFi", "USDC-EagleFi"]
2025-07-01T00:08:10.824347Z DEBUG quedge::helpers: Cycle path Inisde Simulation Task:
2025-07-01T00:08:10.824447Z DEBUG quedge::helpers::display: Path: ["WMAS-EagleFi", "USDC-EagleFi", "USDC-Dusa", "WMAS-Dusa", "WMAS-EagleFi"]
2025-07-01T00:08:10.824582Z DEBUG quedge::helpers: Cycle path Inisde Simulation Task:
2025-07-01T00:08:10.824664Z  INFO quedge::helpers: Default start amount for token AS12FW5Rs5YN2zdpEnqwj4iHUUPt9R4Eqjq2qtpJFNKW3mn33RuLU is 10
2025-07-01T00:08:10.824742Z DEBUG quedge::helpers::display: Path: ["USDC-Dusa", "WMAS-Dusa", "WMAS-EagleFi", "USDC-EagleFi", "USDC-Dusa"]
2025-07-01T00:08:10.824819Z  INFO quedge::helpers: Offchain Amount out after Offchain simulation: 12.834446406283643
2025-07-01T00:08:10.824893Z DEBUG quedge::helpers: Cycle path Inisde Simulation Task:
2025-07-01T00:08:10.824967Z  INFO quedge::helpers: Offchain Earning percentage: 28.344464062836426
2025-07-01T00:08:10.825038Z DEBUG quedge::helpers::display: Path: ["WMAS-Dusa", "WMAS-EagleFi", "USDC-EagleFi", "USDC-Dusa", "WMAS-Dusa"]
2025-07-01T00:08:10.825177Z  INFO quedge::helpers: Default start amount for token AS12FW5Rs5YN2zdpEnqwj4iHUUPt9R4Eqjq2qtpJFNKW3mn33RuLU is 10
2025-07-01T00:08:10.825276Z  INFO quedge::helpers: Offchain Amount out after Offchain simulation: 12.834446406283643
2025-07-01T00:08:10.825386Z  INFO quedge::helpers: Offchain Earning percentage: 28.344464062836426
2025-07-01T00:08:10.985183Z  INFO quedge::helpers: Amount out after Onchain simulation: 63.552358199
2025-07-01T00:08:10.985301Z  INFO quedge::helpers: Earning percentage: 27.104716397999994
2025-07-01T00:08:10.990542Z  INFO quedge::helpers: Amount out after Onchain simulation: 63.552358199
2025-07-01T00:08:10.990704Z  INFO quedge::helpers: Earning percentage: 27.104716397999994
2025-07-01T00:08:10.990901Z  INFO quedge::helpers: Found 2 valid cycles
2025-07-01T00:08:10.991100Z  INFO quedge::executor: Executing arbitrage with start amount 50 for cycle path: ["WMAS-Dusa", "WMAS-EagleFi", "USDC-EagleFi", "USDC-Dusa", "WMAS-Dusa"]...
2025-07-01T00:08:26.373543Z  INFO quedge::spawns::swap_fetcher: Found 2 Swap events from the operation O1LJsAyZ4vAQ4Z39jhBAtHTaUTo7y83aqLjx7AU3NnHWJKRkKDn. Processing them...
2025-07-01T00:08:26.373688Z  INFO quedge::spawns::swap_fetcher: New detected swap on pool: AS12dny2u8p6DmcPnYurnzqrAG7kMjYUcSD6bDCdJfbYeadYqXaL5
2025-07-01T00:08:26.519480Z  INFO quedge::strategies::bellman_ford_matrix: Updating sparse matrix by pool: Pool { address: "AS12dny2u8p6DmcPnYurnzqrAG7kMjYUcSD6bDCdJfbYeadYqXaL5", a_token: Token { address: "AS12N76WPYB3QNYKGhV2jZuQs1djdhNJLQgnm7m52pHWecvvj1fCQ", name: "USD Coin", symbol: "USDC", decimals: 6, dex: EagleFi }, b_token: Token { address: "AS12FW5Rs5YN2zdpEnqwj4iHUUPt9R4Eqjq2qtpJFNKW3mn33RuLU", name: "Wrapped Massa", symbol: "WMAS", decimals: 9, dex: EagleFi }, a_reserve: 3415.917858, b_reserve: 5196.290271648, dex: EagleFi, swap_fee: 0.3, a_price: 1.5211988366401756, b_price: 0.6573762587201741, active_bin_id: 0, bin_step: 0, bins: {} }
2025-07-01T00:08:26.519596Z  WARN quedge::spawns::swap_fetcher: Skipping arbitrage detection after updating the sparse matrix because of an order from the events stream
2025-07-01T00:08:26.519675Z  INFO quedge::spawns::swap_fetcher: Swap event processed successfully
2025-07-01T00:08:26.519752Z DEBUG quedge::spawns::swap_fetcher: Processing last event of the operation O1LJsAyZ4vAQ4Z39jhBAtHTaUTo7y83aqLjx7AU3NnHWJKRkKDn...
2025-07-01T00:08:26.519823Z  WARN quedge::spawns::swap_fetcher: Pool AS1VJvdvuU5AZFFKWLD2yxyHsS1CE65imQxQUShqNEf4BqZ2ZHLT not found in the pools hash map
2025-07-01T00:08:26.519888Z  WARN quedge::spawns::swap_fetcher: Pool AS1fewbVp4FMTEJH81e1BNaH9B3SmeE7sNpZQ2QqwP1wGBzLKJGF not found in the pools hash map
2025-07-01T00:08:26.519953Z  WARN quedge::spawns::swap_fetcher: Pool AS1VJvdvuU5AZFFKWLD2yxyHsS1CE65imQxQUShqNEf4BqZ2ZHLT not found in the pools hash map
2025-07-01T00:08:26.520019Z  WARN quedge::spawns::swap_fetcher: Pool AS1XqtvX3rz2RWbnqLfaYVKEjM3VS5pny9yKDdXcmJ5C1vrcLEFd not found in the pools hash map
2025-07-01T00:08:26.520097Z  INFO quedge::spawns::swap_fetcher: New detected swap on pool: AS112Wdy9pM4fvLNLHQXyf7uam9waMPdG5ekr4vxCyQHPkrMMPPY
2025-07-01T00:08:26.529160Z ERROR quedge::spawns::swap_fetcher: Error in detect_and_execute_arb: Failed to parse amount out to U256
2025-07-01T00:08:26.822064Z  INFO quedge::strategies::bellman_ford_matrix: Updating sparse matrix by pool: Pool { address: "AS112Wdy9pM4fvLNLHQXyf7uam9waMPdG5ekr4vxCyQHPkrMMPPY", a_token: Token { address: "AS12FW5Rs5YN2zdpEnqwj4iHUUPt9R4Eqjq2qtpJFNKW3mn33RuLU", name: "Wrapped Massa", symbol: "WMAS", decimals: 9, dex: Dusa }, b_token: Token { address: "AS12N76WPYB3QNYKGhV2jZuQs1djdhNJLQgnm7m52pHWecvvj1fCQ", name: "USD Coin", symbol: "USDC", decimals: 6, dex: Dusa }, a_reserve: 11644.002015513, b_reserve: 5692.460356, dex: Dusa, swap_fee: 0.0, a_price: 0.519605816928739, b_price: 1.9245358066057685, active_bin_id: 8384823, bin_step: 20, bins: {8384822: Bin { id: 8384822, a_reserve: 0.0, b_reserve: 256.083299 }, 8384823: Bin { id: 8384823, a_reserve: 285.977324174, b_reserve: 107.999984 }, 8384824: Bin { id: 8384824, a_reserve: 493.827160493, b_reserve: 0.0 }} }
2025-07-01T00:08:26.822259Z  INFO quedge::spawns::swap_fetcher: Swap event processed successfully
2025-07-01T00:08:26.822466Z DEBUG quedge::helpers: Detecting arbitrage cycles...
2025-07-01T00:08:26.822616Z DEBUG quedge::helpers: Arbitrage cycles found (4 total):
2025-07-01T00:08:26.822730Z DEBUG quedge::helpers: Cycle path Inisde Simulation Task:
2025-07-01T00:08:26.822873Z DEBUG quedge::helpers::display: Path: ["USDC-EagleFi", "USDC-Dusa", "WMAS-Dusa", "WMAS-EagleFi", "USDC-EagleFi"]
2025-07-01T00:08:26.823017Z DEBUG quedge::helpers: Cycle path Inisde Simulation Task:
2025-07-01T00:08:26.823128Z DEBUG quedge::helpers::display: Path: ["WMAS-EagleFi", "USDC-EagleFi", "USDC-Dusa", "WMAS-Dusa", "WMAS-EagleFi"]
2025-07-01T00:08:26.823284Z DEBUG quedge::helpers: Cycle path Inisde Simulation Task:
2025-07-01T00:08:26.823371Z DEBUG quedge::helpers::display: Path: ["USDC-Dusa", "WMAS-Dusa", "WMAS-EagleFi", "USDC-EagleFi", "USDC-Dusa"]
2025-07-01T00:08:26.823503Z DEBUG quedge::helpers: Cycle path Inisde Simulation Task:
2025-07-01T00:08:26.823579Z  INFO quedge::helpers: Default start amount for token AS12FW5Rs5YN2zdpEnqwj4iHUUPt9R4Eqjq2qtpJFNKW3mn33RuLU is 10
2025-07-01T00:08:26.823667Z DEBUG quedge::helpers::display: Path: ["WMAS-Dusa", "WMAS-EagleFi", "USDC-EagleFi", "USDC-Dusa", "WMAS-Dusa"]
2025-07-01T00:08:26.823750Z  INFO quedge::helpers: Offchain Amount out after Offchain simulation: 12.589332302781553
2025-07-01T00:08:26.823840Z  INFO quedge::helpers: Default start amount for token AS12FW5Rs5YN2zdpEnqwj4iHUUPt9R4Eqjq2qtpJFNKW3mn33RuLU is 10
2025-07-01T00:08:26.823917Z  INFO quedge::helpers: Offchain Earning percentage: 25.893323027815534
2025-07-01T00:08:26.823987Z  INFO quedge::helpers: Offchain Amount out after Offchain simulation: 12.589332302781553
2025-07-01T00:08:26.824069Z  INFO quedge::helpers: Offchain Earning percentage: 25.893323027815534
2025-07-01T00:08:26.987315Z  INFO quedge::helpers: Amount out after Onchain simulation: 62.343216616
2025-07-01T00:08:26.987404Z  INFO quedge::helpers: Amount out after Onchain simulation: 62.343216616
2025-07-01T00:08:26.987517Z  INFO quedge::helpers: Earning percentage: 24.686433232
2025-07-01T00:08:26.987574Z  INFO quedge::helpers: Earning percentage: 24.686433232
2025-07-01T00:08:26.987696Z  INFO quedge::helpers: Found 2 valid cycles
2025-07-01T00:08:26.987963Z  INFO quedge::executor: Executing arbitrage with start amount 50 for cycle path: ["WMAS-Dusa", "WMAS-EagleFi", "USDC-EagleFi", "USDC-Dusa", "WMAS-Dusa"]...
2025-07-01T00:09:54.270201Z  INFO quedge: Logger initialized Successfully
2025-07-01T00:09:55.050128Z  INFO quedge: Allowed start tokens: ["AS12FW5Rs5YN2zdpEnqwj4iHUUPt9R4Eqjq2qtpJFNKW3mn33RuLU"]
2025-07-01T00:09:55.050379Z  INFO quedge: Exporting the sparse matrix to a png file...
2025-07-01T00:09:55.191543Z  INFO quedge: Exported the sparse matrix to a png file successfully!
2025-07-01T00:09:55.191906Z  INFO quedge::spawns::swap_fetcher: Swap event fetcher working in candidate state
2025-07-01T00:09:55.192004Z DEBUG quedge::helpers: Detecting arbitrage cycles...
2025-07-01T00:09:55.192238Z DEBUG quedge::helpers: Arbitrage cycles found (3 total):
2025-07-01T00:09:55.192420Z DEBUG quedge::helpers: Cycle path Inisde Simulation Task:
2025-07-01T00:09:55.192542Z DEBUG quedge::helpers::display: Path: ["WMAS-Dusa", "WMAS-EagleFi", "USDC-EagleFi", "USDC-Dusa", "WMAS-Dusa"]
2025-07-01T00:09:55.192668Z DEBUG quedge::helpers: Cycle path Inisde Simulation Task:
2025-07-01T00:09:55.192767Z DEBUG quedge::helpers::display: Path: ["USDC-Dusa", "WMAS-Dusa", "WMAS-EagleFi", "USDC-EagleFi", "USDC-Dusa"]
2025-07-01T00:09:55.192891Z DEBUG quedge::helpers: Cycle path Inisde Simulation Task:
2025-07-01T00:09:55.192982Z  INFO quedge::helpers: Default start amount for token AS12FW5Rs5YN2zdpEnqwj4iHUUPt9R4Eqjq2qtpJFNKW3mn33RuLU is 10
2025-07-01T00:09:55.193056Z DEBUG quedge::helpers::display: Path: ["USDC-EagleFi", "USDC-Dusa", "WMAS-Dusa", "WMAS-EagleFi", "USDC-EagleFi"]
2025-07-01T00:09:55.193128Z  INFO quedge::helpers: Offchain Amount out after Offchain simulation: 12.351176109394691
2025-07-01T00:09:55.193214Z  INFO quedge::helpers: Offchain Earning percentage: 23.511761093946912
2025-07-01T00:09:55.353870Z  INFO quedge::spawns::swap_fetcher: Start waitiing for an arbitrage opportunity...
2025-07-01T00:09:55.354811Z DEBUG quedge::spawns::balance_notifier: Balance of AU12Yd4kCcsizeeTEK9AZyBnuJNZ1cpp99XfCZgzS77ZKnwTFMpVE is 104421.822238641
2025-07-01T00:09:55.366681Z  INFO quedge::helpers: Amount out after Onchain simulation: 61.168270185
2025-07-01T00:09:55.366801Z  INFO quedge::helpers: Earning percentage: 22.336540369999994
2025-07-01T00:09:55.366946Z  INFO quedge::helpers: Found 1 valid cycles
2025-07-01T00:09:55.367130Z  INFO quedge::executor: Executing arbitrage with start amount 50 for cycle path: ["WMAS-Dusa", "WMAS-EagleFi", "USDC-EagleFi", "USDC-Dusa", "WMAS-Dusa"]...
2025-07-01T00:10:02.368421Z  INFO quedge::spawns::swap_fetcher: Found 2 Swap events from the operation O1FiPdRPZd8YBWrfVZW16Uid6WEEfKvxbeejB42hgmpRE5kApyZ. Processing them...
2025-07-01T00:10:02.368558Z  INFO quedge::spawns::swap_fetcher: New detected swap on pool: AS12dny2u8p6DmcPnYurnzqrAG7kMjYUcSD6bDCdJfbYeadYqXaL5
2025-07-01T00:10:02.513710Z  INFO quedge::strategies::bellman_ford_matrix: Updating sparse matrix by pool: Pool { address: "AS12dny2u8p6DmcPnYurnzqrAG7kMjYUcSD6bDCdJfbYeadYqXaL5", a_token: Token { address: "AS12N76WPYB3QNYKGhV2jZuQs1djdhNJLQgnm7m52pHWecvvj1fCQ", name: "USD Coin", symbol: "USDC", decimals: 6, dex: EagleFi }, b_token: Token { address: "AS12FW5Rs5YN2zdpEnqwj4iHUUPt9R4Eqjq2qtpJFNKW3mn33RuLU", name: "Wrapped Massa", symbol: "WMAS", decimals: 9, dex: EagleFi }, a_reserve: 3351.611958, b_reserve: 5296.215271648, dex: EagleFi, swap_fee: 0.3, a_price: 1.5801994198661349, b_price: 0.6328315195082872, active_bin_id: 0, bin_step: 0, bins: {} }
2025-07-01T00:10:02.513912Z  WARN quedge::spawns::swap_fetcher: Skipping arbitrage detection after updating the sparse matrix because of an order from the events stream
2025-07-01T00:10:02.514008Z  INFO quedge::spawns::swap_fetcher: Swap event processed successfully
2025-07-01T00:10:02.514167Z DEBUG quedge::spawns::swap_fetcher: Processing last event of the operation O1FiPdRPZd8YBWrfVZW16Uid6WEEfKvxbeejB42hgmpRE5kApyZ...
2025-07-01T00:10:02.514313Z  WARN quedge::spawns::swap_fetcher: Pool AS1VJvdvuU5AZFFKWLD2yxyHsS1CE65imQxQUShqNEf4BqZ2ZHLT not found in the pools hash map
2025-07-01T00:10:02.514434Z  WARN quedge::spawns::swap_fetcher: Pool AS1fewbVp4FMTEJH81e1BNaH9B3SmeE7sNpZQ2QqwP1wGBzLKJGF not found in the pools hash map
2025-07-01T00:10:02.514515Z  WARN quedge::spawns::swap_fetcher: Pool AS1VJvdvuU5AZFFKWLD2yxyHsS1CE65imQxQUShqNEf4BqZ2ZHLT not found in the pools hash map
2025-07-01T00:10:02.514627Z  WARN quedge::spawns::swap_fetcher: Pool AS1XqtvX3rz2RWbnqLfaYVKEjM3VS5pny9yKDdXcmJ5C1vrcLEFd not found in the pools hash map
2025-07-01T00:10:02.514742Z  INFO quedge::spawns::swap_fetcher: New detected swap on pool: AS112Wdy9pM4fvLNLHQXyf7uam9waMPdG5ekr4vxCyQHPkrMMPPY
2025-07-01T00:10:02.547915Z DEBUG quedge::executor: Showing all operation events data for operation id: O1FiPdRPZd8YBWrfVZW16Uid6WEEfKvxbeejB42hgmpRE5kApyZ ...
2025-07-01T00:10:02.548053Z DEBUG quedge::executor: Event Data: "ArbRoutes inside execuwithFlmashLoan: Router Address: AS12JfpytS54Pq2Pi5ZP9dZ8kxe21TmwBpoiQuMNvAgB3bSVsCs9k\nPool Address: AS12dny2u8p6DmcPnYurnzqrAG7kMjYUcSD6bDCdJfbYeadYqXaL5\nToken In Address: AS12FW5Rs5YN2zdpEnqwj4iHUUPt9R4Eqjq2qtpJFNKW3mn33RuLU\nToken Out Address: AS12N76WPYB3QNYKGhV2jZuQs1djdhNJLQgnm7m52pHWecvvj1fCQ\nAmount In: 50000000000\nAmount Out Min: 1\nBin Step: 0\nTo: AS1VJvdvuU5AZFFKWLD2yxyHsS1CE65imQxQUShqNEf4BqZ2ZHLT\nDeadline: 1751329195366\nIs Dusa: false\nCoins To Use: 10000000\nSwap Params: \n,Router Address: AS1XqtvX3rz2RWbnqLfaYVKEjM3VS5pny9yKDdXcmJ5C1vrcLEFd\nPool Address: AS112Wdy9pM4fvLNLHQXyf7uam9waMPdG5ekr4vxCyQHPkrMMPPY\nToken In Address: AS12N76WPYB3QNYKGhV2jZuQs1djdhNJLQgnm7m52pHWecvvj1fCQ\nToken Out Address: AS12FW5Rs5YN2zdpEnqwj4iHUUPt9R4Eqjq2qtpJFNKW3mn33RuLU\nAmount In: 50000000000\nAmount Out Min: 1\nBin Step: 20\nTo: AS1VJvdvuU5AZFFKWLD2yxyHsS1CE65imQxQUShqNEf4BqZ2ZHLT\nDeadline: 1751329195366\nIs Dusa: true\nCoins To Use: 1000000\nSwap Params: \n"
2025-07-01T00:10:02.548155Z DEBUG quedge::executor: Event Data: "TRANSFER SUCCESS"
2025-07-01T00:10:02.548253Z DEBUG quedge::executor: Event Data: "TRANSFER SUCCESS"
2025-07-01T00:10:02.548340Z DEBUG quedge::executor: Event Data: "TRANSFER SUCCESS"
2025-07-01T00:10:02.548446Z DEBUG quedge::executor: Event Data: "TRANSFER SUCCESS"
2025-07-01T00:10:02.548544Z DEBUG quedge::executor: Event Data: "UPDATE_CUMULATIVE_PRICES::AS12dny2u8p6DmcPnYurnzqrAG7kMjYUcSD6bDCdJfbYeadYqXaL5,AS12JfpytS54Pq2Pi5ZP9dZ8kxe21TmwBpoiQuMNvAgB3bSVsCs9k,11571586564857341546000,3549370782883430000,1751328596500"
2025-07-01T00:10:02.548691Z DEBUG quedge::executor: Event Data: "SWAP:AS12dny2u8p6DmcPnYurnzqrAG7kMjYUcSD6bDCdJfbYeadYqXaL5,AS1VJvdvuU5AZFFKWLD2yxyHsS1CE65imQxQUShqNEf4BqZ2ZHLT,50000000000,AS12FW5Rs5YN2zdpEnqwj4iHUUPt9R4Eqjq2qtpJFNKW3mn33RuLU,31847084,AS12N76WPYB3QNYKGhV2jZuQs1djdhNJLQgnm7m52pHWecvvj1fCQ,150000000,37500000,112500000,5296215271648,3351611958"
2025-07-01T00:10:02.548913Z DEBUG quedge::executor: Event Data: "Spent 0 coins"
2025-07-01T00:10:02.549063Z DEBUG quedge::executor: Event Data: "Swap Route Exexcuted: Pool Address: AS12dny2u8p6DmcPnYurnzqrAG7kMjYUcSD6bDCdJfbYeadYqXaL5\nToken In Address: AS12FW5Rs5YN2zdpEnqwj4iHUUPt9R4Eqjq2qtpJFNKW3mn33RuLU\nToken Out Address: AS12N76WPYB3QNYKGhV2jZuQs1djdhNJLQgnm7m52pHWecvvj1fCQ\nReceiver Address: AS1VJvdvuU5AZFFKWLD2yxyHsS1CE65imQxQUShqNEf4BqZ2ZHLT\nAmount In: 50000000000\nMin Amount Out: 1 \nIs Transfer From: true"
2025-07-01T00:10:02.549166Z DEBUG quedge::executor: Event Data: "Spent 0 coins"
2025-07-01T00:10:02.549246Z DEBUG quedge::executor: Event Data: "TRANSFER SUCCESS"
2025-07-01T00:10:02.549328Z DEBUG quedge::executor: Event Data: "SWAP:AS1VJvdvuU5AZFFKWLD2yxyHsS1CE65imQxQUShqNEf4BqZ2ZHLT;?!8384823;?!false;?!利Ǥ\0\0\0\0\0\0\0\0\0\0\0\0\0\0;?!뭩㷩\u{e}\0\0\0\0\0\0\0\0\0\0\0\0\0;?!0;?!\u{f8cf}\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0"
2025-07-01T00:10:02.549401Z DEBUG quedge::executor: Event Data: "TRANSFER SUCCESS"
2025-07-01T00:10:02.549467Z DEBUG quedge::executor: Event Data: "Received coins"
2025-07-01T00:10:02.549538Z DEBUG quedge::executor: Event Data: "TRANSFER SUCCESS"
2025-07-01T00:10:02.549605Z DEBUG quedge::executor: Event Data: "TRANSFER SUCCESS"
2025-07-01T00:10:02.549677Z DEBUG quedge::executor: Event Data: "FLASH_ARB_EXEC:61168270185"
2025-07-01T00:10:02.549747Z DEBUG quedge::executor: Event Data: "TRANSFER SUCCESS"
2025-07-01T00:10:02.549817Z DEBUG quedge::executor: Event Data: "TRANSFER SUCCESS"
2025-07-01T00:10:02.549885Z DEBUG quedge::executor: Event Data: "UPDATE_CUMULATIVE_PRICES::AS1fewbVp4FMTEJH81e1BNaH9B3SmeE7sNpZQ2QqwP1wGBzLKJGF,AS1VJvdvuU5AZFFKWLD2yxyHsS1CE65imQxQUShqNEf4BqZ2ZHLT,675353043714775519281000,15011259794684720418268890424952000,1751328596500"
2025-07-01T00:10:02.549952Z DEBUG quedge::executor: Event Data: "Spent 0 coins"
2025-07-01T00:10:02.550021Z DEBUG quedge::executor: Event Data: "FLASH_LOAN:AS1fewbVp4FMTEJH81e1BNaH9B3SmeE7sNpZQ2QqwP1wGBzLKJGF,AS1VJvdvuU5AZFFKWLD2yxyHsS1CE65imQxQUShqNEf4BqZ2ZHLT,AS1VJvdvuU5AZFFKWLD2yxyHsS1CE65imQxQUShqNEf4BqZ2ZHLT,0,50000000000,12817214505974134383413567,674627865226602,1000"
2025-07-01T00:10:02.550162Z ERROR quedge: Error in detect_and_execute_arb at the start: Failed to parse amount out to U256
2025-07-01T00:10:02.830133Z  INFO quedge::strategies::bellman_ford_matrix: Updating sparse matrix by pool: Pool { address: "AS112Wdy9pM4fvLNLHQXyf7uam9waMPdG5ekr4vxCyQHPkrMMPPY", a_token: Token { address: "AS12FW5Rs5YN2zdpEnqwj4iHUUPt9R4Eqjq2qtpJFNKW3mn33RuLU", name: "Wrapped Massa", symbol: "WMAS", decimals: 9, dex: Dusa }, b_token: Token { address: "AS12N76WPYB3QNYKGhV2jZuQs1djdhNJLQgnm7m52pHWecvvj1fCQ", name: "USD Coin", symbol: "USDC", decimals: 6, dex: Dusa }, a_reserve: 11520.490528712, b_reserve: 5756.637643, dex: Dusa, swap_fee: 0.0, a_price: 0.519605816928739, b_price: 1.9245358066057685, active_bin_id: 8384823, bin_step: 20, bins: {8384822: Bin { id: 8384822, a_reserve: 0.0, b_reserve: 256.083299 }, 8384823: Bin { id: 8384823, a_reserve: 162.465837373, b_reserve: 172.177271 }, 8384824: Bin { id: 8384824, a_reserve: 493.827160493, b_reserve: 0.0 }} }
2025-07-01T00:10:02.830310Z  INFO quedge::spawns::swap_fetcher: Swap event processed successfully
2025-07-01T00:10:02.830511Z DEBUG quedge::helpers: Detecting arbitrage cycles...
2025-07-01T00:10:02.830701Z DEBUG quedge::helpers: Arbitrage cycles found (3 total):
2025-07-01T00:10:02.830822Z DEBUG quedge::helpers: Cycle path Inisde Simulation Task:
2025-07-01T00:10:02.830944Z DEBUG quedge::helpers::display: Path: ["WMAS-Dusa", "WMAS-EagleFi", "USDC-EagleFi", "USDC-Dusa", "WMAS-Dusa"]
2025-07-01T00:10:02.831115Z DEBUG quedge::helpers: Cycle path Inisde Simulation Task:
2025-07-01T00:10:02.831201Z  INFO quedge::helpers: Default start amount for token AS12FW5Rs5YN2zdpEnqwj4iHUUPt9R4Eqjq2qtpJFNKW3mn33RuLU is 10
2025-07-01T00:10:02.831289Z DEBUG quedge::helpers::display: Path: ["USDC-Dusa", "WMAS-Dusa", "WMAS-EagleFi", "USDC-EagleFi", "USDC-Dusa"]
2025-07-01T00:10:02.831377Z  INFO quedge::helpers: Offchain Amount out after Offchain simulation: 12.11971690041023
2025-07-01T00:10:02.831483Z DEBUG quedge::helpers: Cycle path Inisde Simulation Task:
2025-07-01T00:10:02.831568Z  INFO quedge::helpers: Offchain Earning percentage: 21.197169004102303
2025-07-01T00:10:02.831647Z DEBUG quedge::helpers::display: Path: ["USDC-EagleFi", "USDC-Dusa", "WMAS-Dusa", "WMAS-EagleFi", "USDC-EagleFi"]
2025-07-01T00:10:03.000039Z  INFO quedge::helpers: Amount out after Onchain simulation: 60.026242939
2025-07-01T00:10:03.000160Z  INFO quedge::helpers: Earning percentage: 20.052485878
2025-07-01T00:10:03.000336Z  INFO quedge::helpers: Found 1 valid cycles
2025-07-01T00:10:03.000519Z  INFO quedge::executor: Executing arbitrage with start amount 50 for cycle path: ["WMAS-Dusa", "WMAS-EagleFi", "USDC-EagleFi", "USDC-Dusa", "WMAS-Dusa"]...
2025-07-01T00:10:18.377448Z  INFO quedge::spawns::swap_fetcher: Found 2 Swap events from the operation O1yR6MSBc7TruvHznr8vAT9iYyvBMGEDgpNizjJbo4ne55uRvkb. Processing them...
2025-07-01T00:10:18.377594Z  INFO quedge::spawns::swap_fetcher: New detected swap on pool: AS12dny2u8p6DmcPnYurnzqrAG7kMjYUcSD6bDCdJfbYeadYqXaL5
2025-07-01T00:10:18.542974Z DEBUG quedge::executor: Showing all operation events data for operation id: O1yR6MSBc7TruvHznr8vAT9iYyvBMGEDgpNizjJbo4ne55uRvkb ...
2025-07-01T00:10:18.543075Z  INFO quedge::strategies::bellman_ford_matrix: Updating sparse matrix by pool: Pool { address: "AS12dny2u8p6DmcPnYurnzqrAG7kMjYUcSD6bDCdJfbYeadYqXaL5", a_token: Token { address: "AS12N76WPYB3QNYKGhV2jZuQs1djdhNJLQgnm7m52pHWecvvj1fCQ", name: "USD Coin", symbol: "USDC", decimals: 6, dex: EagleFi }, b_token: Token { address: "AS12FW5Rs5YN2zdpEnqwj4iHUUPt9R4Eqjq2qtpJFNKW3mn33RuLU", name: "Wrapped Massa", symbol: "WMAS", decimals: 9, dex: EagleFi }, a_reserve: 3320.359468, b_reserve: 5346.177771648, dex: EagleFi, swap_fee: 0.3, a_price: 1.6101201761953323, b_price: 0.6210716533985502, active_bin_id: 0, bin_step: 0, bins: {} }
2025-07-01T00:10:18.543157Z DEBUG quedge::executor: Event Data: "ArbRoutes inside execuwithFlmashLoan: Router Address: AS12JfpytS54Pq2Pi5ZP9dZ8kxe21TmwBpoiQuMNvAgB3bSVsCs9k\nPool Address: AS12dny2u8p6DmcPnYurnzqrAG7kMjYUcSD6bDCdJfbYeadYqXaL5\nToken In Address: AS12FW5Rs5YN2zdpEnqwj4iHUUPt9R4Eqjq2qtpJFNKW3mn33RuLU\nToken Out Address: AS12N76WPYB3QNYKGhV2jZuQs1djdhNJLQgnm7m52pHWecvvj1fCQ\nAmount In: 50000000000\nAmount Out Min: 1\nBin Step: 0\nTo: AS1VJvdvuU5AZFFKWLD2yxyHsS1CE65imQxQUShqNEf4BqZ2ZHLT\nDeadline: 1751329203000\nIs Dusa: false\nCoins To Use: 10000000\nSwap Params: \n,Router Address: AS1XqtvX3rz2RWbnqLfaYVKEjM3VS5pny9yKDdXcmJ5C1vrcLEFd\nPool Address: AS112Wdy9pM4fvLNLHQXyf7uam9waMPdG5ekr4vxCyQHPkrMMPPY\nToken In Address: AS12N76WPYB3QNYKGhV2jZuQs1djdhNJLQgnm7m52pHWecvvj1fCQ\nToken Out Address: AS12FW5Rs5YN2zdpEnqwj4iHUUPt9R4Eqjq2qtpJFNKW3mn33RuLU\nAmount In: 50000000000\nAmount Out Min: 1\nBin Step: 20\nTo: AS1VJvdvuU5AZFFKWLD2yxyHsS1CE65imQxQUShqNEf4BqZ2ZHLT\nDeadline: 1751329203000\nIs Dusa: true\nCoins To Use: 1000000\nSwap Params: \n"
2025-07-01T00:10:18.543223Z  WARN quedge::spawns::swap_fetcher: Skipping arbitrage detection after updating the sparse matrix because of an order from the events stream
2025-07-01T00:10:18.543292Z DEBUG quedge::executor: Event Data: "TRANSFER SUCCESS"
2025-07-01T00:10:18.543364Z  INFO quedge::spawns::swap_fetcher: Swap event processed successfully
2025-07-01T00:10:18.543459Z DEBUG quedge::executor: Event Data: "TRANSFER SUCCESS"
2025-07-01T00:10:18.543574Z DEBUG quedge::spawns::swap_fetcher: Processing last event of the operation O1yR6MSBc7TruvHznr8vAT9iYyvBMGEDgpNizjJbo4ne55uRvkb...
2025-07-01T00:10:18.543662Z DEBUG quedge::executor: Event Data: "TRANSFER SUCCESS"
2025-07-01T00:10:18.543738Z  WARN quedge::spawns::swap_fetcher: Pool AS1VJvdvuU5AZFFKWLD2yxyHsS1CE65imQxQUShqNEf4BqZ2ZHLT not found in the pools hash map
2025-07-01T00:10:18.543810Z DEBUG quedge::executor: Event Data: "TRANSFER SUCCESS"
2025-07-01T00:10:18.543890Z  WARN quedge::spawns::swap_fetcher: Pool AS1fewbVp4FMTEJH81e1BNaH9B3SmeE7sNpZQ2QqwP1wGBzLKJGF not found in the pools hash map
2025-07-01T00:10:18.543964Z DEBUG quedge::executor: Event Data: "UPDATE_CUMULATIVE_PRICES::AS12dny2u8p6DmcPnYurnzqrAG7kMjYUcSD6bDCdJfbYeadYqXaL5,AS12JfpytS54Pq2Pi5ZP9dZ8kxe21TmwBpoiQuMNvAgB3bSVsCs9k,11571672103701687914000,3549423908634918000,1751328612500"
2025-07-01T00:10:18.544037Z  WARN quedge::spawns::swap_fetcher: Pool AS1VJvdvuU5AZFFKWLD2yxyHsS1CE65imQxQUShqNEf4BqZ2ZHLT not found in the pools hash map
2025-07-01T00:10:18.544110Z DEBUG quedge::executor: Event Data: "SWAP:AS12dny2u8p6DmcPnYurnzqrAG7kMjYUcSD6bDCdJfbYeadYqXaL5,AS1VJvdvuU5AZFFKWLD2yxyHsS1CE65imQxQUShqNEf4BqZ2ZHLT,50000000000,AS12FW5Rs5YN2zdpEnqwj4iHUUPt9R4Eqjq2qtpJFNKW3mn33RuLU,31252490,AS12N76WPYB3QNYKGhV2jZuQs1djdhNJLQgnm7m52pHWecvvj1fCQ,150000000,37500000,112500000,5346177771648,3320359468"
2025-07-01T00:10:18.544186Z  WARN quedge::spawns::swap_fetcher: Pool AS1XqtvX3rz2RWbnqLfaYVKEjM3VS5pny9yKDdXcmJ5C1vrcLEFd not found in the pools hash map
2025-07-01T00:10:18.544254Z DEBUG quedge::executor: Event Data: "Spent 0 coins"
2025-07-01T00:10:18.544325Z  INFO quedge::spawns::swap_fetcher: New detected swap on pool: AS112Wdy9pM4fvLNLHQXyf7uam9waMPdG5ekr4vxCyQHPkrMMPPY
2025-07-01T00:10:18.544392Z DEBUG quedge::executor: Event Data: "Swap Route Exexcuted: Pool Address: AS12dny2u8p6DmcPnYurnzqrAG7kMjYUcSD6bDCdJfbYeadYqXaL5\nToken In Address: AS12FW5Rs5YN2zdpEnqwj4iHUUPt9R4Eqjq2qtpJFNKW3mn33RuLU\nToken Out Address: AS12N76WPYB3QNYKGhV2jZuQs1djdhNJLQgnm7m52pHWecvvj1fCQ\nReceiver Address: AS1VJvdvuU5AZFFKWLD2yxyHsS1CE65imQxQUShqNEf4BqZ2ZHLT\nAmount In: 50000000000\nMin Amount Out: 1 \nIs Transfer From: true"
2025-07-01T00:10:18.544488Z DEBUG quedge::executor: Event Data: "Spent 0 coins"
2025-07-01T00:10:18.544616Z DEBUG quedge::executor: Event Data: "TRANSFER SUCCESS"
2025-07-01T00:10:18.544718Z DEBUG quedge::executor: Event Data: "SWAP:AS1VJvdvuU5AZFFKWLD2yxyHsS1CE65imQxQUShqNEf4BqZ2ZHLT;?!8384823;?!false;?!\u{ebe1}Ǜ\0\0\0\0\0\0\0\0\0\0\0\0\0\0;?!읻輪\r\0\0\0\0\0\0\0\0\0\0\0\0\0;?!0;?!\u{f429}\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0"
2025-07-01T00:10:18.544822Z DEBUG quedge::executor: Event Data: "TRANSFER SUCCESS"
2025-07-01T00:10:18.544919Z DEBUG quedge::executor: Event Data: "Received coins"
2025-07-01T00:10:18.545019Z DEBUG quedge::executor: Event Data: "TRANSFER SUCCESS"
2025-07-01T00:10:18.545096Z DEBUG quedge::executor: Event Data: "TRANSFER SUCCESS"
2025-07-01T00:10:18.545183Z DEBUG quedge::executor: Event Data: "FLASH_ARB_EXEC:60026242939"
2025-07-01T00:10:18.545259Z DEBUG quedge::executor: Event Data: "TRANSFER SUCCESS"
2025-07-01T00:10:18.545332Z DEBUG quedge::executor: Event Data: "TRANSFER SUCCESS"
2025-07-01T00:10:18.545413Z DEBUG quedge::executor: Event Data: "UPDATE_CUMULATIVE_PRICES::AS1fewbVp4FMTEJH81e1BNaH9B3SmeE7sNpZQ2QqwP1wGBzLKJGF,AS1VJvdvuU5AZFFKWLD2yxyHsS1CE65imQxQUShqNEf4BqZ2ZHLT,675363825087379960993000,15011464794516203141118546447672000,1751328612500"
2025-07-01T00:10:18.545502Z DEBUG quedge::executor: Event Data: "Spent 0 coins"
2025-07-01T00:10:18.545603Z DEBUG quedge::executor: Event Data: "FLASH_LOAN:AS1fewbVp4FMTEJH81e1BNaH9B3SmeE7sNpZQ2QqwP1wGBzLKJGF,AS1VJvdvuU5AZFFKWLD2yxyHsS1CE65imQxQUShqNEf4BqZ2ZHLT,AS1VJvdvuU5AZFFKWLD2yxyHsS1CE65imQxQUShqNEf4BqZ2ZHLT,0,50000000000,12817214505974134383413567,674627865226602,1000"
2025-07-01T00:10:18.545739Z ERROR quedge::spawns::swap_fetcher: Error in detect_and_execute_arb: Failed to parse amount out to U256
2025-07-01T00:10:18.864740Z  INFO quedge::strategies::bellman_ford_matrix: Updating sparse matrix by pool: Pool { address: "AS112Wdy9pM4fvLNLHQXyf7uam9waMPdG5ekr4vxCyQHPkrMMPPY", a_token: Token { address: "AS12FW5Rs5YN2zdpEnqwj4iHUUPt9R4Eqjq2qtpJFNKW3mn33RuLU", name: "Wrapped Massa", symbol: "WMAS", decimals: 9, dex: Dusa }, b_token: Token { address: "AS12N76WPYB3QNYKGhV2jZuQs1djdhNJLQgnm7m52pHWecvvj1fCQ", name: "USD Coin", symbol: "USDC", decimals: 6, dex: Dusa }, a_reserve: 11460.464285773, b_reserve: 5787.827628, dex: Dusa, swap_fee: 0.0, a_price: 0.519605816928739, b_price: 1.9245358066057685, active_bin_id: 8384823, bin_step: 20, bins: {8384822: Bin { id: 8384822, a_reserve: 0.0, b_reserve: 256.083299 }, 8384823: Bin { id: 8384823, a_reserve: 102.439594434, b_reserve: 203.367256 }, 8384824: Bin { id: 8384824, a_reserve: 493.827160493, b_reserve: 0.0 }} }
2025-07-01T00:10:18.864929Z  INFO quedge::spawns::swap_fetcher: Swap event processed successfully
2025-07-01T00:10:18.865142Z DEBUG quedge::helpers: Detecting arbitrage cycles...
2025-07-01T00:10:18.865308Z DEBUG quedge::helpers: Arbitrage cycles found (3 total):
2025-07-01T00:10:18.865438Z DEBUG quedge::helpers: Cycle path Inisde Simulation Task:
2025-07-01T00:10:18.865530Z DEBUG quedge::helpers::display: Path: ["WMAS-Dusa", "WMAS-EagleFi", "USDC-EagleFi", "USDC-Dusa", "WMAS-Dusa"]
2025-07-01T00:10:18.865655Z DEBUG quedge::helpers: Cycle path Inisde Simulation Task:
2025-07-01T00:10:18.865723Z  INFO quedge::helpers: Default start amount for token AS12FW5Rs5YN2zdpEnqwj4iHUUPt9R4Eqjq2qtpJFNKW3mn33RuLU is 10
2025-07-01T00:10:18.865791Z DEBUG quedge::helpers::display: Path: ["USDC-Dusa", "WMAS-Dusa", "WMAS-EagleFi", "USDC-EagleFi", "USDC-Dusa"]
2025-07-01T00:10:18.865869Z  INFO quedge::helpers: Offchain Amount out after Offchain simulation: 11.894705871692796
2025-07-01T00:10:18.865938Z DEBUG quedge::helpers: Cycle path Inisde Simulation Task:
2025-07-01T00:10:18.866024Z  INFO quedge::helpers: Offchain Earning percentage: 18.947058716927963
2025-07-01T00:10:18.866119Z DEBUG quedge::helpers::display: Path: ["USDC-EagleFi", "USDC-Dusa", "WMAS-Dusa", "WMAS-EagleFi", "USDC-EagleFi"]
2025-07-01T00:10:19.024423Z  INFO quedge::helpers: Amount out after Onchain simulation: 35.480659375
2025-07-01T00:10:19.024550Z  INFO quedge::helpers: Earning percentage: 18.26886458333334
2025-07-01T00:10:19.024699Z  INFO quedge::helpers: Found 1 valid cycles
2025-07-01T00:10:19.024864Z  INFO quedge::executor: Executing arbitrage with start amount 30 for cycle path: ["WMAS-Dusa", "WMAS-EagleFi", "USDC-EagleFi", "USDC-Dusa", "WMAS-Dusa"]...
2025-07-01T00:10:34.352564Z  INFO quedge::spawns::swap_fetcher: Found 2 Swap events from the operation O12dv3ds2xcZ5xvkJaAFp7Y4thj3jxvap23e6wTMqQAtBMSHu3o. Processing them...
2025-07-01T00:10:34.352713Z  INFO quedge::spawns::swap_fetcher: New detected swap on pool: AS12dny2u8p6DmcPnYurnzqrAG7kMjYUcSD6bDCdJfbYeadYqXaL5
2025-07-01T00:10:34.404592Z DEBUG quedge::executor: Showing all operation events data for operation id: O12dv3ds2xcZ5xvkJaAFp7Y4thj3jxvap23e6wTMqQAtBMSHu3o ...
2025-07-01T00:10:34.404704Z DEBUG quedge::executor: Event Data: "ArbRoutes inside execuwithFlmashLoan: Router Address: AS12JfpytS54Pq2Pi5ZP9dZ8kxe21TmwBpoiQuMNvAgB3bSVsCs9k\nPool Address: AS12dny2u8p6DmcPnYurnzqrAG7kMjYUcSD6bDCdJfbYeadYqXaL5\nToken In Address: AS12FW5Rs5YN2zdpEnqwj4iHUUPt9R4Eqjq2qtpJFNKW3mn33RuLU\nToken Out Address: AS12N76WPYB3QNYKGhV2jZuQs1djdhNJLQgnm7m52pHWecvvj1fCQ\nAmount In: 30000000000\nAmount Out Min: 1\nBin Step: 0\nTo: AS1VJvdvuU5AZFFKWLD2yxyHsS1CE65imQxQUShqNEf4BqZ2ZHLT\nDeadline: 1751329219024\nIs Dusa: false\nCoins To Use: 10000000\nSwap Params: \n,Router Address: AS1XqtvX3rz2RWbnqLfaYVKEjM3VS5pny9yKDdXcmJ5C1vrcLEFd\nPool Address: AS112Wdy9pM4fvLNLHQXyf7uam9waMPdG5ekr4vxCyQHPkrMMPPY\nToken In Address: AS12N76WPYB3QNYKGhV2jZuQs1djdhNJLQgnm7m52pHWecvvj1fCQ\nToken Out Address: AS12FW5Rs5YN2zdpEnqwj4iHUUPt9R4Eqjq2qtpJFNKW3mn33RuLU\nAmount In: 30000000000\nAmount Out Min: 1\nBin Step: 20\nTo: AS1VJvdvuU5AZFFKWLD2yxyHsS1CE65imQxQUShqNEf4BqZ2ZHLT\nDeadline: 1751329219024\nIs Dusa: true\nCoins To Use: 1000000\nSwap Params: \n"
2025-07-01T00:10:34.404790Z DEBUG quedge::executor: Event Data: "TRANSFER SUCCESS"
2025-07-01T00:10:34.404864Z DEBUG quedge::executor: Event Data: "TRANSFER SUCCESS"
2025-07-01T00:10:34.404946Z DEBUG quedge::executor: Event Data: "TRANSFER SUCCESS"
2025-07-01T00:10:34.405018Z DEBUG quedge::executor: Event Data: "TRANSFER SUCCESS"
2025-07-01T00:10:34.405125Z DEBUG quedge::executor: Event Data: "UPDATE_CUMULATIVE_PRICES::AS12dny2u8p6DmcPnYurnzqrAG7kMjYUcSD6bDCdJfbYeadYqXaL5,AS12JfpytS54Pq2Pi5ZP9dZ8kxe21TmwBpoiQuMNvAgB3bSVsCs9k,11571758122186034282000,3549476738819958000,1751328628500"
2025-07-01T00:10:34.405229Z DEBUG quedge::executor: Event Data: "SWAP:AS12dny2u8p6DmcPnYurnzqrAG7kMjYUcSD6bDCdJfbYeadYqXaL5,AS1VJvdvuU5AZFFKWLD2yxyHsS1CE65imQxQUShqNEf4BqZ2ZHLT,30000000000,AS12FW5Rs5YN2zdpEnqwj4iHUUPt9R4Eqjq2qtpJFNKW3mn33RuLU,18472903,AS12N76WPYB3QNYKGhV2jZuQs1djdhNJLQgnm7m52pHWecvvj1fCQ,90000000,22500000,67500000,5376155271648,3301886565"
2025-07-01T00:10:34.405316Z DEBUG quedge::executor: Event Data: "Spent 0 coins"
2025-07-01T00:10:34.405405Z DEBUG quedge::executor: Event Data: "Swap Route Exexcuted: Pool Address: AS12dny2u8p6DmcPnYurnzqrAG7kMjYUcSD6bDCdJfbYeadYqXaL5\nToken In Address: AS12FW5Rs5YN2zdpEnqwj4iHUUPt9R4Eqjq2qtpJFNKW3mn33RuLU\nToken Out Address: AS12N76WPYB3QNYKGhV2jZuQs1djdhNJLQgnm7m52pHWecvvj1fCQ\nReceiver Address: AS1VJvdvuU5AZFFKWLD2yxyHsS1CE65imQxQUShqNEf4BqZ2ZHLT\nAmount In: 30000000000\nMin Amount Out: 1 \nIs Transfer From: true"
2025-07-01T00:10:34.405489Z DEBUG quedge::executor: Event Data: "Spent 0 coins"
2025-07-01T00:10:34.405576Z DEBUG quedge::executor: Event Data: "TRANSFER SUCCESS"
2025-07-01T00:10:34.405657Z DEBUG quedge::executor: Event Data: "SWAP:AS1VJvdvuU5AZFFKWLD2yxyHsS1CE65imQxQUShqNEf4BqZ2ZHLT;?!8384823;?!false;?!併ę\0\0\0\0\0\0\0\0\0\0\0\0\0\0;?!\u{e5af}䋏\u{8}\0\0\0\0\0\0\0\0\0\0\0\0\0;?!0;?!遒\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0"
2025-07-01T00:10:34.405732Z DEBUG quedge::executor: Event Data: "TRANSFER SUCCESS"
2025-07-01T00:10:34.405805Z DEBUG quedge::executor: Event Data: "Received coins"
2025-07-01T00:10:34.405883Z DEBUG quedge::executor: Event Data: "TRANSFER SUCCESS"
2025-07-01T00:10:34.405952Z DEBUG quedge::executor: Event Data: "TRANSFER SUCCESS"
2025-07-01T00:10:34.406034Z DEBUG quedge::executor: Event Data: "FLASH_ARB_EXEC:35480659375"
2025-07-01T00:10:34.406118Z DEBUG quedge::executor: Event Data: "TRANSFER SUCCESS"
2025-07-01T00:10:34.406199Z DEBUG quedge::executor: Event Data: "TRANSFER SUCCESS"
2025-07-01T00:10:34.406281Z DEBUG quedge::executor: Event Data: "UPDATE_CUMULATIVE_PRICES::AS1fewbVp4FMTEJH81e1BNaH9B3SmeE7sNpZQ2QqwP1wGBzLKJGF,AS1VJvdvuU5AZFFKWLD2yxyHsS1CE65imQxQUShqNEf4BqZ2ZHLT,675374606459984402705000,15011669794347685863968202470392000,1751328628500"
2025-07-01T00:10:34.406355Z DEBUG quedge::executor: Event Data: "Spent 0 coins"
2025-07-01T00:10:34.406429Z DEBUG quedge::executor: Event Data: "FLASH_LOAN:AS1fewbVp4FMTEJH81e1BNaH9B3SmeE7sNpZQ2QqwP1wGBzLKJGF,AS1VJvdvuU5AZFFKWLD2yxyHsS1CE65imQxQUShqNEf4BqZ2ZHLT,AS1VJvdvuU5AZFFKWLD2yxyHsS1CE65imQxQUShqNEf4BqZ2ZHLT,0,30000000000,12817214505974134383413567,674627845226602,1000"
2025-07-01T00:10:34.406538Z ERROR quedge::spawns::swap_fetcher: Error in detect_and_execute_arb: Failed to parse amount out to U256
2025-07-01T00:10:34.501438Z  INFO quedge::strategies::bellman_ford_matrix: Updating sparse matrix by pool: Pool { address: "AS12dny2u8p6DmcPnYurnzqrAG7kMjYUcSD6bDCdJfbYeadYqXaL5", a_token: Token { address: "AS12N76WPYB3QNYKGhV2jZuQs1djdhNJLQgnm7m52pHWecvvj1fCQ", name: "USD Coin", symbol: "USDC", decimals: 6, dex: EagleFi }, b_token: Token { address: "AS12FW5Rs5YN2zdpEnqwj4iHUUPt9R4Eqjq2qtpJFNKW3mn33RuLU", name: "Wrapped Massa", symbol: "WMAS", decimals: 9, dex: EagleFi }, a_reserve: 3301.886565, b_reserve: 5376.155271648, dex: EagleFi, swap_fee: 0.3, a_price: 1.6282071372878917, b_price: 0.6141724705037851, active_bin_id: 0, bin_step: 0, bins: {} }
2025-07-01T00:10:34.501603Z  WARN quedge::spawns::swap_fetcher: Skipping arbitrage detection after updating the sparse matrix because of an order from the events stream
2025-07-01T00:10:34.501719Z  INFO quedge::spawns::swap_fetcher: Swap event processed successfully
2025-07-01T00:10:34.501814Z DEBUG quedge::spawns::swap_fetcher: Processing last event of the operation O12dv3ds2xcZ5xvkJaAFp7Y4thj3jxvap23e6wTMqQAtBMSHu3o...
2025-07-01T00:10:34.501928Z  WARN quedge::spawns::swap_fetcher: Pool AS1VJvdvuU5AZFFKWLD2yxyHsS1CE65imQxQUShqNEf4BqZ2ZHLT not found in the pools hash map
2025-07-01T00:10:34.502045Z  WARN quedge::spawns::swap_fetcher: Pool AS1fewbVp4FMTEJH81e1BNaH9B3SmeE7sNpZQ2QqwP1wGBzLKJGF not found in the pools hash map
2025-07-01T00:10:34.502157Z  WARN quedge::spawns::swap_fetcher: Pool AS1VJvdvuU5AZFFKWLD2yxyHsS1CE65imQxQUShqNEf4BqZ2ZHLT not found in the pools hash map
2025-07-01T00:10:34.502274Z  WARN quedge::spawns::swap_fetcher: Pool AS1XqtvX3rz2RWbnqLfaYVKEjM3VS5pny9yKDdXcmJ5C1vrcLEFd not found in the pools hash map
2025-07-01T00:10:34.502389Z  INFO quedge::spawns::swap_fetcher: New detected swap on pool: AS112Wdy9pM4fvLNLHQXyf7uam9waMPdG5ekr4vxCyQHPkrMMPPY
2025-07-01T00:10:34.812356Z  INFO quedge::strategies::bellman_ford_matrix: Updating sparse matrix by pool: Pool { address: "AS112Wdy9pM4fvLNLHQXyf7uam9waMPdG5ekr4vxCyQHPkrMMPPY", a_token: Token { address: "AS12FW5Rs5YN2zdpEnqwj4iHUUPt9R4Eqjq2qtpJFNKW3mn33RuLU", name: "Wrapped Massa", symbol: "WMAS", decimals: 9, dex: Dusa }, b_token: Token { address: "AS12N76WPYB3QNYKGhV2jZuQs1djdhNJLQgnm7m52pHWecvvj1fCQ", name: "USD Coin", symbol: "USDC", decimals: 6, dex: Dusa }, a_reserve: 11424.983626398, b_reserve: 5806.263585, dex: Dusa, swap_fee: 0.0, a_price: 0.519605816928739, b_price: 1.9245358066057685, active_bin_id: 8384823, bin_step: 20, bins: {8384822: Bin { id: 8384822, a_reserve: 0.0, b_reserve: 256.083299 }, 8384823: Bin { id: 8384823, a_reserve: 66.958935059, b_reserve: 221.803213 }, 8384824: Bin { id: 8384824, a_reserve: 493.827160493, b_reserve: 0.0 }} }
2025-07-01T00:10:34.812531Z  INFO quedge::spawns::swap_fetcher: Swap event processed successfully
2025-07-01T00:10:34.812760Z DEBUG quedge::helpers: Detecting arbitrage cycles...
2025-07-01T00:10:34.812909Z DEBUG quedge::helpers: Arbitrage cycles found (3 total):
2025-07-01T00:10:34.813019Z DEBUG quedge::helpers: Cycle path Inisde Simulation Task:
2025-07-01T00:10:34.813108Z DEBUG quedge::helpers::display: Path: ["WMAS-Dusa", "WMAS-EagleFi", "USDC-EagleFi", "USDC-Dusa", "WMAS-Dusa"]
2025-07-01T00:10:34.813207Z DEBUG quedge::helpers: Cycle path Inisde Simulation Task:
2025-07-01T00:10:34.813307Z DEBUG quedge::helpers::display: Path: ["USDC-Dusa", "WMAS-Dusa", "WMAS-EagleFi", "USDC-EagleFi", "USDC-Dusa"]
2025-07-01T00:10:34.813472Z DEBUG quedge::helpers: Cycle path Inisde Simulation Task:
2025-07-01T00:10:34.813545Z  INFO quedge::helpers: Default start amount for token AS12FW5Rs5YN2zdpEnqwj4iHUUPt9R4Eqjq2qtpJFNKW3mn33RuLU is 10
2025-07-01T00:10:34.813616Z DEBUG quedge::helpers::display: Path: ["USDC-EagleFi", "USDC-Dusa", "WMAS-Dusa", "WMAS-EagleFi", "USDC-EagleFi"]
2025-07-01T00:10:34.813687Z  INFO quedge::helpers: Offchain Amount out after Offchain simulation: 11.762695458034628
2025-07-01T00:10:34.813777Z  INFO quedge::helpers: Offchain Earning percentage: 17.62695458034628
2025-07-01T00:10:34.970956Z  INFO quedge::helpers: Amount out after Onchain simulation: 35.087609503
2025-07-01T00:10:34.971105Z  INFO quedge::helpers: Earning percentage: 16.958698343333342
2025-07-01T00:10:34.971277Z  INFO quedge::helpers: Found 1 valid cycles
2025-07-01T00:10:34.971482Z  INFO quedge::executor: Executing arbitrage with start amount 30 for cycle path: ["WMAS-Dusa", "WMAS-EagleFi", "USDC-EagleFi", "USDC-Dusa", "WMAS-Dusa"]...
2025-07-01T00:10:50.388829Z  INFO quedge::spawns::swap_fetcher: Found 2 Swap events from the operation O1ir4AiogAwUxjJzQNwMPyEXHcq9QyV7XmFUxtMDT2cYEnWXYFk. Processing them...
2025-07-01T00:10:50.388994Z  INFO quedge::spawns::swap_fetcher: New detected swap on pool: AS12dny2u8p6DmcPnYurnzqrAG7kMjYUcSD6bDCdJfbYeadYqXaL5
2025-07-01T00:10:50.538501Z DEBUG quedge::executor: Showing all operation events data for operation id: O1ir4AiogAwUxjJzQNwMPyEXHcq9QyV7XmFUxtMDT2cYEnWXYFk ...
2025-07-01T00:10:50.538664Z DEBUG quedge::executor: Event Data: "ArbRoutes inside execuwithFlmashLoan: Router Address: AS12JfpytS54Pq2Pi5ZP9dZ8kxe21TmwBpoiQuMNvAgB3bSVsCs9k\nPool Address: AS12dny2u8p6DmcPnYurnzqrAG7kMjYUcSD6bDCdJfbYeadYqXaL5\nToken In Address: AS12FW5Rs5YN2zdpEnqwj4iHUUPt9R4Eqjq2qtpJFNKW3mn33RuLU\nToken Out Address: AS12N76WPYB3QNYKGhV2jZuQs1djdhNJLQgnm7m52pHWecvvj1fCQ\nAmount In: 30000000000\nAmount Out Min: 1\nBin Step: 0\nTo: AS1VJvdvuU5AZFFKWLD2yxyHsS1CE65imQxQUShqNEf4BqZ2ZHLT\nDeadline: 1751329234971\nIs Dusa: false\nCoins To Use: 10000000\nSwap Params: \n,Router Address: AS1XqtvX3rz2RWbnqLfaYVKEjM3VS5pny9yKDdXcmJ5C1vrcLEFd\nPool Address: AS112Wdy9pM4fvLNLHQXyf7uam9waMPdG5ekr4vxCyQHPkrMMPPY\nToken In Address: AS12N76WPYB3QNYKGhV2jZuQs1djdhNJLQgnm7m52pHWecvvj1fCQ\nToken Out Address: AS12FW5Rs5YN2zdpEnqwj4iHUUPt9R4Eqjq2qtpJFNKW3mn33RuLU\nAmount In: 30000000000\nAmount Out Min: 1\nBin Step: 20\nTo: AS1VJvdvuU5AZFFKWLD2yxyHsS1CE65imQxQUShqNEf4BqZ2ZHLT\nDeadline: 1751329234971\nIs Dusa: true\nCoins To Use: 1000000\nSwap Params: \n"
2025-07-01T00:10:50.538795Z DEBUG quedge::executor: Event Data: "TRANSFER SUCCESS"
2025-07-01T00:10:50.538926Z DEBUG quedge::executor: Event Data: "TRANSFER SUCCESS"
2025-07-01T00:10:50.539056Z DEBUG quedge::executor: Event Data: "TRANSFER SUCCESS"
2025-07-01T00:10:50.539192Z DEBUG quedge::executor: Event Data: "TRANSFER SUCCESS"
2025-07-01T00:10:50.539319Z DEBUG quedge::executor: Event Data: "UPDATE_CUMULATIVE_PRICES::AS12dny2u8p6DmcPnYurnzqrAG7kMjYUcSD6bDCdJfbYeadYqXaL5,AS12JfpytS54Pq2Pi5ZP9dZ8kxe21TmwBpoiQuMNvAgB3bSVsCs9k,11571844620310380650000,3549529276712790000,1751328644500"
2025-07-01T00:10:50.539421Z DEBUG quedge::executor: Event Data: "SWAP:AS12dny2u8p6DmcPnYurnzqrAG7kMjYUcSD6bDCdJfbYeadYqXaL5,AS1VJvdvuU5AZFFKWLD2yxyHsS1CE65imQxQUShqNEf4BqZ2ZHLT,30000000000,AS12FW5Rs5YN2zdpEnqwj4iHUUPt9R4Eqjq2qtpJFNKW3mn33RuLU,18268263,AS12N76WPYB3QNYKGhV2jZuQs1djdhNJLQgnm7m52pHWecvvj1fCQ,90000000,22500000,67500000,5406132771648,3283618302"
2025-07-01T00:10:50.539554Z DEBUG quedge::executor: Event Data: "Spent 0 coins"
2025-07-01T00:10:50.539667Z DEBUG quedge::executor: Event Data: "Swap Route Exexcuted: Pool Address: AS12dny2u8p6DmcPnYurnzqrAG7kMjYUcSD6bDCdJfbYeadYqXaL5\nToken In Address: AS12FW5Rs5YN2zdpEnqwj4iHUUPt9R4Eqjq2qtpJFNKW3mn33RuLU\nToken Out Address: AS12N76WPYB3QNYKGhV2jZuQs1djdhNJLQgnm7m52pHWecvvj1fCQ\nReceiver Address: AS1VJvdvuU5AZFFKWLD2yxyHsS1CE65imQxQUShqNEf4BqZ2ZHLT\nAmount In: 30000000000\nMin Amount Out: 1 \nIs Transfer From: true"
2025-07-01T00:10:50.539764Z DEBUG quedge::executor: Event Data: "Spent 0 coins"
2025-07-01T00:10:50.539877Z DEBUG quedge::executor: Event Data: "TRANSFER SUCCESS"
2025-07-01T00:10:50.539990Z DEBUG quedge::executor: Event Data: "SWAP:AS1VJvdvuU5AZFFKWLD2yxyHsS1CE65imQxQUShqNEf4BqZ2ZHLT;?!8384823;?!false;?!ㆮĖ\0\0\0\0\0\0\0\0\0\0\0\0\0\0;?!溟⭢\u{8}\0\0\0\0\0\0\0\0\0\0\0\0\0;?!0;?!躹\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0"
2025-07-01T00:10:50.540098Z DEBUG quedge::executor: Event Data: "TRANSFER SUCCESS"
2025-07-01T00:10:50.540206Z DEBUG quedge::executor: Event Data: "Received coins"
2025-07-01T00:10:50.540304Z DEBUG quedge::executor: Event Data: "TRANSFER SUCCESS"
2025-07-01T00:10:50.540404Z DEBUG quedge::executor: Event Data: "TRANSFER SUCCESS"
2025-07-01T00:10:50.540503Z DEBUG quedge::executor: Event Data: "FLASH_ARB_EXEC:35087609503"
2025-07-01T00:10:50.540592Z DEBUG quedge::executor: Event Data: "TRANSFER SUCCESS"
2025-07-01T00:10:50.540686Z DEBUG quedge::executor: Event Data: "TRANSFER SUCCESS"
2025-07-01T00:10:50.540778Z DEBUG quedge::executor: Event Data: "UPDATE_CUMULATIVE_PRICES::AS1fewbVp4FMTEJH81e1BNaH9B3SmeE7sNpZQ2QqwP1wGBzLKJGF,AS1VJvdvuU5AZFFKWLD2yxyHsS1CE65imQxQUShqNEf4BqZ2ZHLT,675385387832588844417000,15011874794179168586817858493112000,1751328644500"
2025-07-01T00:10:50.540875Z DEBUG quedge::executor: Event Data: "Spent 0 coins"
2025-07-01T00:10:50.540992Z DEBUG quedge::executor: Event Data: "FLASH_LOAN:AS1fewbVp4FMTEJH81e1BNaH9B3SmeE7sNpZQ2QqwP1wGBzLKJGF,AS1VJvdvuU5AZFFKWLD2yxyHsS1CE65imQxQUShqNEf4BqZ2ZHLT,AS1VJvdvuU5AZFFKWLD2yxyHsS1CE65imQxQUShqNEf4BqZ2ZHLT,0,30000000000,12817214505974134383413567,674627845226602,1000"
2025-07-01T00:10:50.541137Z ERROR quedge::spawns::swap_fetcher: Error in detect_and_execute_arb: Failed to parse amount out to U256
2025-07-01T00:10:50.548010Z  INFO quedge::strategies::bellman_ford_matrix: Updating sparse matrix by pool: Pool { address: "AS12dny2u8p6DmcPnYurnzqrAG7kMjYUcSD6bDCdJfbYeadYqXaL5", a_token: Token { address: "AS12N76WPYB3QNYKGhV2jZuQs1djdhNJLQgnm7m52pHWecvvj1fCQ", name: "USD Coin", symbol: "USDC", decimals: 6, dex: EagleFi }, b_token: Token { address: "AS12FW5Rs5YN2zdpEnqwj4iHUUPt9R4Eqjq2qtpJFNKW3mn33RuLU", name: "Wrapped Massa", symbol: "WMAS", decimals: 9, dex: EagleFi }, a_reserve: 3283.618302, b_reserve: 5406.132771648, dex: EagleFi, swap_fee: 0.3, a_price: 1.6463950052767127, b_price: 0.6073876541139823, active_bin_id: 0, bin_step: 0, bins: {} }
2025-07-01T00:10:50.548193Z  WARN quedge::spawns::swap_fetcher: Skipping arbitrage detection after updating the sparse matrix because of an order from the events stream
2025-07-01T00:10:50.548321Z  INFO quedge::spawns::swap_fetcher: Swap event processed successfully
2025-07-01T00:10:50.548467Z DEBUG quedge::spawns::swap_fetcher: Processing last event of the operation O1ir4AiogAwUxjJzQNwMPyEXHcq9QyV7XmFUxtMDT2cYEnWXYFk...
2025-07-01T00:10:50.548607Z  WARN quedge::spawns::swap_fetcher: Pool AS1VJvdvuU5AZFFKWLD2yxyHsS1CE65imQxQUShqNEf4BqZ2ZHLT not found in the pools hash map
2025-07-01T00:10:50.548727Z  WARN quedge::spawns::swap_fetcher: Pool AS1fewbVp4FMTEJH81e1BNaH9B3SmeE7sNpZQ2QqwP1wGBzLKJGF not found in the pools hash map
2025-07-01T00:10:50.548835Z  WARN quedge::spawns::swap_fetcher: Pool AS1VJvdvuU5AZFFKWLD2yxyHsS1CE65imQxQUShqNEf4BqZ2ZHLT not found in the pools hash map
2025-07-01T00:10:50.548943Z  WARN quedge::spawns::swap_fetcher: Pool AS1XqtvX3rz2RWbnqLfaYVKEjM3VS5pny9yKDdXcmJ5C1vrcLEFd not found in the pools hash map
2025-07-01T00:10:50.549055Z  INFO quedge::spawns::swap_fetcher: New detected swap on pool: AS112Wdy9pM4fvLNLHQXyf7uam9waMPdG5ekr4vxCyQHPkrMMPPY
2025-07-01T00:10:50.867414Z  INFO quedge::strategies::bellman_ford_matrix: Updating sparse matrix by pool: Pool { address: "AS112Wdy9pM4fvLNLHQXyf7uam9waMPdG5ekr4vxCyQHPkrMMPPY", a_token: Token { address: "AS12FW5Rs5YN2zdpEnqwj4iHUUPt9R4Eqjq2qtpJFNKW3mn33RuLU", name: "Wrapped Massa", symbol: "WMAS", decimals: 9, dex: Dusa }, b_token: Token { address: "AS12N76WPYB3QNYKGhV2jZuQs1djdhNJLQgnm7m52pHWecvvj1fCQ", name: "USD Coin", symbol: "USDC", decimals: 6, dex: Dusa }, a_reserve: 11389.896016895, b_reserve: 5824.495311, dex: Dusa, swap_fee: 0.0, a_price: 0.519605816928739, b_price: 1.9245358066057685, active_bin_id: 8384823, bin_step: 20, bins: {8384822: Bin { id: 8384822, a_reserve: 0.0, b_reserve: 256.083299 }, 8384823: Bin { id: 8384823, a_reserve: 31.871325556, b_reserve: 240.034939 }, 8384824: Bin { id: 8384824, a_reserve: 493.827160493, b_reserve: 0.0 }} }
2025-07-01T00:10:50.867658Z  INFO quedge::spawns::swap_fetcher: Swap event processed successfully
2025-07-01T00:10:50.867916Z DEBUG quedge::helpers: Detecting arbitrage cycles...
2025-07-01T00:10:50.868118Z DEBUG quedge::helpers: Arbitrage cycles found (3 total):
2025-07-01T00:10:50.868246Z DEBUG quedge::helpers: Cycle path Inisde Simulation Task:
2025-07-01T00:10:50.868350Z DEBUG quedge::helpers::display: Path: ["WMAS-Dusa", "WMAS-EagleFi", "USDC-EagleFi", "USDC-Dusa", "WMAS-Dusa"]
2025-07-01T00:10:50.868474Z DEBUG quedge::helpers: Cycle path Inisde Simulation Task:
2025-07-01T00:10:50.868580Z  INFO quedge::helpers: Default start amount for token AS12FW5Rs5YN2zdpEnqwj4iHUUPt9R4Eqjq2qtpJFNKW3mn33RuLU is 10
2025-07-01T00:10:50.868684Z DEBUG quedge::helpers::display: Path: ["USDC-Dusa", "WMAS-Dusa", "WMAS-EagleFi", "USDC-EagleFi", "USDC-Dusa"]
2025-07-01T00:10:50.868806Z  INFO quedge::helpers: Offchain Amount out after Offchain simulation: 11.632871346929152
2025-07-01T00:10:50.868897Z DEBUG quedge::helpers: Cycle path Inisde Simulation Task:
2025-07-01T00:10:50.868993Z  INFO quedge::helpers: Offchain Earning percentage: 16.328713469291518
2025-07-01T00:10:50.869090Z DEBUG quedge::helpers::display: Path: ["USDC-EagleFi", "USDC-Dusa", "WMAS-Dusa", "WMAS-EagleFi", "USDC-EagleFi"]
2025-07-01T00:10:51.032712Z  INFO quedge::helpers: Amount out after Onchain simulation: 34.695385941
2025-07-01T00:10:51.032892Z  INFO quedge::helpers: Earning percentage: 15.651286469999992
2025-07-01T00:10:51.033156Z  INFO quedge::helpers: Found 1 valid cycles
2025-07-01T00:10:51.033377Z  INFO quedge::executor: Executing arbitrage with start amount 30 for cycle path: ["WMAS-Dusa", "WMAS-EagleFi", "USDC-EagleFi", "USDC-Dusa", "WMAS-Dusa"]...
2025-07-01T00:11:06.367677Z  INFO quedge::spawns::swap_fetcher: Found 3 Swap events from the operation O12GNcvUFdE1AjEW7hcPaFjCFe95F6bX8WerPbtvn9QULBWUtHBA. Processing them...
2025-07-01T00:11:06.367891Z  INFO quedge::spawns::swap_fetcher: New detected swap on pool: AS12dny2u8p6DmcPnYurnzqrAG7kMjYUcSD6bDCdJfbYeadYqXaL5
2025-07-01T00:11:06.523183Z DEBUG quedge::executor: Showing all operation events data for operation id: O12GNcvUFdE1AjEW7hcPaFjCFe95F6bX8WerPbtvn9QULBWUtHBA ...
2025-07-01T00:11:06.523335Z DEBUG quedge::executor: Event Data: "ArbRoutes inside execuwithFlmashLoan: Router Address: AS12JfpytS54Pq2Pi5ZP9dZ8kxe21TmwBpoiQuMNvAgB3bSVsCs9k\nPool Address: AS12dny2u8p6DmcPnYurnzqrAG7kMjYUcSD6bDCdJfbYeadYqXaL5\nToken In Address: AS12FW5Rs5YN2zdpEnqwj4iHUUPt9R4Eqjq2qtpJFNKW3mn33RuLU\nToken Out Address: AS12N76WPYB3QNYKGhV2jZuQs1djdhNJLQgnm7m52pHWecvvj1fCQ\nAmount In: 30000000000\nAmount Out Min: 1\nBin Step: 0\nTo: AS1VJvdvuU5AZFFKWLD2yxyHsS1CE65imQxQUShqNEf4BqZ2ZHLT\nDeadline: 1751329251033\nIs Dusa: false\nCoins To Use: 10000000\nSwap Params: \n,Router Address: AS1XqtvX3rz2RWbnqLfaYVKEjM3VS5pny9yKDdXcmJ5C1vrcLEFd\nPool Address: AS112Wdy9pM4fvLNLHQXyf7uam9waMPdG5ekr4vxCyQHPkrMMPPY\nToken In Address: AS12N76WPYB3QNYKGhV2jZuQs1djdhNJLQgnm7m52pHWecvvj1fCQ\nToken Out Address: AS12FW5Rs5YN2zdpEnqwj4iHUUPt9R4Eqjq2qtpJFNKW3mn33RuLU\nAmount In: 30000000000\nAmount Out Min: 1\nBin Step: 20\nTo: AS1VJvdvuU5AZFFKWLD2yxyHsS1CE65imQxQUShqNEf4BqZ2ZHLT\nDeadline: 1751329251033\nIs Dusa: true\nCoins To Use: 1000000\nSwap Params: \n"
2025-07-01T00:11:06.523485Z DEBUG quedge::executor: Event Data: "TRANSFER SUCCESS"
2025-07-01T00:11:06.523616Z DEBUG quedge::executor: Event Data: "TRANSFER SUCCESS"
2025-07-01T00:11:06.523730Z DEBUG quedge::executor: Event Data: "TRANSFER SUCCESS"
2025-07-01T00:11:06.523860Z DEBUG quedge::executor: Event Data: "TRANSFER SUCCESS"
2025-07-01T00:11:06.523987Z DEBUG quedge::executor: Event Data: "UPDATE_CUMULATIVE_PRICES::AS12dny2u8p6DmcPnYurnzqrAG7kMjYUcSD6bDCdJfbYeadYqXaL5,AS12JfpytS54Pq2Pi5ZP9dZ8kxe21TmwBpoiQuMNvAgB3bSVsCs9k,11571931598074727018000,3549581525533510000,1751328660500"
2025-07-01T00:11:06.524122Z DEBUG quedge::executor: Event Data: "SWAP:AS12dny2u8p6DmcPnYurnzqrAG7kMjYUcSD6bDCdJfbYeadYqXaL5,AS1VJvdvuU5AZFFKWLD2yxyHsS1CE65imQxQUShqNEf4BqZ2ZHLT,30000000000,AS12FW5Rs5YN2zdpEnqwj4iHUUPt9R4Eqjq2qtpJFNKW3mn33RuLU,18067007,AS12N76WPYB3QNYKGhV2jZuQs1djdhNJLQgnm7m52pHWecvvj1fCQ,90000000,22500000,67500000,5436110271648,3265551295"
2025-07-01T00:11:06.524240Z DEBUG quedge::executor: Event Data: "Spent 0 coins"
2025-07-01T00:11:06.524368Z DEBUG quedge::executor: Event Data: "Swap Route Exexcuted: Pool Address: AS12dny2u8p6DmcPnYurnzqrAG7kMjYUcSD6bDCdJfbYeadYqXaL5\nToken In Address: AS12FW5Rs5YN2zdpEnqwj4iHUUPt9R4Eqjq2qtpJFNKW3mn33RuLU\nToken Out Address: AS12N76WPYB3QNYKGhV2jZuQs1djdhNJLQgnm7m52pHWecvvj1fCQ\nReceiver Address: AS1VJvdvuU5AZFFKWLD2yxyHsS1CE65imQxQUShqNEf4BqZ2ZHLT\nAmount In: 30000000000\nMin Amount Out: 1 \nIs Transfer From: true"
2025-07-01T00:11:06.524524Z DEBUG quedge::executor: Event Data: "Spent 0 coins"
2025-07-01T00:11:06.524717Z DEBUG quedge::executor: Event Data: "TRANSFER SUCCESS"
2025-07-01T00:11:06.524969Z DEBUG quedge::executor: Event Data: "SWAP:AS1VJvdvuU5AZFFKWLD2yxyHsS1CE65imQxQUShqNEf4BqZ2ZHLT;?!8384823;?!false;?!놏ü\0\0\0\0\0\0\0\0\0\0\0\0\0\0;?!해殭\u{7}\0\0\0\0\0\0\0\0\0\0\0\0\0;?!0;?!膤\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0"
2025-07-01T00:11:06.525160Z DEBUG quedge::executor: Event Data: "SWAP:AS1VJvdvuU5AZFFKWLD2yxyHsS1CE65imQxQUShqNEf4BqZ2ZHLT;?!8384824;?!false;?!潽\u{16}\0\0\0\0\0\0\0\0\0\0\0\0\0\0;?!뷡ꡓ\0\0\0\0\0\0\0\0\0\0\0\0\0\0;?!10000;?!ஏ\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0"
2025-07-01T00:11:06.525318Z DEBUG quedge::executor: Event Data: "TRANSFER SUCCESS"
2025-07-01T00:11:06.525526Z DEBUG quedge::executor: Event Data: "Received coins"
2025-07-01T00:11:06.525728Z DEBUG quedge::executor: Event Data: "TRANSFER SUCCESS"
2025-07-01T00:11:06.525864Z DEBUG quedge::executor: Event Data: "TRANSFER SUCCESS"
2025-07-01T00:11:06.525997Z DEBUG quedge::executor: Event Data: "FLASH_ARB_EXEC:34695385941"
2025-07-01T00:11:06.526124Z DEBUG quedge::executor: Event Data: "TRANSFER SUCCESS"
2025-07-01T00:11:06.526255Z DEBUG quedge::executor: Event Data: "TRANSFER SUCCESS"
2025-07-01T00:11:06.526385Z DEBUG quedge::executor: Event Data: "UPDATE_CUMULATIVE_PRICES::AS1fewbVp4FMTEJH81e1BNaH9B3SmeE7sNpZQ2QqwP1wGBzLKJGF,AS1VJvdvuU5AZFFKWLD2yxyHsS1CE65imQxQUShqNEf4BqZ2ZHLT,675396169205193286129000,15012079794010651309667514515832000,1751328660500"
2025-07-01T00:11:06.526503Z DEBUG quedge::executor: Event Data: "Spent 0 coins"
2025-07-01T00:11:06.526638Z DEBUG quedge::executor: Event Data: "FLASH_LOAN:AS1fewbVp4FMTEJH81e1BNaH9B3SmeE7sNpZQ2QqwP1wGBzLKJGF,AS1VJvdvuU5AZFFKWLD2yxyHsS1CE65imQxQUShqNEf4BqZ2ZHLT,AS1VJvdvuU5AZFFKWLD2yxyHsS1CE65imQxQUShqNEf4BqZ2ZHLT,0,30000000000,12817214505974134383413567,674627845226602,1000"
2025-07-01T00:11:06.526819Z ERROR quedge::spawns::swap_fetcher: Error in detect_and_execute_arb: Failed to parse amount out to U256
2025-07-01T00:11:06.532592Z  INFO quedge::strategies::bellman_ford_matrix: Updating sparse matrix by pool: Pool { address: "AS12dny2u8p6DmcPnYurnzqrAG7kMjYUcSD6bDCdJfbYeadYqXaL5", a_token: Token { address: "AS12N76WPYB3QNYKGhV2jZuQs1djdhNJLQgnm7m52pHWecvvj1fCQ", name: "USD Coin", symbol: "USDC", decimals: 6, dex: EagleFi }, b_token: Token { address: "AS12FW5Rs5YN2zdpEnqwj4iHUUPt9R4Eqjq2qtpJFNKW3mn33RuLU", name: "Wrapped Massa", symbol: "WMAS", decimals: 9, dex: EagleFi }, a_reserve: 3265.551295, b_reserve: 5436.110271648, dex: EagleFi, swap_fee: 0.3, a_price: 1.664683779419242, b_price: 0.6007146896985264, active_bin_id: 0, bin_step: 0, bins: {} }
2025-07-01T00:11:06.532809Z  WARN quedge::spawns::swap_fetcher: Skipping arbitrage detection after updating the sparse matrix because of an order from the events stream
2025-07-01T00:11:06.532936Z  INFO quedge::spawns::swap_fetcher: Swap event processed successfully
2025-07-01T00:11:06.533111Z  INFO quedge::spawns::swap_fetcher: New detected swap on pool: AS112Wdy9pM4fvLNLHQXyf7uam9waMPdG5ekr4vxCyQHPkrMMPPY
2025-07-01T00:11:06.838956Z  INFO quedge::strategies::bellman_ford_matrix: Updating sparse matrix by pool: Pool { address: "AS112Wdy9pM4fvLNLHQXyf7uam9waMPdG5ekr4vxCyQHPkrMMPPY", a_token: Token { address: "AS12FW5Rs5YN2zdpEnqwj4iHUUPt9R4Eqjq2qtpJFNKW3mn33RuLU", name: "Wrapped Massa", symbol: "WMAS", decimals: 9, dex: Dusa }, b_token: Token { address: "AS12N76WPYB3QNYKGhV2jZuQs1djdhNJLQgnm7m52pHWecvvj1fCQ", name: "USD Coin", symbol: "USDC", decimals: 6, dex: Dusa }, a_reserve: 11355.200630954, b_reserve: 5842.526171, dex: Dusa, swap_fee: 0.0, a_price: 0.5206450285625964, b_price: 1.9206944177702283, active_bin_id: 8384824, bin_step: 20, bins: {8384823: Bin { id: 8384823, a_reserve: 0.0, b_reserve: 256.595466 }, 8384824: Bin { id: 8384824, a_reserve: 491.003100108, b_reserve: 1.470333 }, 8384825: Bin { id: 8384825, a_reserve: 493.827160493, b_reserve: 0.0 }} }
2025-07-01T00:11:06.839171Z  WARN quedge::spawns::swap_fetcher: Skipping arbitrage detection after updating the sparse matrix because of an order from the events stream
2025-07-01T00:11:06.839336Z  INFO quedge::spawns::swap_fetcher: Swap event processed successfully
2025-07-01T00:11:06.839492Z DEBUG quedge::spawns::swap_fetcher: Processing last event of the operation O12GNcvUFdE1AjEW7hcPaFjCFe95F6bX8WerPbtvn9QULBWUtHBA...
2025-07-01T00:11:06.839630Z  WARN quedge::spawns::swap_fetcher: Pool AS1VJvdvuU5AZFFKWLD2yxyHsS1CE65imQxQUShqNEf4BqZ2ZHLT not found in the pools hash map
2025-07-01T00:11:06.839758Z  WARN quedge::spawns::swap_fetcher: Pool AS1fewbVp4FMTEJH81e1BNaH9B3SmeE7sNpZQ2QqwP1wGBzLKJGF not found in the pools hash map
2025-07-01T00:11:06.839887Z  WARN quedge::spawns::swap_fetcher: Pool AS1VJvdvuU5AZFFKWLD2yxyHsS1CE65imQxQUShqNEf4BqZ2ZHLT not found in the pools hash map
2025-07-01T00:11:06.840033Z  WARN quedge::spawns::swap_fetcher: Pool AS1XqtvX3rz2RWbnqLfaYVKEjM3VS5pny9yKDdXcmJ5C1vrcLEFd not found in the pools hash map
2025-07-01T00:11:06.840178Z  INFO quedge::spawns::swap_fetcher: New detected swap on pool: AS112Wdy9pM4fvLNLHQXyf7uam9waMPdG5ekr4vxCyQHPkrMMPPY
2025-07-01T00:11:07.161090Z  INFO quedge::strategies::bellman_ford_matrix: Updating sparse matrix by pool: Pool { address: "AS112Wdy9pM4fvLNLHQXyf7uam9waMPdG5ekr4vxCyQHPkrMMPPY", a_token: Token { address: "AS12FW5Rs5YN2zdpEnqwj4iHUUPt9R4Eqjq2qtpJFNKW3mn33RuLU", name: "Wrapped Massa", symbol: "WMAS", decimals: 9, dex: Dusa }, b_token: Token { address: "AS12N76WPYB3QNYKGhV2jZuQs1djdhNJLQgnm7m52pHWecvvj1fCQ", name: "USD Coin", symbol: "USDC", decimals: 6, dex: Dusa }, a_reserve: 11355.200630954, b_reserve: 5842.526171, dex: Dusa, swap_fee: 0.0, a_price: 0.5206450285625964, b_price: 1.9206944177702283, active_bin_id: 8384824, bin_step: 20, bins: {8384823: Bin { id: 8384823, a_reserve: 0.0, b_reserve: 256.595466 }, 8384824: Bin { id: 8384824, a_reserve: 491.003100108, b_reserve: 1.470333 }, 8384825: Bin { id: 8384825, a_reserve: 493.827160493, b_reserve: 0.0 }} }
2025-07-01T00:11:07.161315Z  INFO quedge::spawns::swap_fetcher: Swap event processed successfully
2025-07-01T00:11:07.161624Z DEBUG quedge::helpers: Detecting arbitrage cycles...
2025-07-01T00:11:07.161954Z DEBUG quedge::helpers: Arbitrage cycles found (3 total):
2025-07-01T00:11:07.162126Z DEBUG quedge::helpers: Cycle path Inisde Simulation Task:
2025-07-01T00:11:07.162249Z DEBUG quedge::helpers::display: Path: ["WMAS-Dusa", "WMAS-EagleFi", "USDC-EagleFi", "USDC-Dusa", "WMAS-Dusa"]
2025-07-01T00:11:07.162423Z DEBUG quedge::helpers: Cycle path Inisde Simulation Task:
2025-07-01T00:11:07.162550Z DEBUG quedge::helpers::display: Path: ["USDC-Dusa", "WMAS-Dusa", "WMAS-EagleFi", "USDC-EagleFi", "USDC-Dusa"]
2025-07-01T00:11:07.162752Z DEBUG quedge::helpers: Cycle path Inisde Simulation Task:
2025-07-01T00:11:07.162859Z  INFO quedge::helpers: Default start amount for token AS12FW5Rs5YN2zdpEnqwj4iHUUPt9R4Eqjq2qtpJFNKW3mn33RuLU is 10
2025-07-01T00:11:07.162972Z DEBUG quedge::helpers::display: Path: ["USDC-EagleFi", "USDC-Dusa", "WMAS-Dusa", "WMAS-EagleFi", "USDC-EagleFi"]
2025-07-01T00:11:07.163079Z  INFO quedge::helpers: Offchain Amount out after Offchain simulation: 11.482221070764394
2025-07-01T00:11:07.163207Z  INFO quedge::helpers: Offchain Earning percentage: 14.822210707643944
2025-07-01T00:11:07.327348Z  INFO quedge::helpers: Amount out after Onchain simulation: 34.252358174
2025-07-01T00:11:07.327501Z  INFO quedge::helpers: Earning percentage: 14.17452724666667
2025-07-01T00:11:07.327725Z  INFO quedge::helpers: Found 1 valid cycles
2025-07-01T00:11:07.327928Z  INFO quedge::executor: Executing arbitrage with start amount 30 for cycle path: ["WMAS-Dusa", "WMAS-EagleFi", "USDC-EagleFi", "USDC-Dusa", "WMAS-Dusa"]...
2025-07-01T00:11:22.237967Z  INFO quedge::spawns::swap_fetcher: Found 2 Swap events from the operation O125fkGVehntdP4XAxUKqVq1rK612yXcjxtRMVEwVxQPf7q7qs7G. Processing them...
2025-07-01T00:11:22.238121Z  INFO quedge::spawns::swap_fetcher: New detected swap on pool: AS12dny2u8p6DmcPnYurnzqrAG7kMjYUcSD6bDCdJfbYeadYqXaL5
2025-07-01T00:11:22.397123Z  INFO quedge::strategies::bellman_ford_matrix: Updating sparse matrix by pool: Pool { address: "AS12dny2u8p6DmcPnYurnzqrAG7kMjYUcSD6bDCdJfbYeadYqXaL5", a_token: Token { address: "AS12N76WPYB3QNYKGhV2jZuQs1djdhNJLQgnm7m52pHWecvvj1fCQ", name: "USD Coin", symbol: "USDC", decimals: 6, dex: EagleFi }, b_token: Token { address: "AS12FW5Rs5YN2zdpEnqwj4iHUUPt9R4Eqjq2qtpJFNKW3mn33RuLU", name: "Wrapped Massa", symbol: "WMAS", decimals: 9, dex: EagleFi }, a_reserve: 3247.682236, b_reserve: 5466.087771648, dex: EagleFi, swap_fee: 0.3, a_price: 1.6830734580672195, b_price: 0.59415113179217, active_bin_id: 0, bin_step: 0, bins: {} }
2025-07-01T00:11:22.397285Z  WARN quedge::spawns::swap_fetcher: Skipping arbitrage detection after updating the sparse matrix because of an order from the events stream
2025-07-01T00:11:22.397421Z  INFO quedge::spawns::swap_fetcher: Swap event processed successfully
2025-07-01T00:11:22.397554Z DEBUG quedge::spawns::swap_fetcher: Processing last event of the operation O125fkGVehntdP4XAxUKqVq1rK612yXcjxtRMVEwVxQPf7q7qs7G...
2025-07-01T00:11:22.397676Z  WARN quedge::spawns::swap_fetcher: Pool AS1VJvdvuU5AZFFKWLD2yxyHsS1CE65imQxQUShqNEf4BqZ2ZHLT not found in the pools hash map
2025-07-01T00:11:22.397782Z  WARN quedge::spawns::swap_fetcher: Pool AS1fewbVp4FMTEJH81e1BNaH9B3SmeE7sNpZQ2QqwP1wGBzLKJGF not found in the pools hash map
2025-07-01T00:11:22.397899Z  WARN quedge::spawns::swap_fetcher: Pool AS1VJvdvuU5AZFFKWLD2yxyHsS1CE65imQxQUShqNEf4BqZ2ZHLT not found in the pools hash map
2025-07-01T00:11:22.398029Z  WARN quedge::spawns::swap_fetcher: Pool AS1XqtvX3rz2RWbnqLfaYVKEjM3VS5pny9yKDdXcmJ5C1vrcLEFd not found in the pools hash map
2025-07-01T00:11:22.398174Z  INFO quedge::spawns::swap_fetcher: New detected swap on pool: AS112Wdy9pM4fvLNLHQXyf7uam9waMPdG5ekr4vxCyQHPkrMMPPY
2025-07-01T00:11:22.414719Z DEBUG quedge::executor: Showing all operation events data for operation id: O125fkGVehntdP4XAxUKqVq1rK612yXcjxtRMVEwVxQPf7q7qs7G ...
2025-07-01T00:11:22.414862Z DEBUG quedge::executor: Event Data: "ArbRoutes inside execuwithFlmashLoan: Router Address: AS12JfpytS54Pq2Pi5ZP9dZ8kxe21TmwBpoiQuMNvAgB3bSVsCs9k\nPool Address: AS12dny2u8p6DmcPnYurnzqrAG7kMjYUcSD6bDCdJfbYeadYqXaL5\nToken In Address: AS12FW5Rs5YN2zdpEnqwj4iHUUPt9R4Eqjq2qtpJFNKW3mn33RuLU\nToken Out Address: AS12N76WPYB3QNYKGhV2jZuQs1djdhNJLQgnm7m52pHWecvvj1fCQ\nAmount In: 30000000000\nAmount Out Min: 1\nBin Step: 0\nTo: AS1VJvdvuU5AZFFKWLD2yxyHsS1CE65imQxQUShqNEf4BqZ2ZHLT\nDeadline: 1751329267327\nIs Dusa: false\nCoins To Use: 10000000\nSwap Params: \n,Router Address: AS1XqtvX3rz2RWbnqLfaYVKEjM3VS5pny9yKDdXcmJ5C1vrcLEFd\nPool Address: AS112Wdy9pM4fvLNLHQXyf7uam9waMPdG5ekr4vxCyQHPkrMMPPY\nToken In Address: AS12N76WPYB3QNYKGhV2jZuQs1djdhNJLQgnm7m52pHWecvvj1fCQ\nToken Out Address: AS12FW5Rs5YN2zdpEnqwj4iHUUPt9R4Eqjq2qtpJFNKW3mn33RuLU\nAmount In: 30000000000\nAmount Out Min: 1\nBin Step: 20\nTo: AS1VJvdvuU5AZFFKWLD2yxyHsS1CE65imQxQUShqNEf4BqZ2ZHLT\nDeadline: 1751329267327\nIs Dusa: true\nCoins To Use: 1000000\nSwap Params: \n"
2025-07-01T00:11:22.414979Z DEBUG quedge::executor: Event Data: "TRANSFER SUCCESS"
2025-07-01T00:11:22.415081Z DEBUG quedge::executor: Event Data: "TRANSFER SUCCESS"
2025-07-01T00:11:22.415191Z DEBUG quedge::executor: Event Data: "TRANSFER SUCCESS"
2025-07-01T00:11:22.415317Z DEBUG quedge::executor: Event Data: "TRANSFER SUCCESS"
2025-07-01T00:11:22.415425Z DEBUG quedge::executor: Event Data: "UPDATE_CUMULATIVE_PRICES::AS12dny2u8p6DmcPnYurnzqrAG7kMjYUcSD6bDCdJfbYeadYqXaL5,AS12JfpytS54Pq2Pi5ZP9dZ8kxe21TmwBpoiQuMNvAgB3bSVsCs9k,11572019055479073386000,3549633488449286000,1751328676500"
2025-07-01T00:11:22.415538Z DEBUG quedge::executor: Event Data: "SWAP:AS12dny2u8p6DmcPnYurnzqrAG7kMjYUcSD6bDCdJfbYeadYqXaL5,AS1VJvdvuU5AZFFKWLD2yxyHsS1CE65imQxQUShqNEf4BqZ2ZHLT,30000000000,AS12FW5Rs5YN2zdpEnqwj4iHUUPt9R4Eqjq2qtpJFNKW3mn33RuLU,17869059,AS12N76WPYB3QNYKGhV2jZuQs1djdhNJLQgnm7m52pHWecvvj1fCQ,90000000,22500000,67500000,5466087771648,3247682236"
2025-07-01T00:11:22.415640Z DEBUG quedge::executor: Event Data: "Spent 0 coins"
2025-07-01T00:11:22.415752Z DEBUG quedge::executor: Event Data: "Swap Route Exexcuted: Pool Address: AS12dny2u8p6DmcPnYurnzqrAG7kMjYUcSD6bDCdJfbYeadYqXaL5\nToken In Address: AS12FW5Rs5YN2zdpEnqwj4iHUUPt9R4Eqjq2qtpJFNKW3mn33RuLU\nToken Out Address: AS12N76WPYB3QNYKGhV2jZuQs1djdhNJLQgnm7m52pHWecvvj1fCQ\nReceiver Address: AS1VJvdvuU5AZFFKWLD2yxyHsS1CE65imQxQUShqNEf4BqZ2ZHLT\nAmount In: 30000000000\nMin Amount Out: 1 \nIs Transfer From: true"
2025-07-01T00:11:22.415851Z DEBUG quedge::executor: Event Data: "Spent 0 coins"
2025-07-01T00:11:22.415948Z DEBUG quedge::executor: Event Data: "TRANSFER SUCCESS"
2025-07-01T00:11:22.416053Z DEBUG quedge::executor: Event Data: "SWAP:AS1VJvdvuU5AZFFKWLD2yxyHsS1CE65imQxQUShqNEf4BqZ2ZHLT;?!8384824;?!false;?!ᵨĐ\0\0\0\0\0\0\0\0\0\0\0\0\0\0;?!舞蓮\u{7}\0\0\0\0\0\0\0\0\0\0\0\0\0;?!0;?!讛\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0"
2025-07-01T00:11:22.416153Z DEBUG quedge::executor: Event Data: "TRANSFER SUCCESS"
2025-07-01T00:11:22.416251Z DEBUG quedge::executor: Event Data: "Received coins"
2025-07-01T00:11:22.416345Z DEBUG quedge::executor: Event Data: "TRANSFER SUCCESS"
2025-07-01T00:11:22.416441Z DEBUG quedge::executor: Event Data: "TRANSFER SUCCESS"
2025-07-01T00:11:22.416545Z DEBUG quedge::executor: Event Data: "FLASH_ARB_EXEC:34252358174"
2025-07-01T00:11:22.416640Z DEBUG quedge::executor: Event Data: "TRANSFER SUCCESS"
2025-07-01T00:11:22.416742Z DEBUG quedge::executor: Event Data: "TRANSFER SUCCESS"
2025-07-01T00:11:22.416839Z DEBUG quedge::executor: Event Data: "UPDATE_CUMULATIVE_PRICES::AS1fewbVp4FMTEJH81e1BNaH9B3SmeE7sNpZQ2QqwP1wGBzLKJGF,AS1VJvdvuU5AZFFKWLD2yxyHsS1CE65imQxQUShqNEf4BqZ2ZHLT,675406950577797727841000,15012284793842134032517170538552000,1751328676500"
2025-07-01T00:11:22.416933Z DEBUG quedge::executor: Event Data: "Spent 0 coins"
2025-07-01T00:11:22.417029Z DEBUG quedge::executor: Event Data: "FLASH_LOAN:AS1fewbVp4FMTEJH81e1BNaH9B3SmeE7sNpZQ2QqwP1wGBzLKJGF,AS1VJvdvuU5AZFFKWLD2yxyHsS1CE65imQxQUShqNEf4BqZ2ZHLT,AS1VJvdvuU5AZFFKWLD2yxyHsS1CE65imQxQUShqNEf4BqZ2ZHLT,0,30000000000,12817214505974134383413567,674627845226602,1000"
2025-07-01T00:11:22.417176Z ERROR quedge::spawns::swap_fetcher: Error in detect_and_execute_arb: Failed to parse amount out to U256
2025-07-01T00:11:22.705067Z  INFO quedge::strategies::bellman_ford_matrix: Updating sparse matrix by pool: Pool { address: "AS112Wdy9pM4fvLNLHQXyf7uam9waMPdG5ekr4vxCyQHPkrMMPPY", a_token: Token { address: "AS12FW5Rs5YN2zdpEnqwj4iHUUPt9R4Eqjq2qtpJFNKW3mn33RuLU", name: "Wrapped Massa", symbol: "WMAS", decimals: 9, dex: Dusa }, b_token: Token { address: "AS12N76WPYB3QNYKGhV2jZuQs1djdhNJLQgnm7m52pHWecvvj1fCQ", name: "USD Coin", symbol: "USDC", decimals: 6, dex: Dusa }, a_reserve: 11320.94827278, b_reserve: 5860.359491, dex: Dusa, swap_fee: 0.0, a_price: 0.5206450285625964, b_price: 1.9206944177702283, active_bin_id: 8384824, bin_step: 20, bins: {8384823: Bin { id: 8384823, a_reserve: 0.0, b_reserve: 256.595466 }, 8384824: Bin { id: 8384824, a_reserve: 456.750741934, b_reserve: 19.303653 }, 8384825: Bin { id: 8384825, a_reserve: 493.827160493, b_reserve: 0.0 }} }
2025-07-01T00:11:22.705269Z  INFO quedge::spawns::swap_fetcher: Swap event processed successfully
2025-07-01T00:11:22.705497Z DEBUG quedge::helpers: Detecting arbitrage cycles...
2025-07-01T00:11:22.705721Z DEBUG quedge::helpers: Arbitrage cycles found (3 total):
2025-07-01T00:11:22.705839Z DEBUG quedge::helpers: Cycle path Inisde Simulation Task:
2025-07-01T00:11:22.705934Z DEBUG quedge::helpers::display: Path: ["WMAS-Dusa", "WMAS-EagleFi", "USDC-EagleFi", "USDC-Dusa", "WMAS-Dusa"]
2025-07-01T00:11:22.706074Z DEBUG quedge::helpers: Cycle path Inisde Simulation Task:
2025-07-01T00:11:22.706175Z  INFO quedge::helpers: Default start amount for token AS12FW5Rs5YN2zdpEnqwj4iHUUPt9R4Eqjq2qtpJFNKW3mn33RuLU is 10
2025-07-01T00:11:22.706337Z DEBUG quedge::helpers::display: Path: ["USDC-Dusa", "WMAS-Dusa", "WMAS-EagleFi", "USDC-EagleFi", "USDC-Dusa"]
2025-07-01T00:11:22.706435Z  INFO quedge::helpers: Offchain Amount out after Offchain simulation: 11.35687749342533
2025-07-01T00:11:22.706525Z DEBUG quedge::helpers: Cycle path Inisde Simulation Task:
2025-07-01T00:11:22.706600Z  INFO quedge::helpers: Offchain Earning percentage: 13.568774934253296
2025-07-01T00:11:22.706681Z DEBUG quedge::helpers::display: Path: ["USDC-EagleFi", "USDC-Dusa", "WMAS-Dusa", "WMAS-EagleFi", "USDC-EagleFi"]
2025-07-01T00:11:22.863603Z  INFO quedge::helpers: Amount out after Onchain simulation: 33.879124993
2025-07-01T00:11:22.863737Z  INFO quedge::helpers: Earning percentage: 12.930416643333325
2025-07-01T00:11:22.863931Z  INFO quedge::helpers: Found 1 valid cycles
2025-07-01T00:11:22.864106Z  INFO quedge::executor: Executing arbitrage with start amount 30 for cycle path: ["WMAS-Dusa", "WMAS-EagleFi", "USDC-EagleFi", "USDC-Dusa", "WMAS-Dusa"]...
2025-07-01T00:11:38.400143Z  INFO quedge::spawns::swap_fetcher: Found 2 Swap events from the operation O1R9nKLGwfamCceYLjhfbq2UVvxd51dY4tibrWw3j96mxh8rUZD. Processing them...
2025-07-01T00:11:38.400329Z  INFO quedge::spawns::swap_fetcher: New detected swap on pool: AS12dny2u8p6DmcPnYurnzqrAG7kMjYUcSD6bDCdJfbYeadYqXaL5
2025-07-01T00:11:38.571520Z DEBUG quedge::executor: Showing all operation events data for operation id: O1R9nKLGwfamCceYLjhfbq2UVvxd51dY4tibrWw3j96mxh8rUZD ...
2025-07-01T00:11:38.571704Z DEBUG quedge::executor: Event Data: "ArbRoutes inside execuwithFlmashLoan: Router Address: AS12JfpytS54Pq2Pi5ZP9dZ8kxe21TmwBpoiQuMNvAgB3bSVsCs9k\nPool Address: AS12dny2u8p6DmcPnYurnzqrAG7kMjYUcSD6bDCdJfbYeadYqXaL5\nToken In Address: AS12FW5Rs5YN2zdpEnqwj4iHUUPt9R4Eqjq2qtpJFNKW3mn33RuLU\nToken Out Address: AS12N76WPYB3QNYKGhV2jZuQs1djdhNJLQgnm7m52pHWecvvj1fCQ\nAmount In: 30000000000\nAmount Out Min: 1\nBin Step: 0\nTo: AS1VJvdvuU5AZFFKWLD2yxyHsS1CE65imQxQUShqNEf4BqZ2ZHLT\nDeadline: 1751329282863\nIs Dusa: false\nCoins To Use: 10000000\nSwap Params: \n,Router Address: AS1XqtvX3rz2RWbnqLfaYVKEjM3VS5pny9yKDdXcmJ5C1vrcLEFd\nPool Address: AS112Wdy9pM4fvLNLHQXyf7uam9waMPdG5ekr4vxCyQHPkrMMPPY\nToken In Address: AS12N76WPYB3QNYKGhV2jZuQs1djdhNJLQgnm7m52pHWecvvj1fCQ\nToken Out Address: AS12FW5Rs5YN2zdpEnqwj4iHUUPt9R4Eqjq2qtpJFNKW3mn33RuLU\nAmount In: 30000000000\nAmount Out Min: 1\nBin Step: 20\nTo: AS1VJvdvuU5AZFFKWLD2yxyHsS1CE65imQxQUShqNEf4BqZ2ZHLT\nDeadline: 1751329282863\nIs Dusa: true\nCoins To Use: 1000000\nSwap Params: \n"
2025-07-01T00:11:38.571855Z DEBUG quedge::executor: Event Data: "TRANSFER SUCCESS"
2025-07-01T00:11:38.571973Z DEBUG quedge::executor: Event Data: "TRANSFER SUCCESS"
2025-07-01T00:11:38.572114Z DEBUG quedge::executor: Event Data: "TRANSFER SUCCESS"
2025-07-01T00:11:38.572254Z DEBUG quedge::executor: Event Data: "TRANSFER SUCCESS"
2025-07-01T00:11:38.572408Z DEBUG quedge::executor: Event Data: "UPDATE_CUMULATIVE_PRICES::AS12dny2u8p6DmcPnYurnzqrAG7kMjYUcSD6bDCdJfbYeadYqXaL5,AS12JfpytS54Pq2Pi5ZP9dZ8kxe21TmwBpoiQuMNvAgB3bSVsCs9k,11572106992523419754000,3549685168575510000,1751328692500"
2025-07-01T00:11:38.572536Z DEBUG quedge::executor: Event Data: "SWAP:AS12dny2u8p6DmcPnYurnzqrAG7kMjYUcSD6bDCdJfbYeadYqXaL5,AS1VJvdvuU5AZFFKWLD2yxyHsS1CE65imQxQUShqNEf4BqZ2ZHLT,30000000000,AS12FW5Rs5YN2zdpEnqwj4iHUUPt9R4Eqjq2qtpJFNKW3mn33RuLU,17674347,AS12N76WPYB3QNYKGhV2jZuQs1djdhNJLQgnm7m52pHWecvvj1fCQ,90000000,22500000,67500000,5496065271648,3230007889"
2025-07-01T00:11:38.572645Z DEBUG quedge::executor: Event Data: "Spent 0 coins"
2025-07-01T00:11:38.572775Z DEBUG quedge::executor: Event Data: "Swap Route Exexcuted: Pool Address: AS12dny2u8p6DmcPnYurnzqrAG7kMjYUcSD6bDCdJfbYeadYqXaL5\nToken In Address: AS12FW5Rs5YN2zdpEnqwj4iHUUPt9R4Eqjq2qtpJFNKW3mn33RuLU\nToken Out Address: AS12N76WPYB3QNYKGhV2jZuQs1djdhNJLQgnm7m52pHWecvvj1fCQ\nReceiver Address: AS1VJvdvuU5AZFFKWLD2yxyHsS1CE65imQxQUShqNEf4BqZ2ZHLT\nAmount In: 30000000000\nMin Amount Out: 1 \nIs Transfer From: true"
2025-07-01T00:11:38.572891Z DEBUG quedge::executor: Event Data: "Spent 0 coins"
2025-07-01T00:11:38.573002Z DEBUG quedge::executor: Event Data: "TRANSFER SUCCESS"
2025-07-01T00:11:38.573153Z DEBUG quedge::executor: Event Data: "SWAP:AS1VJvdvuU5AZFFKWLD2yxyHsS1CE65imQxQUShqNEf4BqZ2ZHLT;?!8384824;?!false;?!♖č\0\0\0\0\0\0\0\0\0\0\0\0\0\0;?!氁\u{e35a}\u{7}\0\0\0\0\0\0\0\0\0\0\0\0\0;?!0;?!訕\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0"
2025-07-01T00:11:38.573281Z DEBUG quedge::executor: Event Data: "TRANSFER SUCCESS"
2025-07-01T00:11:38.573403Z DEBUG quedge::executor: Event Data: "Received coins"
2025-07-01T00:11:38.573528Z DEBUG quedge::executor: Event Data: "TRANSFER SUCCESS"
2025-07-01T00:11:38.573677Z DEBUG quedge::executor: Event Data: "TRANSFER SUCCESS"
2025-07-01T00:11:38.573822Z DEBUG quedge::executor: Event Data: "FLASH_ARB_EXEC:33879124993"
2025-07-01T00:11:38.573944Z DEBUG quedge::executor: Event Data: "TRANSFER SUCCESS"
2025-07-01T00:11:38.574067Z DEBUG quedge::executor: Event Data: "TRANSFER SUCCESS"
2025-07-01T00:11:38.574187Z DEBUG quedge::executor: Event Data: "UPDATE_CUMULATIVE_PRICES::AS1fewbVp4FMTEJH81e1BNaH9B3SmeE7sNpZQ2QqwP1wGBzLKJGF,AS1VJvdvuU5AZFFKWLD2yxyHsS1CE65imQxQUShqNEf4BqZ2ZHLT,675417731950402169553000,15012489793673616755366826561272000,1751328692500"
2025-07-01T00:11:38.574298Z DEBUG quedge::executor: Event Data: "Spent 0 coins"
2025-07-01T00:11:38.574411Z DEBUG quedge::executor: Event Data: "FLASH_LOAN:AS1fewbVp4FMTEJH81e1BNaH9B3SmeE7sNpZQ2QqwP1wGBzLKJGF,AS1VJvdvuU5AZFFKWLD2yxyHsS1CE65imQxQUShqNEf4BqZ2ZHLT,AS1VJvdvuU5AZFFKWLD2yxyHsS1CE65imQxQUShqNEf4BqZ2ZHLT,0,30000000000,12817214505974134383413567,674627845226602,1000"
2025-07-01T00:11:38.574561Z ERROR quedge::spawns::swap_fetcher: Error in detect_and_execute_arb: Failed to parse amount out to U256
2025-07-01T00:11:38.575055Z  INFO quedge::strategies::bellman_ford_matrix: Updating sparse matrix by pool: Pool { address: "AS12dny2u8p6DmcPnYurnzqrAG7kMjYUcSD6bDCdJfbYeadYqXaL5", a_token: Token { address: "AS12N76WPYB3QNYKGhV2jZuQs1djdhNJLQgnm7m52pHWecvvj1fCQ", name: "USD Coin", symbol: "USDC", decimals: 6, dex: EagleFi }, b_token: Token { address: "AS12FW5Rs5YN2zdpEnqwj4iHUUPt9R4Eqjq2qtpJFNKW3mn33RuLU", name: "Wrapped Massa", symbol: "WMAS", decimals: 9, dex: EagleFi }, a_reserve: 3230.007889, b_reserve: 5496.065271648, dex: EagleFi, swap_fee: 0.3, a_price: 1.7015640396313596, b_price: 0.5876946013837058, active_bin_id: 0, bin_step: 0, bins: {} }
2025-07-01T00:11:38.575195Z  WARN quedge::spawns::swap_fetcher: Skipping arbitrage detection after updating the sparse matrix because of an order from the events stream
2025-07-01T00:11:38.575310Z  INFO quedge::spawns::swap_fetcher: Swap event processed successfully
2025-07-01T00:11:38.575413Z DEBUG quedge::spawns::swap_fetcher: Processing last event of the operation O1R9nKLGwfamCceYLjhfbq2UVvxd51dY4tibrWw3j96mxh8rUZD...
2025-07-01T00:11:38.575516Z  WARN quedge::spawns::swap_fetcher: Pool AS1VJvdvuU5AZFFKWLD2yxyHsS1CE65imQxQUShqNEf4BqZ2ZHLT not found in the pools hash map
2025-07-01T00:11:38.575607Z  WARN quedge::spawns::swap_fetcher: Pool AS1fewbVp4FMTEJH81e1BNaH9B3SmeE7sNpZQ2QqwP1wGBzLKJGF not found in the pools hash map
2025-07-01T00:11:38.575694Z  WARN quedge::spawns::swap_fetcher: Pool AS1VJvdvuU5AZFFKWLD2yxyHsS1CE65imQxQUShqNEf4BqZ2ZHLT not found in the pools hash map
2025-07-01T00:11:38.575782Z  WARN quedge::spawns::swap_fetcher: Pool AS1XqtvX3rz2RWbnqLfaYVKEjM3VS5pny9yKDdXcmJ5C1vrcLEFd not found in the pools hash map
2025-07-01T00:11:38.575895Z  INFO quedge::spawns::swap_fetcher: New detected swap on pool: AS112Wdy9pM4fvLNLHQXyf7uam9waMPdG5ekr4vxCyQHPkrMMPPY
2025-07-01T00:11:38.894125Z  INFO quedge::strategies::bellman_ford_matrix: Updating sparse matrix by pool: Pool { address: "AS112Wdy9pM4fvLNLHQXyf7uam9waMPdG5ekr4vxCyQHPkrMMPPY", a_token: Token { address: "AS12FW5Rs5YN2zdpEnqwj4iHUUPt9R4Eqjq2qtpJFNKW3mn33RuLU", name: "Wrapped Massa", symbol: "WMAS", decimals: 9, dex: Dusa }, b_token: Token { address: "AS12N76WPYB3QNYKGhV2jZuQs1djdhNJLQgnm7m52pHWecvvj1fCQ", name: "USD Coin", symbol: "USDC", decimals: 6, dex: Dusa }, a_reserve: 11287.069147787, b_reserve: 5877.998489, dex: Dusa, swap_fee: 0.0, a_price: 0.5206450285625964, b_price: 1.9206944177702283, active_bin_id: 8384824, bin_step: 20, bins: {8384823: Bin { id: 8384823, a_reserve: 0.0, b_reserve: 256.595466 }, 8384824: Bin { id: 8384824, a_reserve: 422.871616941, b_reserve: 36.942651 }, 8384825: Bin { id: 8384825, a_reserve: 493.827160493, b_reserve: 0.0 }} }
2025-07-01T00:11:38.894356Z  INFO quedge::spawns::swap_fetcher: Swap event processed successfully
2025-07-01T00:11:38.894649Z DEBUG quedge::helpers: Detecting arbitrage cycles...
2025-07-01T00:11:38.894870Z DEBUG quedge::helpers: Arbitrage cycles found (3 total):
2025-07-01T00:11:38.895022Z DEBUG quedge::helpers: Cycle path Inisde Simulation Task:
2025-07-01T00:11:38.895144Z DEBUG quedge::helpers::display: Path: ["WMAS-Dusa", "WMAS-EagleFi", "USDC-EagleFi", "USDC-Dusa", "WMAS-Dusa"]
2025-07-01T00:11:38.895291Z DEBUG quedge::helpers: Cycle path Inisde Simulation Task:
2025-07-01T00:11:38.895377Z  INFO quedge::helpers: Default start amount for token AS12FW5Rs5YN2zdpEnqwj4iHUUPt9R4Eqjq2qtpJFNKW3mn33RuLU is 10
2025-07-01T00:11:38.895479Z DEBUG quedge::helpers::display: Path: ["USDC-Dusa", "WMAS-Dusa", "WMAS-EagleFi", "USDC-EagleFi", "USDC-Dusa"]
2025-07-01T00:11:38.895582Z  INFO quedge::helpers: Offchain Amount out after Offchain simulation: 11.2335759620725
2025-07-01T00:11:38.895673Z DEBUG quedge::helpers: Cycle path Inisde Simulation Task:
2025-07-01T00:11:38.895754Z  INFO quedge::helpers: Offchain Earning percentage: 12.335759620725
2025-07-01T00:11:38.895835Z DEBUG quedge::helpers::display: Path: ["USDC-EagleFi", "USDC-Dusa", "WMAS-Dusa", "WMAS-EagleFi", "USDC-EagleFi"]
2025-07-01T00:11:39.050657Z  INFO quedge::helpers: Amount out after Onchain simulation: 33.511961207
2025-07-01T00:11:39.050839Z  INFO quedge::helpers: Earning percentage: 11.706537356666663
2025-07-01T00:11:39.051041Z  INFO quedge::helpers: Found 1 valid cycles
2025-07-01T00:11:39.051215Z  INFO quedge::executor: Executing arbitrage with start amount 30 for cycle path: ["WMAS-Dusa", "WMAS-EagleFi", "USDC-EagleFi", "USDC-Dusa", "WMAS-Dusa"]...
2025-07-01T00:11:54.361440Z  INFO quedge::spawns::swap_fetcher: Found 2 Swap events from the operation O1HsHdJwcGAHcw5AJRj3ewT243RoENYmkw3w49qUxEpL7QffVa1. Processing them...
2025-07-01T00:11:54.361606Z  INFO quedge::spawns::swap_fetcher: New detected swap on pool: AS12dny2u8p6DmcPnYurnzqrAG7kMjYUcSD6bDCdJfbYeadYqXaL5
2025-07-01T00:11:54.395002Z DEBUG quedge::executor: Showing all operation events data for operation id: O1HsHdJwcGAHcw5AJRj3ewT243RoENYmkw3w49qUxEpL7QffVa1 ...
2025-07-01T00:11:54.395160Z DEBUG quedge::executor: Event Data: "ArbRoutes inside execuwithFlmashLoan: Router Address: AS12JfpytS54Pq2Pi5ZP9dZ8kxe21TmwBpoiQuMNvAgB3bSVsCs9k\nPool Address: AS12dny2u8p6DmcPnYurnzqrAG7kMjYUcSD6bDCdJfbYeadYqXaL5\nToken In Address: AS12FW5Rs5YN2zdpEnqwj4iHUUPt9R4Eqjq2qtpJFNKW3mn33RuLU\nToken Out Address: AS12N76WPYB3QNYKGhV2jZuQs1djdhNJLQgnm7m52pHWecvvj1fCQ\nAmount In: 30000000000\nAmount Out Min: 1\nBin Step: 0\nTo: AS1VJvdvuU5AZFFKWLD2yxyHsS1CE65imQxQUShqNEf4BqZ2ZHLT\nDeadline: 1751329299051\nIs Dusa: false\nCoins To Use: 10000000\nSwap Params: \n,Router Address: AS1XqtvX3rz2RWbnqLfaYVKEjM3VS5pny9yKDdXcmJ5C1vrcLEFd\nPool Address: AS112Wdy9pM4fvLNLHQXyf7uam9waMPdG5ekr4vxCyQHPkrMMPPY\nToken In Address: AS12N76WPYB3QNYKGhV2jZuQs1djdhNJLQgnm7m52pHWecvvj1fCQ\nToken Out Address: AS12FW5Rs5YN2zdpEnqwj4iHUUPt9R4Eqjq2qtpJFNKW3mn33RuLU\nAmount In: 30000000000\nAmount Out Min: 1\nBin Step: 20\nTo: AS1VJvdvuU5AZFFKWLD2yxyHsS1CE65imQxQUShqNEf4BqZ2ZHLT\nDeadline: 1751329299051\nIs Dusa: true\nCoins To Use: 1000000\nSwap Params: \n"
2025-07-01T00:11:54.395281Z DEBUG quedge::executor: Event Data: "TRANSFER SUCCESS"
2025-07-01T00:11:54.395374Z DEBUG quedge::executor: Event Data: "TRANSFER SUCCESS"
2025-07-01T00:11:54.395479Z DEBUG quedge::executor: Event Data: "TRANSFER SUCCESS"
2025-07-01T00:11:54.395586Z DEBUG quedge::executor: Event Data: "TRANSFER SUCCESS"
2025-07-01T00:11:54.395700Z DEBUG quedge::executor: Event Data: "UPDATE_CUMULATIVE_PRICES::AS12dny2u8p6DmcPnYurnzqrAG7kMjYUcSD6bDCdJfbYeadYqXaL5,AS12JfpytS54Pq2Pi5ZP9dZ8kxe21TmwBpoiQuMNvAgB3bSVsCs9k,11572195409207766122000,3549736568976902000,1751328708500"
2025-07-01T00:11:54.395823Z DEBUG quedge::executor: Event Data: "SWAP:AS12dny2u8p6DmcPnYurnzqrAG7kMjYUcSD6bDCdJfbYeadYqXaL5,AS1VJvdvuU5AZFFKWLD2yxyHsS1CE65imQxQUShqNEf4BqZ2ZHLT,30000000000,AS12FW5Rs5YN2zdpEnqwj4iHUUPt9R4Eqjq2qtpJFNKW3mn33RuLU,17482802,AS12N76WPYB3QNYKGhV2jZuQs1djdhNJLQgnm7m52pHWecvvj1fCQ,90000000,22500000,67500000,5526042771648,3212525087"
2025-07-01T00:11:54.395925Z DEBUG quedge::executor: Event Data: "Spent 0 coins"
2025-07-01T00:11:54.396026Z DEBUG quedge::executor: Event Data: "Swap Route Exexcuted: Pool Address: AS12dny2u8p6DmcPnYurnzqrAG7kMjYUcSD6bDCdJfbYeadYqXaL5\nToken In Address: AS12FW5Rs5YN2zdpEnqwj4iHUUPt9R4Eqjq2qtpJFNKW3mn33RuLU\nToken Out Address: AS12N76WPYB3QNYKGhV2jZuQs1djdhNJLQgnm7m52pHWecvvj1fCQ\nReceiver Address: AS1VJvdvuU5AZFFKWLD2yxyHsS1CE65imQxQUShqNEf4BqZ2ZHLT\nAmount In: 30000000000\nMin Amount Out: 1 \nIs Transfer From: true"
2025-07-01T00:11:54.396134Z DEBUG quedge::executor: Event Data: "Spent 0 coins"
2025-07-01T00:11:54.396229Z DEBUG quedge::executor: Event Data: "TRANSFER SUCCESS"
2025-07-01T00:11:54.396332Z DEBUG quedge::executor: Event Data: "SWAP:AS1VJvdvuU5AZFFKWLD2yxyHsS1CE65imQxQUShqNEf4BqZ2ZHLT;?!8384824;?!false;?!㮜Ċ\0\0\0\0\0\0\0\0\0\0\0\0\0\0;?!\u{f277}쵷\u{7}\0\0\0\0\0\0\0\0\0\0\0\0\0;?!0;?!袖\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0"
2025-07-01T00:11:54.396431Z DEBUG quedge::executor: Event Data: "TRANSFER SUCCESS"
2025-07-01T00:11:54.396527Z DEBUG quedge::executor: Event Data: "Received coins"
2025-07-01T00:11:54.396627Z DEBUG quedge::executor: Event Data: "TRANSFER SUCCESS"
2025-07-01T00:11:54.396728Z DEBUG quedge::executor: Event Data: "TRANSFER SUCCESS"
2025-07-01T00:11:54.396821Z DEBUG quedge::executor: Event Data: "FLASH_ARB_EXEC:33511961207"
2025-07-01T00:11:54.396922Z DEBUG quedge::executor: Event Data: "TRANSFER SUCCESS"
2025-07-01T00:11:54.397016Z DEBUG quedge::executor: Event Data: "TRANSFER SUCCESS"
2025-07-01T00:11:54.397111Z DEBUG quedge::executor: Event Data: "UPDATE_CUMULATIVE_PRICES::AS1fewbVp4FMTEJH81e1BNaH9B3SmeE7sNpZQ2QqwP1wGBzLKJGF,AS1VJvdvuU5AZFFKWLD2yxyHsS1CE65imQxQUShqNEf4BqZ2ZHLT,675428513323006611265000,15012694793505099478216482583992000,1751328708500"
2025-07-01T00:11:54.397218Z DEBUG quedge::executor: Event Data: "Spent 0 coins"
2025-07-01T00:11:54.397317Z DEBUG quedge::executor: Event Data: "FLASH_LOAN:AS1fewbVp4FMTEJH81e1BNaH9B3SmeE7sNpZQ2QqwP1wGBzLKJGF,AS1VJvdvuU5AZFFKWLD2yxyHsS1CE65imQxQUShqNEf4BqZ2ZHLT,AS1VJvdvuU5AZFFKWLD2yxyHsS1CE65imQxQUShqNEf4BqZ2ZHLT,0,30000000000,12817214505974134383413567,674627845226602,1000"
2025-07-01T00:11:54.397464Z ERROR quedge::spawns::swap_fetcher: Error in detect_and_execute_arb: Failed to parse amount out to U256
2025-07-01T00:11:54.526869Z  INFO quedge::strategies::bellman_ford_matrix: Updating sparse matrix by pool: Pool { address: "AS12dny2u8p6DmcPnYurnzqrAG7kMjYUcSD6bDCdJfbYeadYqXaL5", a_token: Token { address: "AS12N76WPYB3QNYKGhV2jZuQs1djdhNJLQgnm7m52pHWecvvj1fCQ", name: "USD Coin", symbol: "USDC", decimals: 6, dex: EagleFi }, b_token: Token { address: "AS12FW5Rs5YN2zdpEnqwj4iHUUPt9R4Eqjq2qtpJFNKW3mn33RuLU", name: "Wrapped Massa", symbol: "WMAS", decimals: 9, dex: EagleFi }, a_reserve: 3212.525087, b_reserve: 5526.042771648, dex: EagleFi, swap_fee: 0.3, a_price: 1.7201555231459582, b_price: 0.5813427835705924, active_bin_id: 0, bin_step: 0, bins: {} }
2025-07-01T00:11:54.527141Z  WARN quedge::spawns::swap_fetcher: Skipping arbitrage detection after updating the sparse matrix because of an order from the events stream
2025-07-01T00:11:54.527334Z  INFO quedge::spawns::swap_fetcher: Swap event processed successfully
2025-07-01T00:11:54.527488Z DEBUG quedge::spawns::swap_fetcher: Processing last event of the operation O1HsHdJwcGAHcw5AJRj3ewT243RoENYmkw3w49qUxEpL7QffVa1...
2025-07-01T00:11:54.527600Z  WARN quedge::spawns::swap_fetcher: Pool AS1VJvdvuU5AZFFKWLD2yxyHsS1CE65imQxQUShqNEf4BqZ2ZHLT not found in the pools hash map
2025-07-01T00:11:54.527726Z  WARN quedge::spawns::swap_fetcher: Pool AS1fewbVp4FMTEJH81e1BNaH9B3SmeE7sNpZQ2QqwP1wGBzLKJGF not found in the pools hash map
2025-07-01T00:11:54.527825Z  WARN quedge::spawns::swap_fetcher: Pool AS1VJvdvuU5AZFFKWLD2yxyHsS1CE65imQxQUShqNEf4BqZ2ZHLT not found in the pools hash map
2025-07-01T00:11:54.527924Z  WARN quedge::spawns::swap_fetcher: Pool AS1XqtvX3rz2RWbnqLfaYVKEjM3VS5pny9yKDdXcmJ5C1vrcLEFd not found in the pools hash map
2025-07-01T00:11:54.528023Z  INFO quedge::spawns::swap_fetcher: New detected swap on pool: AS112Wdy9pM4fvLNLHQXyf7uam9waMPdG5ekr4vxCyQHPkrMMPPY
2025-07-01T00:11:54.834378Z  INFO quedge::strategies::bellman_ford_matrix: Updating sparse matrix by pool: Pool { address: "AS112Wdy9pM4fvLNLHQXyf7uam9waMPdG5ekr4vxCyQHPkrMMPPY", a_token: Token { address: "AS12FW5Rs5YN2zdpEnqwj4iHUUPt9R4Eqjq2qtpJFNKW3mn33RuLU", name: "Wrapped Massa", symbol: "WMAS", decimals: 9, dex: Dusa }, b_token: Token { address: "AS12N76WPYB3QNYKGhV2jZuQs1djdhNJLQgnm7m52pHWecvvj1fCQ", name: "USD Coin", symbol: "USDC", decimals: 6, dex: Dusa }, a_reserve: 11253.55718658, b_reserve: 5895.446325, dex: Dusa, swap_fee: 0.0, a_price: 0.5206450285625964, b_price: 1.9206944177702283, active_bin_id: 8384824, bin_step: 20, bins: {8384823: Bin { id: 8384823, a_reserve: 0.0, b_reserve: 256.595466 }, 8384824: Bin { id: 8384824, a_reserve: 389.359655734, b_reserve: 54.390487 }, 8384825: Bin { id: 8384825, a_reserve: 493.827160493, b_reserve: 0.0 }} }
2025-07-01T00:11:54.834636Z  INFO quedge::spawns::swap_fetcher: Swap event processed successfully
2025-07-01T00:11:54.834930Z DEBUG quedge::helpers: Detecting arbitrage cycles...
2025-07-01T00:11:54.835146Z DEBUG quedge::helpers: Arbitrage cycles found (3 total):
2025-07-01T00:11:54.835274Z DEBUG quedge::helpers: Cycle path Inisde Simulation Task:
2025-07-01T00:11:54.835376Z DEBUG quedge::helpers::display: Path: ["WMAS-Dusa", "WMAS-EagleFi", "USDC-EagleFi", "USDC-Dusa", "WMAS-Dusa"]
2025-07-01T00:11:54.835524Z DEBUG quedge::helpers: Cycle path Inisde Simulation Task:
2025-07-01T00:11:54.835636Z DEBUG quedge::helpers::display: Path: ["USDC-Dusa", "WMAS-Dusa", "WMAS-EagleFi", "USDC-EagleFi", "USDC-Dusa"]
2025-07-01T00:11:54.835785Z DEBUG quedge::helpers: Cycle path Inisde Simulation Task:
2025-07-01T00:11:54.835867Z  INFO quedge::helpers: Default start amount for token AS12FW5Rs5YN2zdpEnqwj4iHUUPt9R4Eqjq2qtpJFNKW3mn33RuLU is 10
2025-07-01T00:11:54.835951Z DEBUG quedge::helpers::display: Path: ["USDC-EagleFi", "USDC-Dusa", "WMAS-Dusa", "WMAS-EagleFi", "USDC-EagleFi"]
2025-07-01T00:11:54.836040Z  INFO quedge::helpers: Offchain Amount out after Offchain simulation: 11.112272348880026
2025-07-01T00:11:54.836146Z  INFO quedge::helpers: Offchain Earning percentage: 11.122723488800261
2025-07-01T00:11:55.002681Z  INFO quedge::helpers: Amount out after Onchain simulation: 33.150738128
2025-07-01T00:11:55.002822Z  INFO quedge::helpers: Earning percentage: 10.502460426666667
2025-07-01T00:11:55.003026Z  INFO quedge::helpers: Found 1 valid cycles
2025-07-01T00:11:55.003199Z  INFO quedge::executor: Executing arbitrage with start amount 30 for cycle path: ["WMAS-Dusa", "WMAS-EagleFi", "USDC-EagleFi", "USDC-Dusa", "WMAS-Dusa"]...
2025-07-01T00:12:10.410237Z  INFO quedge::spawns::swap_fetcher: Found 2 Swap events from the operation O12VUeebwkXpQv2eZsx4QbsU187h6H5fwQWy4NYK7sYtDL9YEgvj. Processing them...
2025-07-01T00:12:10.410432Z  INFO quedge::spawns::swap_fetcher: New detected swap on pool: AS12dny2u8p6DmcPnYurnzqrAG7kMjYUcSD6bDCdJfbYeadYqXaL5
2025-07-01T00:12:10.422754Z DEBUG quedge::executor: Showing all operation events data for operation id: O12VUeebwkXpQv2eZsx4QbsU187h6H5fwQWy4NYK7sYtDL9YEgvj ...
2025-07-01T00:12:10.422930Z DEBUG quedge::executor: Event Data: "ArbRoutes inside execuwithFlmashLoan: Router Address: AS12JfpytS54Pq2Pi5ZP9dZ8kxe21TmwBpoiQuMNvAgB3bSVsCs9k\nPool Address: AS12dny2u8p6DmcPnYurnzqrAG7kMjYUcSD6bDCdJfbYeadYqXaL5\nToken In Address: AS12FW5Rs5YN2zdpEnqwj4iHUUPt9R4Eqjq2qtpJFNKW3mn33RuLU\nToken Out Address: AS12N76WPYB3QNYKGhV2jZuQs1djdhNJLQgnm7m52pHWecvvj1fCQ\nAmount In: 30000000000\nAmount Out Min: 1\nBin Step: 0\nTo: AS1VJvdvuU5AZFFKWLD2yxyHsS1CE65imQxQUShqNEf4BqZ2ZHLT\nDeadline: 1751329315003\nIs Dusa: false\nCoins To Use: 10000000\nSwap Params: \n,Router Address: AS1XqtvX3rz2RWbnqLfaYVKEjM3VS5pny9yKDdXcmJ5C1vrcLEFd\nPool Address: AS112Wdy9pM4fvLNLHQXyf7uam9waMPdG5ekr4vxCyQHPkrMMPPY\nToken In Address: AS12N76WPYB3QNYKGhV2jZuQs1djdhNJLQgnm7m52pHWecvvj1fCQ\nToken Out Address: AS12FW5Rs5YN2zdpEnqwj4iHUUPt9R4Eqjq2qtpJFNKW3mn33RuLU\nAmount In: 30000000000\nAmount Out Min: 1\nBin Step: 20\nTo: AS1VJvdvuU5AZFFKWLD2yxyHsS1CE65imQxQUShqNEf4BqZ2ZHLT\nDeadline: 1751329315003\nIs Dusa: true\nCoins To Use: 1000000\nSwap Params: \n"
2025-07-01T00:12:10.423066Z DEBUG quedge::executor: Event Data: "TRANSFER SUCCESS"
2025-07-01T00:12:10.423194Z DEBUG quedge::executor: Event Data: "TRANSFER SUCCESS"
2025-07-01T00:12:10.423323Z DEBUG quedge::executor: Event Data: "TRANSFER SUCCESS"
2025-07-01T00:12:10.423435Z DEBUG quedge::executor: Event Data: "TRANSFER SUCCESS"
2025-07-01T00:12:10.423560Z DEBUG quedge::executor: Event Data: "UPDATE_CUMULATIVE_PRICES::AS12dny2u8p6DmcPnYurnzqrAG7kMjYUcSD6bDCdJfbYeadYqXaL5,AS12JfpytS54Pq2Pi5ZP9dZ8kxe21TmwBpoiQuMNvAgB3bSVsCs9k,11572284305532112490000,3549787692668598000,1751328724500"
2025-07-01T00:12:10.423682Z DEBUG quedge::executor: Event Data: "SWAP:AS12dny2u8p6DmcPnYurnzqrAG7kMjYUcSD6bDCdJfbYeadYqXaL5,AS1VJvdvuU5AZFFKWLD2yxyHsS1CE65imQxQUShqNEf4BqZ2ZHLT,30000000000,AS12FW5Rs5YN2zdpEnqwj4iHUUPt9R4Eqjq2qtpJFNKW3mn33RuLU,17294356,AS12N76WPYB3QNYKGhV2jZuQs1djdhNJLQgnm7m52pHWecvvj1fCQ,90000000,22500000,67500000,5556020271648,3195230731"
2025-07-01T00:12:10.423803Z DEBUG quedge::executor: Event Data: "Spent 0 coins"
2025-07-01T00:12:10.424005Z DEBUG quedge::executor: Event Data: "Swap Route Exexcuted: Pool Address: AS12dny2u8p6DmcPnYurnzqrAG7kMjYUcSD6bDCdJfbYeadYqXaL5\nToken In Address: AS12FW5Rs5YN2zdpEnqwj4iHUUPt9R4Eqjq2qtpJFNKW3mn33RuLU\nToken Out Address: AS12N76WPYB3QNYKGhV2jZuQs1djdhNJLQgnm7m52pHWecvvj1fCQ\nReceiver Address: AS1VJvdvuU5AZFFKWLD2yxyHsS1CE65imQxQUShqNEf4BqZ2ZHLT\nAmount In: 30000000000\nMin Amount Out: 1 \nIs Transfer From: true"
2025-07-01T00:12:10.424160Z DEBUG quedge::executor: Event Data: "Spent 0 coins"
2025-07-01T00:12:10.424277Z DEBUG quedge::executor: Event Data: "TRANSFER SUCCESS"
2025-07-01T00:12:10.424428Z DEBUG quedge::executor: Event Data: "SWAP:AS1VJvdvuU5AZFFKWLD2yxyHsS1CE65imQxQUShqNEf4BqZ2ZHLT;?!8384824;?!false;?!峷ć\0\0\0\0\0\0\0\0\0\0\0\0\0\0;?!Ố런\u{7}\0\0\0\0\0\0\0\0\0\0\0\0\0;?!0;?!蜝\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0"
2025-07-01T00:12:10.424551Z DEBUG quedge::executor: Event Data: "TRANSFER SUCCESS"
2025-07-01T00:12:10.424666Z DEBUG quedge::executor: Event Data: "Received coins"
2025-07-01T00:12:10.424805Z DEBUG quedge::executor: Event Data: "TRANSFER SUCCESS"
2025-07-01T00:12:10.424973Z DEBUG quedge::executor: Event Data: "TRANSFER SUCCESS"
2025-07-01T00:12:10.425105Z DEBUG quedge::executor: Event Data: "FLASH_ARB_EXEC:33150738128"
2025-07-01T00:12:10.425289Z DEBUG quedge::executor: Event Data: "TRANSFER SUCCESS"
2025-07-01T00:12:10.425416Z DEBUG quedge::executor: Event Data: "TRANSFER SUCCESS"
2025-07-01T00:12:10.425519Z DEBUG quedge::executor: Event Data: "UPDATE_CUMULATIVE_PRICES::AS1fewbVp4FMTEJH81e1BNaH9B3SmeE7sNpZQ2QqwP1wGBzLKJGF,AS1VJvdvuU5AZFFKWLD2yxyHsS1CE65imQxQUShqNEf4BqZ2ZHLT,675439294695611052977000,15012899793336582201066138606712000,1751328724500"
2025-07-01T00:12:10.425648Z DEBUG quedge::executor: Event Data: "Spent 0 coins"
2025-07-01T00:12:10.425764Z DEBUG quedge::executor: Event Data: "FLASH_LOAN:AS1fewbVp4FMTEJH81e1BNaH9B3SmeE7sNpZQ2QqwP1wGBzLKJGF,AS1VJvdvuU5AZFFKWLD2yxyHsS1CE65imQxQUShqNEf4BqZ2ZHLT,AS1VJvdvuU5AZFFKWLD2yxyHsS1CE65imQxQUShqNEf4BqZ2ZHLT,0,30000000000,12817214505974134383413567,674627845226602,1000"
2025-07-01T00:12:10.425927Z ERROR quedge::spawns::swap_fetcher: Error in detect_and_execute_arb: Failed to parse amount out to U256
2025-07-01T00:12:10.576350Z  INFO quedge::strategies::bellman_ford_matrix: Updating sparse matrix by pool: Pool { address: "AS12dny2u8p6DmcPnYurnzqrAG7kMjYUcSD6bDCdJfbYeadYqXaL5", a_token: Token { address: "AS12N76WPYB3QNYKGhV2jZuQs1djdhNJLQgnm7m52pHWecvvj1fCQ", name: "USD Coin", symbol: "USDC", decimals: 6, dex: EagleFi }, b_token: Token { address: "AS12FW5Rs5YN2zdpEnqwj4iHUUPt9R4Eqjq2qtpJFNKW3mn33RuLU", name: "Wrapped Massa", symbol: "WMAS", decimals: 9, dex: EagleFi }, a_reserve: 3195.230731, b_reserve: 5556.020271648, dex: EagleFi, swap_fee: 0.3, a_price: 1.738847907834547, b_price: 0.5750934256494795, active_bin_id: 0, bin_step: 0, bins: {} }
2025-07-01T00:12:10.576514Z  WARN quedge::spawns::swap_fetcher: Skipping arbitrage detection after updating the sparse matrix because of an order from the events stream
2025-07-01T00:12:10.576646Z  INFO quedge::spawns::swap_fetcher: Swap event processed successfully
2025-07-01T00:12:10.576768Z DEBUG quedge::spawns::swap_fetcher: Processing last event of the operation O12VUeebwkXpQv2eZsx4QbsU187h6H5fwQWy4NYK7sYtDL9YEgvj...
2025-07-01T00:12:10.576899Z  WARN quedge::spawns::swap_fetcher: Pool AS1VJvdvuU5AZFFKWLD2yxyHsS1CE65imQxQUShqNEf4BqZ2ZHLT not found in the pools hash map
2025-07-01T00:12:10.577029Z  WARN quedge::spawns::swap_fetcher: Pool AS1fewbVp4FMTEJH81e1BNaH9B3SmeE7sNpZQ2QqwP1wGBzLKJGF not found in the pools hash map
2025-07-01T00:12:10.577151Z  WARN quedge::spawns::swap_fetcher: Pool AS1VJvdvuU5AZFFKWLD2yxyHsS1CE65imQxQUShqNEf4BqZ2ZHLT not found in the pools hash map
2025-07-01T00:12:10.577277Z  WARN quedge::spawns::swap_fetcher: Pool AS1XqtvX3rz2RWbnqLfaYVKEjM3VS5pny9yKDdXcmJ5C1vrcLEFd not found in the pools hash map
2025-07-01T00:12:10.577395Z  INFO quedge::spawns::swap_fetcher: New detected swap on pool: AS112Wdy9pM4fvLNLHQXyf7uam9waMPdG5ekr4vxCyQHPkrMMPPY
2025-07-01T00:12:10.880219Z  INFO quedge::strategies::bellman_ford_matrix: Updating sparse matrix by pool: Pool { address: "AS112Wdy9pM4fvLNLHQXyf7uam9waMPdG5ekr4vxCyQHPkrMMPPY", a_token: Token { address: "AS12FW5Rs5YN2zdpEnqwj4iHUUPt9R4Eqjq2qtpJFNKW3mn33RuLU", name: "Wrapped Massa", symbol: "WMAS", decimals: 9, dex: Dusa }, b_token: Token { address: "AS12N76WPYB3QNYKGhV2jZuQs1djdhNJLQgnm7m52pHWecvvj1fCQ", name: "USD Coin", symbol: "USDC", decimals: 6, dex: Dusa }, a_reserve: 11220.406448452, b_reserve: 5912.706092, dex: Dusa, swap_fee: 0.0, a_price: 0.5206450285625964, b_price: 1.9206944177702283, active_bin_id: 8384824, bin_step: 20, bins: {8384823: Bin { id: 8384823, a_reserve: 0.0, b_reserve: 256.595466 }, 8384824: Bin { id: 8384824, a_reserve: 356.208917606, b_reserve: 71.650254 }, 8384825: Bin { id: 8384825, a_reserve: 493.827160493, b_reserve: 0.0 }} }
2025-07-01T00:12:10.880438Z  INFO quedge::spawns::swap_fetcher: Swap event processed successfully
2025-07-01T00:12:10.880654Z DEBUG quedge::helpers: Detecting arbitrage cycles...
2025-07-01T00:12:10.880859Z DEBUG quedge::helpers: Arbitrage cycles found (3 total):
2025-07-01T00:12:10.880986Z DEBUG quedge::helpers: Cycle path Inisde Simulation Task:
2025-07-01T00:12:10.881090Z DEBUG quedge::helpers::display: Path: ["WMAS-Dusa", "WMAS-EagleFi", "USDC-EagleFi", "USDC-Dusa", "WMAS-Dusa"]
2025-07-01T00:12:10.881234Z DEBUG quedge::helpers: Cycle path Inisde Simulation Task:
2025-07-01T00:12:10.881345Z  INFO quedge::helpers: Default start amount for token AS12FW5Rs5YN2zdpEnqwj4iHUUPt9R4Eqjq2qtpJFNKW3mn33RuLU is 10
2025-07-01T00:12:10.881437Z DEBUG quedge::helpers::display: Path: ["USDC-Dusa", "WMAS-Dusa", "WMAS-EagleFi", "USDC-EagleFi", "USDC-Dusa"]
2025-07-01T00:12:10.881553Z  INFO quedge::helpers: Offchain Amount out after Offchain simulation: 10.99292371063091
2025-07-01T00:12:10.881639Z DEBUG quedge::helpers: Cycle path Inisde Simulation Task:
2025-07-01T00:12:10.881736Z  INFO quedge::helpers: Offchain Earning percentage: 9.929237106309099
2025-07-01T00:12:10.881832Z DEBUG quedge::helpers::display: Path: ["USDC-EagleFi", "USDC-Dusa", "WMAS-Dusa", "WMAS-EagleFi", "USDC-EagleFi"]
2025-07-01T00:12:11.103259Z  INFO quedge::helpers: Amount out after Onchain simulation: 32.79532323
2025-07-01T00:12:11.103396Z  INFO quedge::helpers: Earning percentage: 9.317744100000004
2025-07-01T00:12:11.103590Z  INFO quedge::helpers: Found 1 valid cycles
2025-07-01T00:12:11.103755Z  INFO quedge::executor: Executing arbitrage with start amount 30 for cycle path: ["WMAS-Dusa", "WMAS-EagleFi", "USDC-EagleFi", "USDC-Dusa", "WMAS-Dusa"]...
2025-07-01T00:12:26.372639Z  INFO quedge::spawns::swap_fetcher: Found 2 Swap events from the operation O12sJEh2WJdsBZT9YYaui1MsacQrHHs14Z1qXohJganj3X3pbipv. Processing them...
2025-07-01T00:12:26.372808Z  INFO quedge::spawns::swap_fetcher: New detected swap on pool: AS12dny2u8p6DmcPnYurnzqrAG7kMjYUcSD6bDCdJfbYeadYqXaL5
2025-07-01T00:12:26.529961Z  INFO quedge::strategies::bellman_ford_matrix: Updating sparse matrix by pool: Pool { address: "AS12dny2u8p6DmcPnYurnzqrAG7kMjYUcSD6bDCdJfbYeadYqXaL5", a_token: Token { address: "AS12N76WPYB3QNYKGhV2jZuQs1djdhNJLQgnm7m52pHWecvvj1fCQ", name: "USD Coin", symbol: "USDC", decimals: 6, dex: EagleFi }, b_token: Token { address: "AS12FW5Rs5YN2zdpEnqwj4iHUUPt9R4Eqjq2qtpJFNKW3mn33RuLU", name: "Wrapped Massa", symbol: "WMAS", decimals: 9, dex: EagleFi }, a_reserve: 3178.121791, b_reserve: 5585.997771648, dex: EagleFi, swap_fee: 0.3, a_price: 1.7576411915574697, b_price: 0.5689443356262528, active_bin_id: 0, bin_step: 0, bins: {} }
2025-07-01T00:12:26.530138Z  WARN quedge::spawns::swap_fetcher: Skipping arbitrage detection after updating the sparse matrix because of an order from the events stream
2025-07-01T00:12:26.530277Z  INFO quedge::spawns::swap_fetcher: Swap event processed successfully
2025-07-01T00:12:26.530403Z DEBUG quedge::spawns::swap_fetcher: Processing last event of the operation O12sJEh2WJdsBZT9YYaui1MsacQrHHs14Z1qXohJganj3X3pbipv...
2025-07-01T00:12:26.530515Z  WARN quedge::spawns::swap_fetcher: Pool AS1VJvdvuU5AZFFKWLD2yxyHsS1CE65imQxQUShqNEf4BqZ2ZHLT not found in the pools hash map
2025-07-01T00:12:26.530636Z  WARN quedge::spawns::swap_fetcher: Pool AS1fewbVp4FMTEJH81e1BNaH9B3SmeE7sNpZQ2QqwP1wGBzLKJGF not found in the pools hash map
2025-07-01T00:12:26.530769Z  WARN quedge::spawns::swap_fetcher: Pool AS1VJvdvuU5AZFFKWLD2yxyHsS1CE65imQxQUShqNEf4BqZ2ZHLT not found in the pools hash map
2025-07-01T00:12:26.530905Z  WARN quedge::spawns::swap_fetcher: Pool AS1XqtvX3rz2RWbnqLfaYVKEjM3VS5pny9yKDdXcmJ5C1vrcLEFd not found in the pools hash map
2025-07-01T00:12:26.531032Z  INFO quedge::spawns::swap_fetcher: New detected swap on pool: AS112Wdy9pM4fvLNLHQXyf7uam9waMPdG5ekr4vxCyQHPkrMMPPY
2025-07-01T00:12:26.564396Z DEBUG quedge::executor: Showing all operation events data for operation id: O12sJEh2WJdsBZT9YYaui1MsacQrHHs14Z1qXohJganj3X3pbipv ...
2025-07-01T00:12:26.564598Z DEBUG quedge::executor: Event Data: "ArbRoutes inside execuwithFlmashLoan: Router Address: AS12JfpytS54Pq2Pi5ZP9dZ8kxe21TmwBpoiQuMNvAgB3bSVsCs9k\nPool Address: AS12dny2u8p6DmcPnYurnzqrAG7kMjYUcSD6bDCdJfbYeadYqXaL5\nToken In Address: AS12FW5Rs5YN2zdpEnqwj4iHUUPt9R4Eqjq2qtpJFNKW3mn33RuLU\nToken Out Address: AS12N76WPYB3QNYKGhV2jZuQs1djdhNJLQgnm7m52pHWecvvj1fCQ\nAmount In: 30000000000\nAmount Out Min: 1\nBin Step: 0\nTo: AS1VJvdvuU5AZFFKWLD2yxyHsS1CE65imQxQUShqNEf4BqZ2ZHLT\nDeadline: 1751329331103\nIs Dusa: false\nCoins To Use: 10000000\nSwap Params: \n,Router Address: AS1XqtvX3rz2RWbnqLfaYVKEjM3VS5pny9yKDdXcmJ5C1vrcLEFd\nPool Address: AS112Wdy9pM4fvLNLHQXyf7uam9waMPdG5ekr4vxCyQHPkrMMPPY\nToken In Address: AS12N76WPYB3QNYKGhV2jZuQs1djdhNJLQgnm7m52pHWecvvj1fCQ\nToken Out Address: AS12FW5Rs5YN2zdpEnqwj4iHUUPt9R4Eqjq2qtpJFNKW3mn33RuLU\nAmount In: 30000000000\nAmount Out Min: 1\nBin Step: 20\nTo: AS1VJvdvuU5AZFFKWLD2yxyHsS1CE65imQxQUShqNEf4BqZ2ZHLT\nDeadline: 1751329331103\nIs Dusa: true\nCoins To Use: 1000000\nSwap Params: \n"
2025-07-01T00:12:26.564757Z DEBUG quedge::executor: Event Data: "TRANSFER SUCCESS"
2025-07-01T00:12:26.564893Z DEBUG quedge::executor: Event Data: "TRANSFER SUCCESS"
2025-07-01T00:12:26.565052Z DEBUG quedge::executor: Event Data: "TRANSFER SUCCESS"
2025-07-01T00:12:26.565179Z DEBUG quedge::executor: Event Data: "TRANSFER SUCCESS"
2025-07-01T00:12:26.565447Z DEBUG quedge::executor: Event Data: "UPDATE_CUMULATIVE_PRICES::AS12dny2u8p6DmcPnYurnzqrAG7kMjYUcSD6bDCdJfbYeadYqXaL5,AS12JfpytS54Pq2Pi5ZP9dZ8kxe21TmwBpoiQuMNvAgB3bSVsCs9k,11572373681496458858000,3549838542617254000,1751328740500"
2025-07-01T00:12:26.565611Z DEBUG quedge::executor: Event Data: "SWAP:AS12dny2u8p6DmcPnYurnzqrAG7kMjYUcSD6bDCdJfbYeadYqXaL5,AS1VJvdvuU5AZFFKWLD2yxyHsS1CE65imQxQUShqNEf4BqZ2ZHLT,30000000000,AS12FW5Rs5YN2zdpEnqwj4iHUUPt9R4Eqjq2qtpJFNKW3mn33RuLU,17108940,AS12N76WPYB3QNYKGhV2jZuQs1djdhNJLQgnm7m52pHWecvvj1fCQ,90000000,22500000,67500000,5585997771648,3178121791"
2025-07-01T00:12:26.565718Z DEBUG quedge::executor: Event Data: "Spent 0 coins"
2025-07-01T00:12:26.565807Z DEBUG quedge::executor: Event Data: "Swap Route Exexcuted: Pool Address: AS12dny2u8p6DmcPnYurnzqrAG7kMjYUcSD6bDCdJfbYeadYqXaL5\nToken In Address: AS12FW5Rs5YN2zdpEnqwj4iHUUPt9R4Eqjq2qtpJFNKW3mn33RuLU\nToken Out Address: AS12N76WPYB3QNYKGhV2jZuQs1djdhNJLQgnm7m52pHWecvvj1fCQ\nReceiver Address: AS1VJvdvuU5AZFFKWLD2yxyHsS1CE65imQxQUShqNEf4BqZ2ZHLT\nAmount In: 30000000000\nMin Amount Out: 1 \nIs Transfer From: true"
2025-07-01T00:12:26.565958Z DEBUG quedge::executor: Event Data: "Spent 0 coins"
2025-07-01T00:12:26.566137Z DEBUG quedge::executor: Event Data: "TRANSFER SUCCESS"
2025-07-01T00:12:26.566392Z DEBUG quedge::executor: Event Data: "SWAP:AS1VJvdvuU5AZFFKWLD2yxyHsS1CE65imQxQUShqNEf4BqZ2ZHLT;?!8384824;?!false;?!訢Ą\0\0\0\0\0\0\0\0\0\0\0\0\0\0;?!\u{eb5e}ꋀ\u{7}\0\0\0\0\0\0\0\0\0\0\0\0\0;?!0;?!薪\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0"
2025-07-01T00:12:26.566527Z DEBUG quedge::executor: Event Data: "TRANSFER SUCCESS"
2025-07-01T00:12:26.566654Z DEBUG quedge::executor: Event Data: "Received coins"
2025-07-01T00:12:26.566807Z DEBUG quedge::executor: Event Data: "TRANSFER SUCCESS"
2025-07-01T00:12:26.566931Z DEBUG quedge::executor: Event Data: "TRANSFER SUCCESS"
2025-07-01T00:12:26.567049Z DEBUG quedge::executor: Event Data: "FLASH_ARB_EXEC:32795323230"
2025-07-01T00:12:26.567174Z DEBUG quedge::executor: Event Data: "TRANSFER SUCCESS"
2025-07-01T00:12:26.567313Z DEBUG quedge::executor: Event Data: "TRANSFER SUCCESS"
2025-07-01T00:12:26.567449Z DEBUG quedge::executor: Event Data: "UPDATE_CUMULATIVE_PRICES::AS1fewbVp4FMTEJH81e1BNaH9B3SmeE7sNpZQ2QqwP1wGBzLKJGF,AS1VJvdvuU5AZFFKWLD2yxyHsS1CE65imQxQUShqNEf4BqZ2ZHLT,675450076068215494689000,15013104793168064923915794629432000,1751328740500"
2025-07-01T00:12:26.567592Z DEBUG quedge::executor: Event Data: "Spent 0 coins"
2025-07-01T00:12:26.567706Z DEBUG quedge::executor: Event Data: "FLASH_LOAN:AS1fewbVp4FMTEJH81e1BNaH9B3SmeE7sNpZQ2QqwP1wGBzLKJGF,AS1VJvdvuU5AZFFKWLD2yxyHsS1CE65imQxQUShqNEf4BqZ2ZHLT,AS1VJvdvuU5AZFFKWLD2yxyHsS1CE65imQxQUShqNEf4BqZ2ZHLT,0,30000000000,12817214505974134383413567,674627845226602,1000"
2025-07-01T00:12:26.567898Z ERROR quedge::spawns::swap_fetcher: Error in detect_and_execute_arb: Failed to parse amount out to U256
2025-07-01T00:12:26.873567Z  INFO quedge::strategies::bellman_ford_matrix: Updating sparse matrix by pool: Pool { address: "AS112Wdy9pM4fvLNLHQXyf7uam9waMPdG5ekr4vxCyQHPkrMMPPY", a_token: Token { address: "AS12FW5Rs5YN2zdpEnqwj4iHUUPt9R4Eqjq2qtpJFNKW3mn33RuLU", name: "Wrapped Massa", symbol: "WMAS", decimals: 9, dex: Dusa }, b_token: Token { address: "AS12N76WPYB3QNYKGhV2jZuQs1djdhNJLQgnm7m52pHWecvvj1fCQ", name: "USD Coin", symbol: "USDC", decimals: 6, dex: Dusa }, a_reserve: 11187.611125222, b_reserve: 5929.780814, dex: Dusa, swap_fee: 0.0, a_price: 0.5206450285625964, b_price: 1.9206944177702283, active_bin_id: 8384824, bin_step: 20, bins: {8384823: Bin { id: 8384823, a_reserve: 0.0, b_reserve: 256.595466 }, 8384824: Bin { id: 8384824, a_reserve: 323.413594376, b_reserve: 88.724976 }, 8384825: Bin { id: 8384825, a_reserve: 493.827160493, b_reserve: 0.0 }} }
2025-07-01T00:12:26.873801Z  INFO quedge::spawns::swap_fetcher: Swap event processed successfully
2025-07-01T00:12:26.874068Z DEBUG quedge::helpers: Detecting arbitrage cycles...
2025-07-01T00:12:26.874298Z DEBUG quedge::helpers: Arbitrage cycles found (3 total):
2025-07-01T00:12:26.874455Z DEBUG quedge::helpers: Cycle path Inisde Simulation Task:
2025-07-01T00:12:26.874589Z DEBUG quedge::helpers::display: Path: ["WMAS-Dusa", "WMAS-EagleFi", "USDC-EagleFi", "USDC-Dusa", "WMAS-Dusa"]
2025-07-01T00:12:26.874761Z DEBUG quedge::helpers: Cycle path Inisde Simulation Task:
2025-07-01T00:12:26.874862Z  INFO quedge::helpers: Default start amount for token AS12FW5Rs5YN2zdpEnqwj4iHUUPt9R4Eqjq2qtpJFNKW3mn33RuLU is 10
2025-07-01T00:12:26.874963Z DEBUG quedge::helpers::display: Path: ["USDC-Dusa", "WMAS-Dusa", "WMAS-EagleFi", "USDC-EagleFi", "USDC-Dusa"]
2025-07-01T00:12:26.875091Z  INFO quedge::helpers: Offchain Amount out after Offchain simulation: 10.875488260409401
2025-07-01T00:12:26.875186Z DEBUG quedge::helpers: Cycle path Inisde Simulation Task:
2025-07-01T00:12:26.875290Z  INFO quedge::helpers: Offchain Earning percentage: 8.754882604094014
2025-07-01T00:12:26.875384Z DEBUG quedge::helpers::display: Path: ["USDC-EagleFi", "USDC-Dusa", "WMAS-Dusa", "WMAS-EagleFi", "USDC-EagleFi"]
2025-07-01T00:12:27.042212Z  INFO quedge::helpers: Amount out after Onchain simulation: 32.445597428
2025-07-01T00:12:27.042368Z  INFO quedge::helpers: Earning percentage: 8.151991426666665
2025-07-01T00:12:27.042567Z  INFO quedge::helpers: Found 1 valid cycles
2025-07-01T00:12:27.042734Z  INFO quedge::executor: Executing arbitrage with start amount 30 for cycle path: ["WMAS-Dusa", "WMAS-EagleFi", "USDC-EagleFi", "USDC-Dusa", "WMAS-Dusa"]...
2025-07-01T00:12:42.375424Z  INFO quedge::spawns::swap_fetcher: Found 2 Swap events from the operation O1miiCtDgXZeUbYh4ZiYXFaECNj9r1id1TnUSibEvFfD8rES6N5. Processing them...
2025-07-01T00:12:42.375610Z  INFO quedge::spawns::swap_fetcher: New detected swap on pool: AS12dny2u8p6DmcPnYurnzqrAG7kMjYUcSD6bDCdJfbYeadYqXaL5
2025-07-01T00:12:42.534927Z  INFO quedge::strategies::bellman_ford_matrix: Updating sparse matrix by pool: Pool { address: "AS12dny2u8p6DmcPnYurnzqrAG7kMjYUcSD6bDCdJfbYeadYqXaL5", a_token: Token { address: "AS12N76WPYB3QNYKGhV2jZuQs1djdhNJLQgnm7m52pHWecvvj1fCQ", name: "USD Coin", symbol: "USDC", decimals: 6, dex: EagleFi }, b_token: Token { address: "AS12FW5Rs5YN2zdpEnqwj4iHUUPt9R4Eqjq2qtpJFNKW3mn33RuLU", name: "Wrapped Massa", symbol: "WMAS", decimals: 9, dex: EagleFi }, a_reserve: 3161.195299, b_reserve: 5615.975271648, dex: EagleFi, swap_fee: 0.3, a_price: 1.776535373636844, b_price: 0.5628933793492918, active_bin_id: 0, bin_step: 0, bins: {} }
2025-07-01T00:12:42.535129Z  WARN quedge::spawns::swap_fetcher: Skipping arbitrage detection after updating the sparse matrix because of an order from the events stream
2025-07-01T00:12:42.535284Z  INFO quedge::spawns::swap_fetcher: Swap event processed successfully
2025-07-01T00:12:42.535446Z DEBUG quedge::spawns::swap_fetcher: Processing last event of the operation O1miiCtDgXZeUbYh4ZiYXFaECNj9r1id1TnUSibEvFfD8rES6N5...
2025-07-01T00:12:42.535588Z  WARN quedge::spawns::swap_fetcher: Pool AS1VJvdvuU5AZFFKWLD2yxyHsS1CE65imQxQUShqNEf4BqZ2ZHLT not found in the pools hash map
2025-07-01T00:12:42.535723Z  WARN quedge::spawns::swap_fetcher: Pool AS1fewbVp4FMTEJH81e1BNaH9B3SmeE7sNpZQ2QqwP1wGBzLKJGF not found in the pools hash map
2025-07-01T00:12:42.535846Z  WARN quedge::spawns::swap_fetcher: Pool AS1VJvdvuU5AZFFKWLD2yxyHsS1CE65imQxQUShqNEf4BqZ2ZHLT not found in the pools hash map
2025-07-01T00:12:42.535969Z  WARN quedge::spawns::swap_fetcher: Pool AS1XqtvX3rz2RWbnqLfaYVKEjM3VS5pny9yKDdXcmJ5C1vrcLEFd not found in the pools hash map
2025-07-01T00:12:42.536078Z  INFO quedge::spawns::swap_fetcher: New detected swap on pool: AS112Wdy9pM4fvLNLHQXyf7uam9waMPdG5ekr4vxCyQHPkrMMPPY
2025-07-01T00:12:42.543304Z DEBUG quedge::executor: Showing all operation events data for operation id: O1miiCtDgXZeUbYh4ZiYXFaECNj9r1id1TnUSibEvFfD8rES6N5 ...
2025-07-01T00:12:42.543437Z DEBUG quedge::executor: Event Data: "ArbRoutes inside execuwithFlmashLoan: Router Address: AS12JfpytS54Pq2Pi5ZP9dZ8kxe21TmwBpoiQuMNvAgB3bSVsCs9k\nPool Address: AS12dny2u8p6DmcPnYurnzqrAG7kMjYUcSD6bDCdJfbYeadYqXaL5\nToken In Address: AS12FW5Rs5YN2zdpEnqwj4iHUUPt9R4Eqjq2qtpJFNKW3mn33RuLU\nToken Out Address: AS12N76WPYB3QNYKGhV2jZuQs1djdhNJLQgnm7m52pHWecvvj1fCQ\nAmount In: 30000000000\nAmount Out Min: 1\nBin Step: 0\nTo: AS1VJvdvuU5AZFFKWLD2yxyHsS1CE65imQxQUShqNEf4BqZ2ZHLT\nDeadline: 1751329347042\nIs Dusa: false\nCoins To Use: 10000000\nSwap Params: \n,Router Address: AS1XqtvX3rz2RWbnqLfaYVKEjM3VS5pny9yKDdXcmJ5C1vrcLEFd\nPool Address: AS112Wdy9pM4fvLNLHQXyf7uam9waMPdG5ekr4vxCyQHPkrMMPPY\nToken In Address: AS12N76WPYB3QNYKGhV2jZuQs1djdhNJLQgnm7m52pHWecvvj1fCQ\nToken Out Address: AS12FW5Rs5YN2zdpEnqwj4iHUUPt9R4Eqjq2qtpJFNKW3mn33RuLU\nAmount In: 30000000000\nAmount Out Min: 1\nBin Step: 20\nTo: AS1VJvdvuU5AZFFKWLD2yxyHsS1CE65imQxQUShqNEf4BqZ2ZHLT\nDeadline: 1751329347042\nIs Dusa: true\nCoins To Use: 1000000\nSwap Params: \n"
2025-07-01T00:12:42.543543Z DEBUG quedge::executor: Event Data: "TRANSFER SUCCESS"
2025-07-01T00:12:42.543656Z DEBUG quedge::executor: Event Data: "TRANSFER SUCCESS"
2025-07-01T00:12:42.543804Z DEBUG quedge::executor: Event Data: "TRANSFER SUCCESS"
2025-07-01T00:12:42.543910Z DEBUG quedge::executor: Event Data: "TRANSFER SUCCESS"
2025-07-01T00:12:42.544015Z DEBUG quedge::executor: Event Data: "UPDATE_CUMULATIVE_PRICES::AS12dny2u8p6DmcPnYurnzqrAG7kMjYUcSD6bDCdJfbYeadYqXaL5,AS12JfpytS54Pq2Pi5ZP9dZ8kxe21TmwBpoiQuMNvAgB3bSVsCs9k,11572463537100805226000,3549889121742038000,1751328756500"
2025-07-01T00:12:42.544118Z DEBUG quedge::executor: Event Data: "SWAP:AS12dny2u8p6DmcPnYurnzqrAG7kMjYUcSD6bDCdJfbYeadYqXaL5,AS1VJvdvuU5AZFFKWLD2yxyHsS1CE65imQxQUShqNEf4BqZ2ZHLT,30000000000,AS12FW5Rs5YN2zdpEnqwj4iHUUPt9R4Eqjq2qtpJFNKW3mn33RuLU,16926492,AS12N76WPYB3QNYKGhV2jZuQs1djdhNJLQgnm7m52pHWecvvj1fCQ,90000000,22500000,67500000,5615975271648,3161195299"
2025-07-01T00:12:42.544220Z DEBUG quedge::executor: Event Data: "Spent 0 coins"
2025-07-01T00:12:42.544325Z DEBUG quedge::executor: Event Data: "Swap Route Exexcuted: Pool Address: AS12dny2u8p6DmcPnYurnzqrAG7kMjYUcSD6bDCdJfbYeadYqXaL5\nToken In Address: AS12FW5Rs5YN2zdpEnqwj4iHUUPt9R4Eqjq2qtpJFNKW3mn33RuLU\nToken Out Address: AS12N76WPYB3QNYKGhV2jZuQs1djdhNJLQgnm7m52pHWecvvj1fCQ\nReceiver Address: AS1VJvdvuU5AZFFKWLD2yxyHsS1CE65imQxQUShqNEf4BqZ2ZHLT\nAmount In: 30000000000\nMin Amount Out: 1 \nIs Transfer From: true"
2025-07-01T00:12:42.544428Z DEBUG quedge::executor: Event Data: "Spent 0 coins"
2025-07-01T00:12:42.544528Z DEBUG quedge::executor: Event Data: "TRANSFER SUCCESS"
2025-07-01T00:12:42.544639Z DEBUG quedge::executor: Event Data: "SWAP:AS1VJvdvuU5AZFFKWLD2yxyHsS1CE65imQxQUShqNEf4BqZ2ZHLT;?!8384824;?!false;?!싟ā\0\0\0\0\0\0\0\0\0\0\0\0\0\0;?!蛴跨\u{7}\0\0\0\0\0\0\0\0\0\0\0\0\0;?!0;?!落\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0"
2025-07-01T00:12:42.544767Z DEBUG quedge::executor: Event Data: "TRANSFER SUCCESS"
2025-07-01T00:12:42.544889Z DEBUG quedge::executor: Event Data: "Received coins"
2025-07-01T00:12:42.544988Z DEBUG quedge::executor: Event Data: "TRANSFER SUCCESS"
2025-07-01T00:12:42.545095Z DEBUG quedge::executor: Event Data: "TRANSFER SUCCESS"
2025-07-01T00:12:42.545206Z DEBUG quedge::executor: Event Data: "FLASH_ARB_EXEC:32445597428"
2025-07-01T00:12:42.545325Z DEBUG quedge::executor: Event Data: "TRANSFER SUCCESS"
2025-07-01T00:12:42.545458Z DEBUG quedge::executor: Event Data: "TRANSFER SUCCESS"
2025-07-01T00:12:42.545568Z DEBUG quedge::executor: Event Data: "UPDATE_CUMULATIVE_PRICES::AS1fewbVp4FMTEJH81e1BNaH9B3SmeE7sNpZQ2QqwP1wGBzLKJGF,AS1VJvdvuU5AZFFKWLD2yxyHsS1CE65imQxQUShqNEf4BqZ2ZHLT,675460857440819936401000,15013309792999547646765450652152000,1751328756500"
2025-07-01T00:12:42.545660Z DEBUG quedge::executor: Event Data: "Spent 0 coins"
2025-07-01T00:12:42.545751Z DEBUG quedge::executor: Event Data: "FLASH_LOAN:AS1fewbVp4FMTEJH81e1BNaH9B3SmeE7sNpZQ2QqwP1wGBzLKJGF,AS1VJvdvuU5AZFFKWLD2yxyHsS1CE65imQxQUShqNEf4BqZ2ZHLT,AS1VJvdvuU5AZFFKWLD2yxyHsS1CE65imQxQUShqNEf4BqZ2ZHLT,0,30000000000,12817214505974134383413567,674627845226602,1000"
2025-07-01T00:12:42.545894Z ERROR quedge::spawns::swap_fetcher: Error in detect_and_execute_arb: Failed to parse amount out to U256
2025-07-01T00:12:42.848224Z  INFO quedge::strategies::bellman_ford_matrix: Updating sparse matrix by pool: Pool { address: "AS112Wdy9pM4fvLNLHQXyf7uam9waMPdG5ekr4vxCyQHPkrMMPPY", a_token: Token { address: "AS12FW5Rs5YN2zdpEnqwj4iHUUPt9R4Eqjq2qtpJFNKW3mn33RuLU", name: "Wrapped Massa", symbol: "WMAS", decimals: 9, dex: Dusa }, b_token: Token { address: "AS12N76WPYB3QNYKGhV2jZuQs1djdhNJLQgnm7m52pHWecvvj1fCQ", name: "USD Coin", symbol: "USDC", decimals: 6, dex: Dusa }, a_reserve: 11155.165527794, b_reserve: 5946.673453, dex: Dusa, swap_fee: 0.0, a_price: 0.5206450285625964, b_price: 1.9206944177702283, active_bin_id: 8384824, bin_step: 20, bins: {8384823: Bin { id: 8384823, a_reserve: 0.0, b_reserve: 256.595466 }, 8384824: Bin { id: 8384824, a_reserve: 290.967996948, b_reserve: 105.617615 }, 8384825: Bin { id: 8384825, a_reserve: 493.827160493, b_reserve: 0.0 }} }
2025-07-01T00:12:42.848483Z  INFO quedge::spawns::swap_fetcher: Swap event processed successfully
2025-07-01T00:12:42.848743Z DEBUG quedge::helpers: Detecting arbitrage cycles...
2025-07-01T00:12:42.848965Z DEBUG quedge::helpers: Arbitrage cycles found (3 total):
2025-07-01T00:12:42.849129Z DEBUG quedge::helpers: Cycle path Inisde Simulation Task:
2025-07-01T00:12:42.849284Z DEBUG quedge::helpers::display: Path: ["WMAS-Dusa", "WMAS-EagleFi", "USDC-EagleFi", "USDC-Dusa", "WMAS-Dusa"]
2025-07-01T00:12:42.849413Z DEBUG quedge::helpers: Cycle path Inisde Simulation Task:
2025-07-01T00:12:42.849512Z DEBUG quedge::helpers::display: Path: ["USDC-Dusa", "WMAS-Dusa", "WMAS-EagleFi", "USDC-EagleFi", "USDC-Dusa"]
2025-07-01T00:12:42.849644Z DEBUG quedge::helpers: Cycle path Inisde Simulation Task:
2025-07-01T00:12:42.849739Z  INFO quedge::helpers: Default start amount for token AS12FW5Rs5YN2zdpEnqwj4iHUUPt9R4Eqjq2qtpJFNKW3mn33RuLU is 10
2025-07-01T00:12:42.849848Z DEBUG quedge::helpers::display: Path: ["USDC-EagleFi", "USDC-Dusa", "WMAS-Dusa", "WMAS-EagleFi", "USDC-EagleFi"]
2025-07-01T00:12:42.849952Z  INFO quedge::helpers: Offchain Amount out after Offchain simulation: 10.759925312968322
2025-07-01T00:12:42.850059Z  INFO quedge::helpers: Offchain Earning percentage: 7.599253129683222
2025-07-01T00:12:43.004205Z  INFO quedge::helpers: Amount out after Onchain simulation: 32.101437799
2025-07-01T00:12:43.004348Z  INFO quedge::helpers: Earning percentage: 7.004792663333342
2025-07-01T00:12:43.004529Z  INFO quedge::helpers: Found 1 valid cycles
2025-07-01T00:12:43.004714Z  INFO quedge::executor: Executing arbitrage with start amount 30 for cycle path: ["WMAS-Dusa", "WMAS-EagleFi", "USDC-EagleFi", "USDC-Dusa", "WMAS-Dusa"]...
2025-07-01T00:12:58.385037Z  INFO quedge::spawns::swap_fetcher: Found 2 Swap events from the operation O11Mk34hyWYQFQgkfqk99xBY6MeZSpPC387ju4JMSSpJLRQX1J1. Processing them...
2025-07-01T00:12:58.385229Z  INFO quedge::spawns::swap_fetcher: New detected swap on pool: AS12dny2u8p6DmcPnYurnzqrAG7kMjYUcSD6bDCdJfbYeadYqXaL5
2025-07-01T00:12:58.539888Z DEBUG quedge::executor: Showing all operation events data for operation id: O11Mk34hyWYQFQgkfqk99xBY6MeZSpPC387ju4JMSSpJLRQX1J1 ...
2025-07-01T00:12:58.540032Z DEBUG quedge::executor: Event Data: "ArbRoutes inside execuwithFlmashLoan: Router Address: AS12JfpytS54Pq2Pi5ZP9dZ8kxe21TmwBpoiQuMNvAgB3bSVsCs9k\nPool Address: AS12dny2u8p6DmcPnYurnzqrAG7kMjYUcSD6bDCdJfbYeadYqXaL5\nToken In Address: AS12FW5Rs5YN2zdpEnqwj4iHUUPt9R4Eqjq2qtpJFNKW3mn33RuLU\nToken Out Address: AS12N76WPYB3QNYKGhV2jZuQs1djdhNJLQgnm7m52pHWecvvj1fCQ\nAmount In: 30000000000\nAmount Out Min: 1\nBin Step: 0\nTo: AS1VJvdvuU5AZFFKWLD2yxyHsS1CE65imQxQUShqNEf4BqZ2ZHLT\nDeadline: 1751329363004\nIs Dusa: false\nCoins To Use: 10000000\nSwap Params: \n,Router Address: AS1XqtvX3rz2RWbnqLfaYVKEjM3VS5pny9yKDdXcmJ5C1vrcLEFd\nPool Address: AS112Wdy9pM4fvLNLHQXyf7uam9waMPdG5ekr4vxCyQHPkrMMPPY\nToken In Address: AS12N76WPYB3QNYKGhV2jZuQs1djdhNJLQgnm7m52pHWecvvj1fCQ\nToken Out Address: AS12FW5Rs5YN2zdpEnqwj4iHUUPt9R4Eqjq2qtpJFNKW3mn33RuLU\nAmount In: 30000000000\nAmount Out Min: 1\nBin Step: 20\nTo: AS1VJvdvuU5AZFFKWLD2yxyHsS1CE65imQxQUShqNEf4BqZ2ZHLT\nDeadline: 1751329363004\nIs Dusa: true\nCoins To Use: 1000000\nSwap Params: \n"
2025-07-01T00:12:58.540152Z DEBUG quedge::executor: Event Data: "TRANSFER SUCCESS"
2025-07-01T00:12:58.540253Z DEBUG quedge::executor: Event Data: "TRANSFER SUCCESS"
2025-07-01T00:12:58.540366Z DEBUG quedge::executor: Event Data: "TRANSFER SUCCESS"
2025-07-01T00:12:58.540473Z DEBUG quedge::executor: Event Data: "TRANSFER SUCCESS"
2025-07-01T00:12:58.540600Z DEBUG quedge::executor: Event Data: "UPDATE_CUMULATIVE_PRICES::AS12dny2u8p6DmcPnYurnzqrAG7kMjYUcSD6bDCdJfbYeadYqXaL5,AS12JfpytS54Pq2Pi5ZP9dZ8kxe21TmwBpoiQuMNvAgB3bSVsCs9k,11572553872345151594000,3549939432915654000,1751328772500"
2025-07-01T00:12:58.540733Z DEBUG quedge::executor: Event Data: "SWAP:AS12dny2u8p6DmcPnYurnzqrAG7kMjYUcSD6bDCdJfbYeadYqXaL5,AS1VJvdvuU5AZFFKWLD2yxyHsS1CE65imQxQUShqNEf4BqZ2ZHLT,30000000000,AS12FW5Rs5YN2zdpEnqwj4iHUUPt9R4Eqjq2qtpJFNKW3mn33RuLU,16746948,AS12N76WPYB3QNYKGhV2jZuQs1djdhNJLQgnm7m52pHWecvvj1fCQ,90000000,22500000,67500000,5645952771648,3144448351"
2025-07-01T00:12:58.540832Z DEBUG quedge::executor: Event Data: "Spent 0 coins"
2025-07-01T00:12:58.540932Z DEBUG quedge::executor: Event Data: "Swap Route Exexcuted: Pool Address: AS12dny2u8p6DmcPnYurnzqrAG7kMjYUcSD6bDCdJfbYeadYqXaL5\nToken In Address: AS12FW5Rs5YN2zdpEnqwj4iHUUPt9R4Eqjq2qtpJFNKW3mn33RuLU\nToken Out Address: AS12N76WPYB3QNYKGhV2jZuQs1djdhNJLQgnm7m52pHWecvvj1fCQ\nReceiver Address: AS1VJvdvuU5AZFFKWLD2yxyHsS1CE65imQxQUShqNEf4BqZ2ZHLT\nAmount In: 30000000000\nMin Amount Out: 1 \nIs Transfer From: true"
2025-07-01T00:12:58.541033Z DEBUG quedge::executor: Event Data: "Spent 0 coins"
2025-07-01T00:12:58.541160Z DEBUG quedge::executor: Event Data: "TRANSFER SUCCESS"
2025-07-01T00:12:58.541278Z DEBUG quedge::executor: Event Data: "SWAP:AS1VJvdvuU5AZFFKWLD2yxyHsS1CE65imQxQUShqNEf4BqZ2ZHLT;?!8384824;?!false;?!ۮÿ\0\0\0\0\0\0\0\0\0\0\0\0\0\0;?!ᅧ祥\u{7}\0\0\0\0\0\0\0\0\0\0\0\0\0;?!0;?!苖\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0"
2025-07-01T00:12:58.541387Z DEBUG quedge::executor: Event Data: "TRANSFER SUCCESS"
2025-07-01T00:12:58.541492Z DEBUG quedge::executor: Event Data: "Received coins"
2025-07-01T00:12:58.541595Z DEBUG quedge::executor: Event Data: "TRANSFER SUCCESS"
2025-07-01T00:12:58.541698Z DEBUG quedge::executor: Event Data: "TRANSFER SUCCESS"
2025-07-01T00:12:58.541802Z DEBUG quedge::executor: Event Data: "FLASH_ARB_EXEC:32101437799"
2025-07-01T00:12:58.541905Z DEBUG quedge::executor: Event Data: "TRANSFER SUCCESS"
2025-07-01T00:12:58.542008Z DEBUG quedge::executor: Event Data: "TRANSFER SUCCESS"
2025-07-01T00:12:58.542111Z DEBUG quedge::executor: Event Data: "UPDATE_CUMULATIVE_PRICES::AS1fewbVp4FMTEJH81e1BNaH9B3SmeE7sNpZQ2QqwP1wGBzLKJGF,AS1VJvdvuU5AZFFKWLD2yxyHsS1CE65imQxQUShqNEf4BqZ2ZHLT,675471638813424378113000,15013514792831030369615106674872000,1751328772500"
2025-07-01T00:12:58.542212Z DEBUG quedge::executor: Event Data: "Spent 0 coins"
2025-07-01T00:12:58.542316Z DEBUG quedge::executor: Event Data: "FLASH_LOAN:AS1fewbVp4FMTEJH81e1BNaH9B3SmeE7sNpZQ2QqwP1wGBzLKJGF,AS1VJvdvuU5AZFFKWLD2yxyHsS1CE65imQxQUShqNEf4BqZ2ZHLT,AS1VJvdvuU5AZFFKWLD2yxyHsS1CE65imQxQUShqNEf4BqZ2ZHLT,0,30000000000,12817214505974134383413567,674627845226602,1000"
2025-07-01T00:12:58.542452Z ERROR quedge::spawns::swap_fetcher: Error in detect_and_execute_arb: Failed to parse amount out to U256
2025-07-01T00:12:58.542805Z  INFO quedge::strategies::bellman_ford_matrix: Updating sparse matrix by pool: Pool { address: "AS12dny2u8p6DmcPnYurnzqrAG7kMjYUcSD6bDCdJfbYeadYqXaL5", a_token: Token { address: "AS12N76WPYB3QNYKGhV2jZuQs1djdhNJLQgnm7m52pHWecvvj1fCQ", name: "USD Coin", symbol: "USDC", decimals: 6, dex: EagleFi }, b_token: Token { address: "AS12FW5Rs5YN2zdpEnqwj4iHUUPt9R4Eqjq2qtpJFNKW3mn33RuLU", name: "Wrapped Massa", symbol: "WMAS", decimals: 9, dex: EagleFi }, a_reserve: 3144.448351, b_reserve: 5645.952771648, dex: EagleFi, swap_fee: 0.3, a_price: 1.7955304528543041, b_price: 0.5569384793281162, active_bin_id: 0, bin_step: 0, bins: {} }
2025-07-01T00:12:58.542950Z  WARN quedge::spawns::swap_fetcher: Skipping arbitrage detection after updating the sparse matrix because of an order from the events stream
2025-07-01T00:12:58.543054Z  INFO quedge::spawns::swap_fetcher: Swap event processed successfully
2025-07-01T00:12:58.543166Z DEBUG quedge::spawns::swap_fetcher: Processing last event of the operation O11Mk34hyWYQFQgkfqk99xBY6MeZSpPC387ju4JMSSpJLRQX1J1...
2025-07-01T00:12:58.543267Z  WARN quedge::spawns::swap_fetcher: Pool AS1VJvdvuU5AZFFKWLD2yxyHsS1CE65imQxQUShqNEf4BqZ2ZHLT not found in the pools hash map
2025-07-01T00:12:58.543366Z  WARN quedge::spawns::swap_fetcher: Pool AS1fewbVp4FMTEJH81e1BNaH9B3SmeE7sNpZQ2QqwP1wGBzLKJGF not found in the pools hash map
2025-07-01T00:12:58.543463Z  WARN quedge::spawns::swap_fetcher: Pool AS1VJvdvuU5AZFFKWLD2yxyHsS1CE65imQxQUShqNEf4BqZ2ZHLT not found in the pools hash map
2025-07-01T00:12:58.543560Z  WARN quedge::spawns::swap_fetcher: Pool AS1XqtvX3rz2RWbnqLfaYVKEjM3VS5pny9yKDdXcmJ5C1vrcLEFd not found in the pools hash map
2025-07-01T00:12:58.543668Z  INFO quedge::spawns::swap_fetcher: New detected swap on pool: AS112Wdy9pM4fvLNLHQXyf7uam9waMPdG5ekr4vxCyQHPkrMMPPY
2025-07-01T00:12:58.861140Z  INFO quedge::strategies::bellman_ford_matrix: Updating sparse matrix by pool: Pool { address: "AS112Wdy9pM4fvLNLHQXyf7uam9waMPdG5ekr4vxCyQHPkrMMPPY", a_token: Token { address: "AS12FW5Rs5YN2zdpEnqwj4iHUUPt9R4Eqjq2qtpJFNKW3mn33RuLU", name: "Wrapped Massa", symbol: "WMAS", decimals: 9, dex: Dusa }, b_token: Token { address: "AS12N76WPYB3QNYKGhV2jZuQs1djdhNJLQgnm7m52pHWecvvj1fCQ", name: "USD Coin", symbol: "USDC", decimals: 6, dex: Dusa }, a_reserve: 11123.064089995, b_reserve: 5963.386907, dex: Dusa, swap_fee: 0.0, a_price: 0.5206450285625964, b_price: 1.9206944177702283, active_bin_id: 8384824, bin_step: 20, bins: {8384823: Bin { id: 8384823, a_reserve: 0.0, b_reserve: 256.595466 }, 8384824: Bin { id: 8384824, a_reserve: 258.866559149, b_reserve: 122.331069 }, 8384825: Bin { id: 8384825, a_reserve: 493.827160493, b_reserve: 0.0 }} }
2025-07-01T00:12:58.861360Z  INFO quedge::spawns::swap_fetcher: Swap event processed successfully
2025-07-01T00:12:58.861610Z DEBUG quedge::helpers: Detecting arbitrage cycles...
2025-07-01T00:12:58.861841Z DEBUG quedge::helpers: Arbitrage cycles found (3 total):
2025-07-01T00:12:58.862006Z DEBUG quedge::helpers: Cycle path Inisde Simulation Task:
2025-07-01T00:12:58.862125Z DEBUG quedge::helpers::display: Path: ["WMAS-Dusa", "WMAS-EagleFi", "USDC-EagleFi", "USDC-Dusa", "WMAS-Dusa"]
2025-07-01T00:12:58.862254Z DEBUG quedge::helpers: Cycle path Inisde Simulation Task:
2025-07-01T00:12:58.862367Z DEBUG quedge::helpers::display: Path: ["USDC-Dusa", "WMAS-Dusa", "WMAS-EagleFi", "USDC-EagleFi", "USDC-Dusa"]
2025-07-01T00:12:58.862507Z DEBUG quedge::helpers: Cycle path Inisde Simulation Task:
2025-07-01T00:12:58.862611Z  INFO quedge::helpers: Default start amount for token AS12FW5Rs5YN2zdpEnqwj4iHUUPt9R4Eqjq2qtpJFNKW3mn33RuLU is 10
2025-07-01T00:12:58.862708Z DEBUG quedge::helpers::display: Path: ["USDC-EagleFi", "USDC-Dusa", "WMAS-Dusa", "WMAS-EagleFi", "USDC-EagleFi"]
2025-07-01T00:12:58.862796Z  INFO quedge::helpers: Offchain Amount out after Offchain simulation: 10.646195262305197
2025-07-01T00:12:58.862898Z  INFO quedge::helpers: Offchain Earning percentage: 6.461952623051967
2025-07-01T00:12:59.032961Z  INFO quedge::helpers: Amount out after Onchain simulation: 31.76272718
2025-07-01T00:12:59.033105Z  INFO quedge::helpers: Earning percentage: 5.875757266666662
2025-07-01T00:12:59.033308Z  INFO quedge::helpers: Found 1 valid cycles
2025-07-01T00:12:59.033491Z  INFO quedge::executor: Executing arbitrage with start amount 30 for cycle path: ["WMAS-Dusa", "WMAS-EagleFi", "USDC-EagleFi", "USDC-Dusa", "WMAS-Dusa"]...
2025-07-01T00:13:17.631930Z  INFO quedge::spawns::swap_fetcher: Found 2 Swap events from the operation O12iugY5JfqLsShKF1TLsgF5MF4YXA5Tystqw9ZHWMbS243oqWFs. Processing them...
2025-07-01T00:13:17.632129Z  INFO quedge::spawns::swap_fetcher: New detected swap on pool: AS12dny2u8p6DmcPnYurnzqrAG7kMjYUcSD6bDCdJfbYeadYqXaL5
2025-07-01T00:13:18.227086Z  INFO quedge::strategies::bellman_ford_matrix: Updating sparse matrix by pool: Pool { address: "AS12dny2u8p6DmcPnYurnzqrAG7kMjYUcSD6bDCdJfbYeadYqXaL5", a_token: Token { address: "AS12N76WPYB3QNYKGhV2jZuQs1djdhNJLQgnm7m52pHWecvvj1fCQ", name: "USD Coin", symbol: "USDC", decimals: 6, dex: EagleFi }, b_token: Token { address: "AS12FW5Rs5YN2zdpEnqwj4iHUUPt9R4Eqjq2qtpJFNKW3mn33RuLU", name: "Wrapped Massa", symbol: "WMAS", decimals: 9, dex: EagleFi }, a_reserve: 3127.878104, b_reserve: 5675.930271648, dex: EagleFi, swap_fee: 0.3, a_price: 1.8146264281812945, b_price: 0.551077612708555, active_bin_id: 0, bin_step: 0, bins: {} }
2025-07-01T00:13:18.227292Z  WARN quedge::spawns::swap_fetcher: Skipping arbitrage detection after updating the sparse matrix because of an order from the events stream
2025-07-01T00:13:18.227419Z  INFO quedge::spawns::swap_fetcher: Swap event processed successfully
2025-07-01T00:13:18.227535Z DEBUG quedge::spawns::swap_fetcher: Processing last event of the operation O12iugY5JfqLsShKF1TLsgF5MF4YXA5Tystqw9ZHWMbS243oqWFs...
2025-07-01T00:13:18.227648Z  WARN quedge::spawns::swap_fetcher: Pool AS1VJvdvuU5AZFFKWLD2yxyHsS1CE65imQxQUShqNEf4BqZ2ZHLT not found in the pools hash map
2025-07-01T00:13:18.227755Z  WARN quedge::spawns::swap_fetcher: Pool AS1fewbVp4FMTEJH81e1BNaH9B3SmeE7sNpZQ2QqwP1wGBzLKJGF not found in the pools hash map
2025-07-01T00:13:18.227873Z  WARN quedge::spawns::swap_fetcher: Pool AS1VJvdvuU5AZFFKWLD2yxyHsS1CE65imQxQUShqNEf4BqZ2ZHLT not found in the pools hash map
2025-07-01T00:13:18.228002Z  WARN quedge::spawns::swap_fetcher: Pool AS1XqtvX3rz2RWbnqLfaYVKEjM3VS5pny9yKDdXcmJ5C1vrcLEFd not found in the pools hash map
2025-07-01T00:13:18.228132Z  INFO quedge::spawns::swap_fetcher: New detected swap on pool: AS112Wdy9pM4fvLNLHQXyf7uam9waMPdG5ekr4vxCyQHPkrMMPPY
2025-07-01T00:13:18.359629Z DEBUG quedge::executor: Showing all operation events data for operation id: O12iugY5JfqLsShKF1TLsgF5MF4YXA5Tystqw9ZHWMbS243oqWFs ...
2025-07-01T00:13:18.359776Z DEBUG quedge::executor: Event Data: "ArbRoutes inside execuwithFlmashLoan: Router Address: AS12JfpytS54Pq2Pi5ZP9dZ8kxe21TmwBpoiQuMNvAgB3bSVsCs9k\nPool Address: AS12dny2u8p6DmcPnYurnzqrAG7kMjYUcSD6bDCdJfbYeadYqXaL5\nToken In Address: AS12FW5Rs5YN2zdpEnqwj4iHUUPt9R4Eqjq2qtpJFNKW3mn33RuLU\nToken Out Address: AS12N76WPYB3QNYKGhV2jZuQs1djdhNJLQgnm7m52pHWecvvj1fCQ\nAmount In: 30000000000\nAmount Out Min: 1\nBin Step: 0\nTo: AS1VJvdvuU5AZFFKWLD2yxyHsS1CE65imQxQUShqNEf4BqZ2ZHLT\nDeadline: 1751329379033\nIs Dusa: false\nCoins To Use: 10000000\nSwap Params: \n,Router Address: AS1XqtvX3rz2RWbnqLfaYVKEjM3VS5pny9yKDdXcmJ5C1vrcLEFd\nPool Address: AS112Wdy9pM4fvLNLHQXyf7uam9waMPdG5ekr4vxCyQHPkrMMPPY\nToken In Address: AS12N76WPYB3QNYKGhV2jZuQs1djdhNJLQgnm7m52pHWecvvj1fCQ\nToken Out Address: AS12FW5Rs5YN2zdpEnqwj4iHUUPt9R4Eqjq2qtpJFNKW3mn33RuLU\nAmount In: 30000000000\nAmount Out Min: 1\nBin Step: 20\nTo: AS1VJvdvuU5AZFFKWLD2yxyHsS1CE65imQxQUShqNEf4BqZ2ZHLT\nDeadline: 1751329379033\nIs Dusa: true\nCoins To Use: 1000000\nSwap Params: \n"
2025-07-01T00:13:18.359901Z DEBUG quedge::executor: Event Data: "TRANSFER SUCCESS"
2025-07-01T00:13:18.360020Z DEBUG quedge::executor: Event Data: "TRANSFER SUCCESS"
2025-07-01T00:13:18.360142Z DEBUG quedge::executor: Event Data: "TRANSFER SUCCESS"
2025-07-01T00:13:18.360239Z DEBUG quedge::executor: Event Data: "TRANSFER SUCCESS"
2025-07-01T00:13:18.360393Z DEBUG quedge::executor: Event Data: "UPDATE_CUMULATIVE_PRICES::AS12dny2u8p6DmcPnYurnzqrAG7kMjYUcSD6bDCdJfbYeadYqXaL5,AS12JfpytS54Pq2Pi5ZP9dZ8kxe21TmwBpoiQuMNvAgB3bSVsCs9k,11572644687229497962000,3549989478965318000,1751328788500"
2025-07-01T00:13:18.360509Z DEBUG quedge::executor: Event Data: "SWAP:AS12dny2u8p6DmcPnYurnzqrAG7kMjYUcSD6bDCdJfbYeadYqXaL5,AS1VJvdvuU5AZFFKWLD2yxyHsS1CE65imQxQUShqNEf4BqZ2ZHLT,30000000000,AS12FW5Rs5YN2zdpEnqwj4iHUUPt9R4Eqjq2qtpJFNKW3mn33RuLU,16570247,AS12N76WPYB3QNYKGhV2jZuQs1djdhNJLQgnm7m52pHWecvvj1fCQ,90000000,22500000,67500000,5675930271648,3127878104"
2025-07-01T00:13:18.360640Z DEBUG quedge::executor: Event Data: "Spent 0 coins"
2025-07-01T00:13:18.360760Z DEBUG quedge::executor: Event Data: "Swap Route Exexcuted: Pool Address: AS12dny2u8p6DmcPnYurnzqrAG7kMjYUcSD6bDCdJfbYeadYqXaL5\nToken In Address: AS12FW5Rs5YN2zdpEnqwj4iHUUPt9R4Eqjq2qtpJFNKW3mn33RuLU\nToken Out Address: AS12N76WPYB3QNYKGhV2jZuQs1djdhNJLQgnm7m52pHWecvvj1fCQ\nReceiver Address: AS1VJvdvuU5AZFFKWLD2yxyHsS1CE65imQxQUShqNEf4BqZ2ZHLT\nAmount In: 30000000000\nMin Amount Out: 1 \nIs Transfer From: true"
2025-07-01T00:13:18.360875Z DEBUG quedge::executor: Event Data: "Spent 0 coins"
2025-07-01T00:13:18.361002Z DEBUG quedge::executor: Event Data: "TRANSFER SUCCESS"
2025-07-01T00:13:18.361140Z DEBUG quedge::executor: Event Data: "SWAP:AS1VJvdvuU5AZFFKWLD2yxyHsS1CE65imQxQUShqNEf4BqZ2ZHLT;?!8384824;?!false;?!嘒ü\0\0\0\0\0\0\0\0\0\0\0\0\0\0;?!섌攴\u{7}\0\0\0\0\0\0\0\0\0\0\0\0\0;?!0;?!腵\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0"
2025-07-01T00:13:18.361253Z DEBUG quedge::executor: Event Data: "TRANSFER SUCCESS"
2025-07-01T00:13:18.361350Z DEBUG quedge::executor: Event Data: "Received coins"
2025-07-01T00:13:18.361477Z DEBUG quedge::executor: Event Data: "TRANSFER SUCCESS"
2025-07-01T00:13:18.361594Z DEBUG quedge::executor: Event Data: "TRANSFER SUCCESS"
2025-07-01T00:13:18.361731Z DEBUG quedge::executor: Event Data: "FLASH_ARB_EXEC:31762727180"
2025-07-01T00:13:18.361831Z DEBUG quedge::executor: Event Data: "TRANSFER SUCCESS"
2025-07-01T00:13:18.361949Z DEBUG quedge::executor: Event Data: "TRANSFER SUCCESS"
2025-07-01T00:13:18.362062Z DEBUG quedge::executor: Event Data: "UPDATE_CUMULATIVE_PRICES::AS1fewbVp4FMTEJH81e1BNaH9B3SmeE7sNpZQ2QqwP1wGBzLKJGF,AS1VJvdvuU5AZFFKWLD2yxyHsS1CE65imQxQUShqNEf4BqZ2ZHLT,675482420186028819825000,15013719792662513092464762697592000,1751328788500"
2025-07-01T00:13:18.362174Z DEBUG quedge::executor: Event Data: "Spent 0 coins"
2025-07-01T00:13:18.362277Z DEBUG quedge::executor: Event Data: "FLASH_LOAN:AS1fewbVp4FMTEJH81e1BNaH9B3SmeE7sNpZQ2QqwP1wGBzLKJGF,AS1VJvdvuU5AZFFKWLD2yxyHsS1CE65imQxQUShqNEf4BqZ2ZHLT,AS1VJvdvuU5AZFFKWLD2yxyHsS1CE65imQxQUShqNEf4BqZ2ZHLT,0,30000000000,12817214505974134383413567,674627845226602,1000"
2025-07-01T00:13:18.362428Z ERROR quedge::spawns::swap_fetcher: Error in detect_and_execute_arb: Failed to parse amount out to U256
2025-07-01T00:13:18.570028Z  INFO quedge::strategies::bellman_ford_matrix: Updating sparse matrix by pool: Pool { address: "AS112Wdy9pM4fvLNLHQXyf7uam9waMPdG5ekr4vxCyQHPkrMMPPY", a_token: Token { address: "AS12FW5Rs5YN2zdpEnqwj4iHUUPt9R4Eqjq2qtpJFNKW3mn33RuLU", name: "Wrapped Massa", symbol: "WMAS", decimals: 9, dex: Dusa }, b_token: Token { address: "AS12N76WPYB3QNYKGhV2jZuQs1djdhNJLQgnm7m52pHWecvvj1fCQ", name: "USD Coin", symbol: "USDC", decimals: 6, dex: Dusa }, a_reserve: 11091.301362815, b_reserve: 5979.924013, dex: Dusa, swap_fee: 0.0, a_price: 0.5206450285625964, b_price: 1.9206944177702283, active_bin_id: 8384824, bin_step: 20, bins: {8384823: Bin { id: 8384823, a_reserve: 0.0, b_reserve: 256.595466 }, 8384824: Bin { id: 8384824, a_reserve: 227.103831969, b_reserve: 138.868175 }, 8384825: Bin { id: 8384825, a_reserve: 493.827160493, b_reserve: 0.0 }} }
2025-07-01T00:13:18.570274Z  INFO quedge::spawns::swap_fetcher: Swap event processed successfully
2025-07-01T00:13:18.570776Z DEBUG quedge::helpers: Detecting arbitrage cycles...
2025-07-01T00:13:18.570976Z DEBUG quedge::helpers: Arbitrage cycles found (3 total):
2025-07-01T00:13:18.571081Z DEBUG quedge::helpers: Cycle path Inisde Simulation Task:
2025-07-01T00:13:18.571215Z DEBUG quedge::helpers::display: Path: ["WMAS-Dusa", "WMAS-EagleFi", "USDC-EagleFi", "USDC-Dusa", "WMAS-Dusa"]
2025-07-01T00:13:18.571383Z DEBUG quedge::helpers: Cycle path Inisde Simulation Task:
2025-07-01T00:13:18.571500Z  INFO quedge::helpers: Default start amount for token AS12FW5Rs5YN2zdpEnqwj4iHUUPt9R4Eqjq2qtpJFNKW3mn33RuLU is 10
2025-07-01T00:13:18.571611Z DEBUG quedge::helpers::display: Path: ["USDC-Dusa", "WMAS-Dusa", "WMAS-EagleFi", "USDC-EagleFi", "USDC-Dusa"]
2025-07-01T00:13:18.571731Z  INFO quedge::helpers: Offchain Amount out after Offchain simulation: 10.534259543108421
2025-07-01T00:13:18.571837Z DEBUG quedge::helpers: Cycle path Inisde Simulation Task:
2025-07-01T00:13:18.571930Z  INFO quedge::helpers: Offchain Earning percentage: 5.342595431084209
2025-07-01T00:13:18.572026Z DEBUG quedge::helpers::display: Path: ["USDC-EagleFi", "USDC-Dusa", "WMAS-Dusa", "WMAS-EagleFi", "USDC-EagleFi"]
2025-07-01T00:13:18.836903Z  INFO quedge::helpers: Amount out after Onchain simulation: 31.429350329
2025-07-01T00:13:18.837103Z  INFO quedge::helpers: Earning percentage: 4.7645010966666606
2025-07-01T00:13:18.837332Z  INFO quedge::helpers: Found 1 valid cycles
2025-07-01T00:13:18.837553Z  INFO quedge::executor: Executing arbitrage with start amount 30 for cycle path: ["WMAS-Dusa", "WMAS-EagleFi", "USDC-EagleFi", "USDC-Dusa", "WMAS-Dusa"]...
2025-07-01T00:13:36.325234Z  INFO quedge: Logger initialized Successfully
2025-07-01T00:13:37.116594Z  INFO quedge: Allowed start tokens: ["AS12FW5Rs5YN2zdpEnqwj4iHUUPt9R4Eqjq2qtpJFNKW3mn33RuLU"]
2025-07-01T00:13:37.116891Z  INFO quedge: Exporting the sparse matrix to a png file...
2025-07-01T00:13:37.265869Z  INFO quedge: Exported the sparse matrix to a png file successfully!
2025-07-01T00:13:37.266221Z  INFO quedge::spawns::swap_fetcher: Swap event fetcher working in candidate state
2025-07-01T00:13:37.266343Z DEBUG quedge::helpers: Detecting arbitrage cycles...
2025-07-01T00:13:37.266670Z DEBUG quedge::helpers: Arbitrage cycles found (3 total):
2025-07-01T00:13:37.266859Z DEBUG quedge::helpers: Cycle path Inisde Simulation Task:
2025-07-01T00:13:37.267020Z DEBUG quedge::helpers::display: Path: ["WMAS-EagleFi", "USDC-EagleFi", "USDC-Dusa", "WMAS-Dusa", "WMAS-EagleFi"]
2025-07-01T00:13:37.267412Z DEBUG quedge::helpers: Cycle path Inisde Simulation Task:
2025-07-01T00:13:37.267529Z DEBUG quedge::helpers::display: Path: ["WMAS-Dusa", "WMAS-EagleFi", "USDC-EagleFi", "USDC-Dusa", "WMAS-Dusa"]
2025-07-01T00:13:37.267689Z DEBUG quedge::helpers: Cycle path Inisde Simulation Task:
2025-07-01T00:13:37.267782Z  INFO quedge::helpers: Default start amount for token AS12FW5Rs5YN2zdpEnqwj4iHUUPt9R4Eqjq2qtpJFNKW3mn33RuLU is 10
2025-07-01T00:13:37.267858Z DEBUG quedge::helpers::display: Path: ["USDC-Dusa", "WMAS-Dusa", "WMAS-EagleFi", "USDC-EagleFi", "USDC-Dusa"]
2025-07-01T00:13:37.267929Z  INFO quedge::helpers: Offchain Amount out after Offchain simulation: 10.424080603467203
2025-07-01T00:13:37.268029Z  INFO quedge::helpers: Offchain Earning percentage: 4.240806034672033
2025-07-01T00:13:37.268099Z  INFO quedge::helpers: Default start amount for token AS12FW5Rs5YN2zdpEnqwj4iHUUPt9R4Eqjq2qtpJFNKW3mn33RuLU is 10
2025-07-01T00:13:37.268204Z  INFO quedge::helpers: Offchain Amount out after Offchain simulation: 10.424080603467203
2025-07-01T00:13:37.268299Z  INFO quedge::helpers: Offchain Earning percentage: 4.240806034672033
2025-07-01T00:13:37.414790Z  INFO quedge::spawns::swap_fetcher: Start waitiing for an arbitrage opportunity...
2025-07-01T00:13:37.426213Z DEBUG quedge::spawns::balance_notifier: Balance of AU12Yd4kCcsizeeTEK9AZyBnuJNZ1cpp99XfCZgzS77ZKnwTFMpVE is 104421.262238641
2025-07-01T00:13:37.431996Z  INFO quedge::helpers: Amount out after Onchain simulation: 20.770233857
2025-07-01T00:13:37.432107Z  INFO quedge::helpers: Earning percentage: 3.851169285000004
2025-07-01T00:13:37.441294Z  INFO quedge::helpers: Amount out after Onchain simulation: 20.770233857
2025-07-01T00:13:37.441432Z  INFO quedge::helpers: Earning percentage: 3.851169285000004
2025-07-01T00:13:37.441668Z  INFO quedge::helpers: Found 2 valid cycles
2025-07-01T00:13:37.441866Z  INFO quedge::executor: Executing arbitrage with start amount 20 for cycle path: ["WMAS-Dusa", "WMAS-EagleFi", "USDC-EagleFi", "USDC-Dusa", "WMAS-Dusa"]...
2025-07-01T00:13:46.249795Z  INFO quedge::spawns::swap_fetcher: Found 2 Swap events from the operation O12CUB2Xw3ZZXsbptuuaZ4u3JEjAy1shaHpGibzAEce4162MPvmg. Processing them...
2025-07-01T00:13:46.250221Z  INFO quedge::spawns::swap_fetcher: New detected swap on pool: AS12dny2u8p6DmcPnYurnzqrAG7kMjYUcSD6bDCdJfbYeadYqXaL5
2025-07-01T00:13:46.410659Z  INFO quedge::strategies::bellman_ford_matrix: Updating sparse matrix by pool: Pool { address: "AS12dny2u8p6DmcPnYurnzqrAG7kMjYUcSD6bDCdJfbYeadYqXaL5", a_token: Token { address: "AS12N76WPYB3QNYKGhV2jZuQs1djdhNJLQgnm7m52pHWecvvj1fCQ", name: "USD Coin", symbol: "USDC", decimals: 6, dex: EagleFi }, b_token: Token { address: "AS12FW5Rs5YN2zdpEnqwj4iHUUPt9R4Eqjq2qtpJFNKW3mn33RuLU", name: "Wrapped Massa", symbol: "WMAS", decimals: 9, dex: EagleFi }, a_reserve: 3100.646185, b_reserve: 5725.892771648, dex: EagleFi, swap_fee: 0.3, a_price: 1.8466772504867401, b_price: 0.5415131419074036, active_bin_id: 0, bin_step: 0, bins: {} }
2025-07-01T00:13:46.410840Z  WARN quedge::spawns::swap_fetcher: Skipping arbitrage detection after updating the sparse matrix because of an order from the events stream
2025-07-01T00:13:46.410937Z  INFO quedge::spawns::swap_fetcher: Swap event processed successfully
2025-07-01T00:13:46.411045Z DEBUG quedge::spawns::swap_fetcher: Processing last event of the operation O12CUB2Xw3ZZXsbptuuaZ4u3JEjAy1shaHpGibzAEce4162MPvmg...
2025-07-01T00:13:46.411135Z  WARN quedge::spawns::swap_fetcher: Pool AS1VJvdvuU5AZFFKWLD2yxyHsS1CE65imQxQUShqNEf4BqZ2ZHLT not found in the pools hash map
2025-07-01T00:13:46.411246Z  WARN quedge::spawns::swap_fetcher: Pool AS1fewbVp4FMTEJH81e1BNaH9B3SmeE7sNpZQ2QqwP1wGBzLKJGF not found in the pools hash map
2025-07-01T00:13:46.411372Z  WARN quedge::spawns::swap_fetcher: Pool AS1VJvdvuU5AZFFKWLD2yxyHsS1CE65imQxQUShqNEf4BqZ2ZHLT not found in the pools hash map
2025-07-01T00:13:46.411499Z  WARN quedge::spawns::swap_fetcher: Pool AS1XqtvX3rz2RWbnqLfaYVKEjM3VS5pny9yKDdXcmJ5C1vrcLEFd not found in the pools hash map
2025-07-01T00:13:46.411609Z  INFO quedge::spawns::swap_fetcher: New detected swap on pool: AS112Wdy9pM4fvLNLHQXyf7uam9waMPdG5ekr4vxCyQHPkrMMPPY
2025-07-01T00:13:46.514132Z DEBUG quedge::executor: Showing all operation events data for operation id: O12CUB2Xw3ZZXsbptuuaZ4u3JEjAy1shaHpGibzAEce4162MPvmg ...
2025-07-01T00:13:46.514350Z  INFO quedge::executor: Flash Loan Arbitrage executed successfully with start amount 20. Operation id: O12CUB2Xw3ZZXsbptuuaZ4u3JEjAy1shaHpGibzAEce4162MPvmg Cycle path: ["WMAS-Dusa", "WMAS-EagleFi", "USDC-EagleFi", "USDC-Dusa", "WMAS-Dusa"]
2025-07-01T00:13:46.712955Z  INFO quedge::strategies::bellman_ford_matrix: Updating sparse matrix by pool: Pool { address: "AS112Wdy9pM4fvLNLHQXyf7uam9waMPdG5ekr4vxCyQHPkrMMPPY", a_token: Token { address: "AS12FW5Rs5YN2zdpEnqwj4iHUUPt9R4Eqjq2qtpJFNKW3mn33RuLU", name: "Wrapped Massa", symbol: "WMAS", decimals: 9, dex: Dusa }, b_token: Token { address: "AS12N76WPYB3QNYKGhV2jZuQs1djdhNJLQgnm7m52pHWecvvj1fCQ", name: "USD Coin", symbol: "USDC", decimals: 6, dex: Dusa }, a_reserve: 11039.101778629, b_reserve: 6007.101467, dex: Dusa, swap_fee: 0.0, a_price: 0.5206450285625964, b_price: 1.9206944177702283, active_bin_id: 8384824, bin_step: 20, bins: {8384823: Bin { id: 8384823, a_reserve: 0.0, b_reserve: 256.595466 }, 8384824: Bin { id: 8384824, a_reserve: 174.904247783, b_reserve: 166.045629 }, 8384825: Bin { id: 8384825, a_reserve: 493.827160493, b_reserve: 0.0 }} }
2025-07-01T00:13:46.713182Z  INFO quedge::spawns::swap_fetcher: Swap event processed successfully
2025-07-01T00:13:46.713420Z DEBUG quedge::helpers: Detecting arbitrage cycles...
2025-07-01T00:13:46.713619Z DEBUG quedge::helpers: Arbitrage cycles found (3 total):
2025-07-01T00:13:46.713749Z DEBUG quedge::helpers: Cycle path Inisde Simulation Task:
2025-07-01T00:13:46.713850Z DEBUG quedge::helpers::display: Path: ["WMAS-EagleFi", "USDC-EagleFi", "USDC-Dusa", "WMAS-Dusa", "WMAS-EagleFi"]
2025-07-01T00:13:46.713972Z DEBUG quedge::helpers: Cycle path Inisde Simulation Task:
2025-07-01T00:13:46.714065Z DEBUG quedge::helpers::display: Path: ["WMAS-Dusa", "WMAS-EagleFi", "USDC-EagleFi", "USDC-Dusa", "WMAS-Dusa"]
2025-07-01T00:13:46.714190Z DEBUG quedge::helpers: Cycle path Inisde Simulation Task:
2025-07-01T00:13:46.714271Z  INFO quedge::helpers: Default start amount for token AS12FW5Rs5YN2zdpEnqwj4iHUUPt9R4Eqjq2qtpJFNKW3mn33RuLU is 10
2025-07-01T00:13:46.714334Z DEBUG quedge::helpers::display: Path: ["USDC-Dusa", "WMAS-Dusa", "WMAS-EagleFi", "USDC-EagleFi", "USDC-Dusa"]
2025-07-01T00:13:46.714423Z  INFO quedge::helpers: Offchain Amount out after Offchain simulation: 10.351585932114416
2025-07-01T00:13:46.714511Z  INFO quedge::helpers: Default start amount for token AS12FW5Rs5YN2zdpEnqwj4iHUUPt9R4Eqjq2qtpJFNKW3mn33RuLU is 10
2025-07-01T00:13:46.714577Z  INFO quedge::helpers: Offchain Earning percentage: 3.5158593211441587
2025-07-01T00:13:46.714644Z  INFO quedge::helpers: Offchain Amount out after Offchain simulation: 10.351585932114416
2025-07-01T00:13:46.714735Z  INFO quedge::helpers: Offchain Earning percentage: 3.5158593211441587
2025-07-01T00:13:46.877292Z  INFO quedge::helpers: Amount out after Onchain simulation: 20.625912878
2025-07-01T00:13:46.877410Z  INFO quedge::helpers: Amount out after Onchain simulation: 20.625912878
2025-07-01T00:13:46.877493Z  INFO quedge::helpers: Earning percentage: 3.129564390000006
2025-07-01T00:13:46.877584Z  INFO quedge::helpers: Earning percentage: 3.129564390000006
2025-07-01T00:13:46.877726Z  INFO quedge::helpers: Found 2 valid cycles
2025-07-01T00:13:46.877908Z  INFO quedge::executor: Executing arbitrage with start amount 20 for cycle path: ["WMAS-Dusa", "WMAS-EagleFi", "USDC-EagleFi", "USDC-Dusa", "WMAS-Dusa"]...
2025-07-01T00:14:02.376486Z  INFO quedge::spawns::swap_fetcher: Found 2 Swap events from the operation O12oaYJFfTN4Lnp2jBsExS53rspok4MgXkhtDDSiMBPvAjCSSm2K. Processing them...
2025-07-01T00:14:02.376691Z  INFO quedge::spawns::swap_fetcher: New detected swap on pool: AS12dny2u8p6DmcPnYurnzqrAG7kMjYUcSD6bDCdJfbYeadYqXaL5
2025-07-01T00:14:02.523934Z  INFO quedge::strategies::bellman_ford_matrix: Updating sparse matrix by pool: Pool { address: "AS12dny2u8p6DmcPnYurnzqrAG7kMjYUcSD6bDCdJfbYeadYqXaL5", a_token: Token { address: "AS12N76WPYB3QNYKGhV2jZuQs1djdhNJLQgnm7m52pHWecvvj1fCQ", name: "USD Coin", symbol: "USDC", decimals: 6, dex: EagleFi }, b_token: Token { address: "AS12FW5Rs5YN2zdpEnqwj4iHUUPt9R4Eqjq2qtpJFNKW3mn33RuLU", name: "Wrapped Massa", symbol: "WMAS", decimals: 9, dex: EagleFi }, a_reserve: 3089.885885, b_reserve: 5745.877771648, dex: EagleFi, swap_fee: 0.3, a_price: 1.8595760443910374, b_price: 0.5377569812303502, active_bin_id: 0, bin_step: 0, bins: {} }
2025-07-01T00:14:02.524104Z  WARN quedge::spawns::swap_fetcher: Skipping arbitrage detection after updating the sparse matrix because of an order from the events stream
2025-07-01T00:14:02.524217Z  INFO quedge::spawns::swap_fetcher: Swap event processed successfully
2025-07-01T00:14:02.524340Z DEBUG quedge::spawns::swap_fetcher: Processing last event of the operation O12oaYJFfTN4Lnp2jBsExS53rspok4MgXkhtDDSiMBPvAjCSSm2K...
2025-07-01T00:14:02.524467Z  WARN quedge::spawns::swap_fetcher: Pool AS1VJvdvuU5AZFFKWLD2yxyHsS1CE65imQxQUShqNEf4BqZ2ZHLT not found in the pools hash map
2025-07-01T00:14:02.524584Z  WARN quedge::spawns::swap_fetcher: Pool AS1fewbVp4FMTEJH81e1BNaH9B3SmeE7sNpZQ2QqwP1wGBzLKJGF not found in the pools hash map
2025-07-01T00:14:02.524697Z  WARN quedge::spawns::swap_fetcher: Pool AS1VJvdvuU5AZFFKWLD2yxyHsS1CE65imQxQUShqNEf4BqZ2ZHLT not found in the pools hash map
2025-07-01T00:14:02.524810Z  WARN quedge::spawns::swap_fetcher: Pool AS1XqtvX3rz2RWbnqLfaYVKEjM3VS5pny9yKDdXcmJ5C1vrcLEFd not found in the pools hash map
2025-07-01T00:14:02.524914Z  INFO quedge::spawns::swap_fetcher: New detected swap on pool: AS112Wdy9pM4fvLNLHQXyf7uam9waMPdG5ekr4vxCyQHPkrMMPPY
2025-07-01T00:14:02.538938Z DEBUG quedge::executor: Showing all operation events data for operation id: O12oaYJFfTN4Lnp2jBsExS53rspok4MgXkhtDDSiMBPvAjCSSm2K ...
2025-07-01T00:14:02.539156Z  INFO quedge::executor: Flash Loan Arbitrage executed successfully with start amount 20. Operation id: O12oaYJFfTN4Lnp2jBsExS53rspok4MgXkhtDDSiMBPvAjCSSm2K Cycle path: ["WMAS-Dusa", "WMAS-EagleFi", "USDC-EagleFi", "USDC-Dusa", "WMAS-Dusa"]
2025-07-01T00:14:02.827385Z  INFO quedge::strategies::bellman_ford_matrix: Updating sparse matrix by pool: Pool { address: "AS112Wdy9pM4fvLNLHQXyf7uam9waMPdG5ekr4vxCyQHPkrMMPPY", a_token: Token { address: "AS12FW5Rs5YN2zdpEnqwj4iHUUPt9R4Eqjq2qtpJFNKW3mn33RuLU", name: "Wrapped Massa", symbol: "WMAS", decimals: 9, dex: Dusa }, b_token: Token { address: "AS12N76WPYB3QNYKGhV2jZuQs1djdhNJLQgnm7m52pHWecvvj1fCQ", name: "USD Coin", symbol: "USDC", decimals: 6, dex: Dusa }, a_reserve: 11018.475865751, b_reserve: 6017.840246, dex: Dusa, swap_fee: 0.0, a_price: 0.5206450285625964, b_price: 1.9206944177702283, active_bin_id: 8384824, bin_step: 20, bins: {8384823: Bin { id: 8384823, a_reserve: 0.0, b_reserve: 256.595466 }, 8384824: Bin { id: 8384824, a_reserve: 154.278334905, b_reserve: 176.784408 }, 8384825: Bin { id: 8384825, a_reserve: 493.827160493, b_reserve: 0.0 }} }
2025-07-01T00:14:02.827687Z  INFO quedge::spawns::swap_fetcher: Swap event processed successfully
2025-07-01T00:14:02.828002Z DEBUG quedge::helpers: Detecting arbitrage cycles...
2025-07-01T00:14:02.828301Z DEBUG quedge::helpers: Arbitrage cycles found (3 total):
2025-07-01T00:14:02.828470Z DEBUG quedge::helpers: Cycle path Inisde Simulation Task:
2025-07-01T00:14:02.828611Z DEBUG quedge::helpers::display: Path: ["WMAS-EagleFi", "USDC-EagleFi", "USDC-Dusa", "WMAS-Dusa", "WMAS-EagleFi"]
2025-07-01T00:14:02.828760Z DEBUG quedge::helpers: Cycle path Inisde Simulation Task:
2025-07-01T00:14:02.828888Z DEBUG quedge::helpers::display: Path: ["WMAS-Dusa", "WMAS-EagleFi", "USDC-EagleFi", "USDC-Dusa", "WMAS-Dusa"]
2025-07-01T00:14:02.829080Z DEBUG quedge::helpers: Cycle path Inisde Simulation Task:
2025-07-01T00:14:02.829170Z  INFO quedge::helpers: Default start amount for token AS12FW5Rs5YN2zdpEnqwj4iHUUPt9R4Eqjq2qtpJFNKW3mn33RuLU is 10
2025-07-01T00:14:02.829269Z DEBUG quedge::helpers::display: Path: ["USDC-Dusa", "WMAS-Dusa", "WMAS-EagleFi", "USDC-EagleFi", "USDC-Dusa"]
2025-07-01T00:14:02.829374Z  INFO quedge::helpers: Offchain Amount out after Offchain simulation: 10.279845169462854
2025-07-01T00:14:02.829500Z  INFO quedge::helpers: Default start amount for token AS12FW5Rs5YN2zdpEnqwj4iHUUPt9R4Eqjq2qtpJFNKW3mn33RuLU is 10
2025-07-01T00:14:02.829597Z  INFO quedge::helpers: Offchain Earning percentage: 2.798451694628543
2025-07-01T00:14:02.829683Z  INFO quedge::helpers: Offchain Amount out after Offchain simulation: 10.279845169462854
2025-07-01T00:14:02.829773Z  INFO quedge::helpers: Offchain Earning percentage: 2.798451694628543
2025-07-01T00:14:02.983981Z  INFO quedge::helpers: Amount out after Onchain simulation: 20.483090042
2025-07-01T00:14:02.984137Z  INFO quedge::helpers: Earning percentage: 2.415450210000003
2025-07-01T00:14:02.985577Z  INFO quedge::helpers: Amount out after Onchain simulation: 20.483090042
2025-07-01T00:14:02.985713Z  INFO quedge::helpers: Earning percentage: 2.415450210000003
2025-07-01T00:14:02.985925Z  INFO quedge::helpers: Found 2 valid cycles
2025-07-01T00:14:02.986133Z  INFO quedge::executor: Executing arbitrage with start amount 20 for cycle path: ["WMAS-Dusa", "WMAS-EagleFi", "USDC-EagleFi", "USDC-Dusa", "WMAS-Dusa"]...
2025-07-01T00:14:18.354903Z  INFO quedge::spawns::swap_fetcher: Found 2 Swap events from the operation O12S4trx6Xy5uUw8kVwdcL5qqV5kRdhamcGQpdEdAkR6M23Tityn. Processing them...
2025-07-01T00:14:18.355046Z  INFO quedge::spawns::swap_fetcher: New detected swap on pool: AS12dny2u8p6DmcPnYurnzqrAG7kMjYUcSD6bDCdJfbYeadYqXaL5
2025-07-01T00:14:18.395895Z DEBUG quedge::executor: Showing all operation events data for operation id: O12S4trx6Xy5uUw8kVwdcL5qqV5kRdhamcGQpdEdAkR6M23Tityn ...
2025-07-01T00:14:18.396068Z  INFO quedge::executor: Flash Loan Arbitrage executed successfully with start amount 20. Operation id: O12S4trx6Xy5uUw8kVwdcL5qqV5kRdhamcGQpdEdAkR6M23Tityn Cycle path: ["WMAS-Dusa", "WMAS-EagleFi", "USDC-EagleFi", "USDC-Dusa", "WMAS-Dusa"]
2025-07-01T00:14:18.512015Z  INFO quedge::strategies::bellman_ford_matrix: Updating sparse matrix by pool: Pool { address: "AS12dny2u8p6DmcPnYurnzqrAG7kMjYUcSD6bDCdJfbYeadYqXaL5", a_token: Token { address: "AS12N76WPYB3QNYKGhV2jZuQs1djdhNJLQgnm7m52pHWecvvj1fCQ", name: "USD Coin", symbol: "USDC", decimals: 6, dex: EagleFi }, b_token: Token { address: "AS12FW5Rs5YN2zdpEnqwj4iHUUPt9R4Eqjq2qtpJFNKW3mn33RuLU", name: "Wrapped Massa", symbol: "WMAS", decimals: 9, dex: EagleFi }, a_reserve: 3079.200094, b_reserve: 5765.862771648, dex: EagleFi, swap_fee: 0.3, a_price: 1.8725196790176508, b_price: 0.5340397813734131, active_bin_id: 0, bin_step: 0, bins: {} }
2025-07-01T00:14:18.512143Z  WARN quedge::spawns::swap_fetcher: Skipping arbitrage detection after updating the sparse matrix because of an order from the events stream
2025-07-01T00:14:18.512228Z  INFO quedge::spawns::swap_fetcher: Swap event processed successfully
2025-07-01T00:14:18.512311Z DEBUG quedge::spawns::swap_fetcher: Processing last event of the operation O12S4trx6Xy5uUw8kVwdcL5qqV5kRdhamcGQpdEdAkR6M23Tityn...
2025-07-01T00:14:18.512418Z  WARN quedge::spawns::swap_fetcher: Pool AS1VJvdvuU5AZFFKWLD2yxyHsS1CE65imQxQUShqNEf4BqZ2ZHLT not found in the pools hash map
2025-07-01T00:14:18.512503Z  WARN quedge::spawns::swap_fetcher: Pool AS1fewbVp4FMTEJH81e1BNaH9B3SmeE7sNpZQ2QqwP1wGBzLKJGF not found in the pools hash map
2025-07-01T00:14:18.512583Z  WARN quedge::spawns::swap_fetcher: Pool AS1VJvdvuU5AZFFKWLD2yxyHsS1CE65imQxQUShqNEf4BqZ2ZHLT not found in the pools hash map
2025-07-01T00:14:18.512664Z  WARN quedge::spawns::swap_fetcher: Pool AS1XqtvX3rz2RWbnqLfaYVKEjM3VS5pny9yKDdXcmJ5C1vrcLEFd not found in the pools hash map
2025-07-01T00:14:18.512752Z  INFO quedge::spawns::swap_fetcher: New detected swap on pool: AS112Wdy9pM4fvLNLHQXyf7uam9waMPdG5ekr4vxCyQHPkrMMPPY
2025-07-01T00:14:18.822249Z  INFO quedge::strategies::bellman_ford_matrix: Updating sparse matrix by pool: Pool { address: "AS112Wdy9pM4fvLNLHQXyf7uam9waMPdG5ekr4vxCyQHPkrMMPPY", a_token: Token { address: "AS12FW5Rs5YN2zdpEnqwj4iHUUPt9R4Eqjq2qtpJFNKW3mn33RuLU", name: "Wrapped Massa", symbol: "WMAS", decimals: 9, dex: Dusa }, b_token: Token { address: "AS12N76WPYB3QNYKGhV2jZuQs1djdhNJLQgnm7m52pHWecvvj1fCQ", name: "USD Coin", symbol: "USDC", decimals: 6, dex: Dusa }, a_reserve: 10997.992775709, b_reserve: 6028.504665, dex: Dusa, swap_fee: 0.0, a_price: 0.5206450285625964, b_price: 1.9206944177702283, active_bin_id: 8384824, bin_step: 20, bins: {8384823: Bin { id: 8384823, a_reserve: 0.0, b_reserve: 256.595466 }, 8384824: Bin { id: 8384824, a_reserve: 133.795244863, b_reserve: 187.448827 }, 8384825: Bin { id: 8384825, a_reserve: 493.827160493, b_reserve: 0.0 }} }
2025-07-01T00:14:18.822442Z  INFO quedge::spawns::swap_fetcher: Swap event processed successfully
2025-07-01T00:14:18.822660Z DEBUG quedge::helpers: Detecting arbitrage cycles...
2025-07-01T00:14:18.822841Z DEBUG quedge::helpers: Arbitrage cycles found (3 total):
2025-07-01T00:14:18.822947Z DEBUG quedge::helpers: Cycle path Inisde Simulation Task:
2025-07-01T00:14:18.823050Z DEBUG quedge::helpers::display: Path: ["WMAS-EagleFi", "USDC-EagleFi", "USDC-Dusa", "WMAS-Dusa", "WMAS-EagleFi"]
2025-07-01T00:14:18.823173Z DEBUG quedge::helpers: Cycle path Inisde Simulation Task:
2025-07-01T00:14:18.823254Z  INFO quedge::helpers: Default start amount for token AS12FW5Rs5YN2zdpEnqwj4iHUUPt9R4Eqjq2qtpJFNKW3mn33RuLU is 10
2025-07-01T00:14:18.823337Z DEBUG quedge::helpers::display: Path: ["WMAS-Dusa", "WMAS-EagleFi", "USDC-EagleFi", "USDC-Dusa", "WMAS-Dusa"]
2025-07-01T00:14:18.823464Z  INFO quedge::helpers: Offchain Amount out after Offchain simulation: 10.20884789703791
2025-07-01T00:14:18.823549Z DEBUG quedge::helpers: Cycle path Inisde Simulation Task:
2025-07-01T00:14:18.823629Z  INFO quedge::helpers: Default start amount for token AS12FW5Rs5YN2zdpEnqwj4iHUUPt9R4Eqjq2qtpJFNKW3mn33RuLU is 10
2025-07-01T00:14:18.823701Z  INFO quedge::helpers: Offchain Earning percentage: 2.088478970379093
2025-07-01T00:14:18.823767Z DEBUG quedge::helpers::display: Path: ["USDC-Dusa", "WMAS-Dusa", "WMAS-EagleFi", "USDC-EagleFi", "USDC-Dusa"]
2025-07-01T00:14:18.823847Z  INFO quedge::helpers: Offchain Amount out after Offchain simulation: 10.20884789703791
2025-07-01T00:14:18.823974Z  INFO quedge::helpers: Offchain Earning percentage: 2.088478970379093
2025-07-01T00:14:18.982168Z  INFO quedge::helpers: Amount out after Onchain simulation: 20.341744219
2025-07-01T00:14:18.982282Z  INFO quedge::helpers: Earning percentage: 1.7087210949999942
2025-07-01T00:14:18.984924Z  INFO quedge::helpers: Amount out after Onchain simulation: 20.341744219
2025-07-01T00:14:18.985026Z  INFO quedge::helpers: Earning percentage: 1.7087210949999942
2025-07-01T00:14:18.985189Z  INFO quedge::helpers: Found 2 valid cycles
2025-07-01T00:14:18.985360Z  INFO quedge::executor: Executing arbitrage with start amount 20 for cycle path: ["WMAS-Dusa", "WMAS-EagleFi", "USDC-EagleFi", "USDC-Dusa", "WMAS-Dusa"]...
2025-07-01T00:14:34.241564Z  INFO quedge::spawns::swap_fetcher: Found 2 Swap events from the operation O1D9bjrmeMzQC89rMK2NKxvaH6jQqz7ajruqCnt54311t6rntXQ. Processing them...
2025-07-01T00:14:34.241692Z  INFO quedge::spawns::swap_fetcher: New detected swap on pool: AS12dny2u8p6DmcPnYurnzqrAG7kMjYUcSD6bDCdJfbYeadYqXaL5
2025-07-01T00:14:34.399220Z  INFO quedge::strategies::bellman_ford_matrix: Updating sparse matrix by pool: Pool { address: "AS12dny2u8p6DmcPnYurnzqrAG7kMjYUcSD6bDCdJfbYeadYqXaL5", a_token: Token { address: "AS12N76WPYB3QNYKGhV2jZuQs1djdhNJLQgnm7m52pHWecvvj1fCQ", name: "USD Coin", symbol: "USDC", decimals: 6, dex: EagleFi }, b_token: Token { address: "AS12FW5Rs5YN2zdpEnqwj4iHUUPt9R4Eqjq2qtpJFNKW3mn33RuLU", name: "Wrapped Massa", symbol: "WMAS", decimals: 9, dex: EagleFi }, a_reserve: 3068.588041, b_reserve: 5785.847771648, dex: EagleFi, swap_fee: 0.3, a_price: 1.8855081536987586, b_price: 0.5303610053546163, active_bin_id: 0, bin_step: 0, bins: {} }
2025-07-01T00:14:34.399336Z  WARN quedge::spawns::swap_fetcher: Skipping arbitrage detection after updating the sparse matrix because of an order from the events stream
2025-07-01T00:14:34.399412Z  INFO quedge::spawns::swap_fetcher: Swap event processed successfully
2025-07-01T00:14:34.399489Z DEBUG quedge::spawns::swap_fetcher: Processing last event of the operation O1D9bjrmeMzQC89rMK2NKxvaH6jQqz7ajruqCnt54311t6rntXQ...
2025-07-01T00:14:34.399562Z  WARN quedge::spawns::swap_fetcher: Pool AS1VJvdvuU5AZFFKWLD2yxyHsS1CE65imQxQUShqNEf4BqZ2ZHLT not found in the pools hash map
2025-07-01T00:14:34.399640Z  WARN quedge::spawns::swap_fetcher: Pool AS1fewbVp4FMTEJH81e1BNaH9B3SmeE7sNpZQ2QqwP1wGBzLKJGF not found in the pools hash map
2025-07-01T00:14:34.399711Z  WARN quedge::spawns::swap_fetcher: Pool AS1VJvdvuU5AZFFKWLD2yxyHsS1CE65imQxQUShqNEf4BqZ2ZHLT not found in the pools hash map
2025-07-01T00:14:34.399780Z  WARN quedge::spawns::swap_fetcher: Pool AS1XqtvX3rz2RWbnqLfaYVKEjM3VS5pny9yKDdXcmJ5C1vrcLEFd not found in the pools hash map
2025-07-01T00:14:34.399858Z  INFO quedge::spawns::swap_fetcher: New detected swap on pool: AS112Wdy9pM4fvLNLHQXyf7uam9waMPdG5ekr4vxCyQHPkrMMPPY
2025-07-01T00:14:34.526778Z DEBUG quedge::executor: Showing all operation events data for operation id: O1D9bjrmeMzQC89rMK2NKxvaH6jQqz7ajruqCnt54311t6rntXQ ...
2025-07-01T00:14:34.526977Z  INFO quedge::executor: Flash Loan Arbitrage executed successfully with start amount 20. Operation id: O1D9bjrmeMzQC89rMK2NKxvaH6jQqz7ajruqCnt54311t6rntXQ Cycle path: ["WMAS-Dusa", "WMAS-EagleFi", "USDC-EagleFi", "USDC-Dusa", "WMAS-Dusa"]
2025-07-01T00:14:34.701949Z  INFO quedge::strategies::bellman_ford_matrix: Updating sparse matrix by pool: Pool { address: "AS112Wdy9pM4fvLNLHQXyf7uam9waMPdG5ekr4vxCyQHPkrMMPPY", a_token: Token { address: "AS12FW5Rs5YN2zdpEnqwj4iHUUPt9R4Eqjq2qtpJFNKW3mn33RuLU", name: "Wrapped Massa", symbol: "WMAS", decimals: 9, dex: Dusa }, b_token: Token { address: "AS12N76WPYB3QNYKGhV2jZuQs1djdhNJLQgnm7m52pHWecvvj1fCQ", name: "USD Coin", symbol: "USDC", decimals: 6, dex: Dusa }, a_reserve: 10977.65103149, b_reserve: 6039.095493, dex: Dusa, swap_fee: 0.0, a_price: 0.5206450285625964, b_price: 1.9206944177702283, active_bin_id: 8384824, bin_step: 20, bins: {8384823: Bin { id: 8384823, a_reserve: 0.0, b_reserve: 256.595466 }, 8384824: Bin { id: 8384824, a_reserve: 113.453500644, b_reserve: 198.039655 }, 8384825: Bin { id: 8384825, a_reserve: 493.827160493, b_reserve: 0.0 }} }
2025-07-01T00:14:34.702161Z  INFO quedge::spawns::swap_fetcher: Swap event processed successfully
2025-07-01T00:14:34.702418Z DEBUG quedge::helpers: Detecting arbitrage cycles...
2025-07-01T00:14:34.702594Z DEBUG quedge::helpers: Arbitrage cycles found (3 total):
2025-07-01T00:14:34.702700Z DEBUG quedge::helpers: Cycle path Inisde Simulation Task:
2025-07-01T00:14:34.702799Z DEBUG quedge::helpers::display: Path: ["WMAS-EagleFi", "USDC-EagleFi", "USDC-Dusa", "WMAS-Dusa", "WMAS-EagleFi"]
2025-07-01T00:14:34.702933Z DEBUG quedge::helpers: Cycle path Inisde Simulation Task:
2025-07-01T00:14:34.703011Z  INFO quedge::helpers: Default start amount for token AS12FW5Rs5YN2zdpEnqwj4iHUUPt9R4Eqjq2qtpJFNKW3mn33RuLU is 10
2025-07-01T00:14:34.703086Z DEBUG quedge::helpers::display: Path: ["WMAS-Dusa", "WMAS-EagleFi", "USDC-EagleFi", "USDC-Dusa", "WMAS-Dusa"]
2025-07-01T00:14:34.703165Z  INFO quedge::helpers: Offchain Amount out after Offchain simulation: 10.13858387640781
2025-07-01T00:14:34.703244Z  INFO quedge::helpers: Default start amount for token AS12FW5Rs5YN2zdpEnqwj4iHUUPt9R4Eqjq2qtpJFNKW3mn33RuLU is 10
2025-07-01T00:14:34.703317Z DEBUG quedge::helpers: Cycle path Inisde Simulation Task:
2025-07-01T00:14:34.703391Z  INFO quedge::helpers: Offchain Earning percentage: 1.3858387640780911
2025-07-01T00:14:34.703462Z  INFO quedge::helpers: Offchain Amount out after Offchain simulation: 10.13858387640781
2025-07-01T00:14:34.703597Z DEBUG quedge::helpers::display: Path: ["USDC-Dusa", "WMAS-Dusa", "WMAS-EagleFi", "USDC-EagleFi", "USDC-Dusa"]
2025-07-01T00:14:34.703730Z  INFO quedge::helpers: Offchain Earning percentage: 1.3858387640780911
2025-07-01T00:14:34.855769Z  INFO quedge::helpers: Amount out after Onchain simulation: 20.201860044
2025-07-01T00:14:34.855959Z  INFO quedge::helpers: Earning percentage: 1.00930022
2025-07-01T00:14:34.857644Z  INFO quedge::helpers: Amount out after Onchain simulation: 20.201860044
2025-07-01T00:14:34.857778Z  INFO quedge::helpers: Earning percentage: 1.00930022
2025-07-01T00:14:34.857925Z  INFO quedge::helpers: Found 2 valid cycles
2025-07-01T00:14:34.858123Z  INFO quedge::executor: Executing arbitrage with start amount 20 for cycle path: ["WMAS-Dusa", "WMAS-EagleFi", "USDC-EagleFi", "USDC-Dusa", "WMAS-Dusa"]...
2025-07-01T00:14:50.361829Z  INFO quedge::spawns::swap_fetcher: Found 2 Swap events from the operation O1QKp3icCWbUSghzLM9r5omuuPbK81rR4sgyUBRYjEv1qhFgJeT. Processing them...
2025-07-01T00:14:50.361980Z  INFO quedge::spawns::swap_fetcher: New detected swap on pool: AS12dny2u8p6DmcPnYurnzqrAG7kMjYUcSD6bDCdJfbYeadYqXaL5
2025-07-01T00:14:50.509373Z DEBUG quedge::executor: Showing all operation events data for operation id: O1QKp3icCWbUSghzLM9r5omuuPbK81rR4sgyUBRYjEv1qhFgJeT ...
2025-07-01T00:14:50.509562Z  INFO quedge::executor: Flash Loan Arbitrage executed successfully with start amount 20. Operation id: O1QKp3icCWbUSghzLM9r5omuuPbK81rR4sgyUBRYjEv1qhFgJeT Cycle path: ["WMAS-Dusa", "WMAS-EagleFi", "USDC-EagleFi", "USDC-Dusa", "WMAS-Dusa"]
2025-07-01T00:14:50.511551Z  INFO quedge::strategies::bellman_ford_matrix: Updating sparse matrix by pool: Pool { address: "AS12dny2u8p6DmcPnYurnzqrAG7kMjYUcSD6bDCdJfbYeadYqXaL5", a_token: Token { address: "AS12N76WPYB3QNYKGhV2jZuQs1djdhNJLQgnm7m52pHWecvvj1fCQ", name: "USD Coin", symbol: "USDC", decimals: 6, dex: EagleFi }, b_token: Token { address: "AS12FW5Rs5YN2zdpEnqwj4iHUUPt9R4Eqjq2qtpJFNKW3mn33RuLU", name: "Wrapped Massa", symbol: "WMAS", decimals: 9, dex: EagleFi }, a_reserve: 3058.048964, b_reserve: 5805.832771648, dex: EagleFi, swap_fee: 0.3, a_price: 1.89854146875851, b_price: 0.5267201251358063, active_bin_id: 0, bin_step: 0, bins: {} }
2025-07-01T00:14:50.511681Z  WARN quedge::spawns::swap_fetcher: Skipping arbitrage detection after updating the sparse matrix because of an order from the events stream
2025-07-01T00:14:50.511769Z  INFO quedge::spawns::swap_fetcher: Swap event processed successfully
2025-07-01T00:14:50.511857Z DEBUG quedge::spawns::swap_fetcher: Processing last event of the operation O1QKp3icCWbUSghzLM9r5omuuPbK81rR4sgyUBRYjEv1qhFgJeT...
2025-07-01T00:14:50.511939Z  WARN quedge::spawns::swap_fetcher: Pool AS1VJvdvuU5AZFFKWLD2yxyHsS1CE65imQxQUShqNEf4BqZ2ZHLT not found in the pools hash map
2025-07-01T00:14:50.512020Z  WARN quedge::spawns::swap_fetcher: Pool AS1fewbVp4FMTEJH81e1BNaH9B3SmeE7sNpZQ2QqwP1wGBzLKJGF not found in the pools hash map
2025-07-01T00:14:50.512097Z  WARN quedge::spawns::swap_fetcher: Pool AS1VJvdvuU5AZFFKWLD2yxyHsS1CE65imQxQUShqNEf4BqZ2ZHLT not found in the pools hash map
2025-07-01T00:14:50.512168Z  WARN quedge::spawns::swap_fetcher: Pool AS1XqtvX3rz2RWbnqLfaYVKEjM3VS5pny9yKDdXcmJ5C1vrcLEFd not found in the pools hash map
2025-07-01T00:14:50.512249Z  INFO quedge::spawns::swap_fetcher: New detected swap on pool: AS112Wdy9pM4fvLNLHQXyf7uam9waMPdG5ekr4vxCyQHPkrMMPPY
2025-07-01T00:14:50.855952Z  INFO quedge::strategies::bellman_ford_matrix: Updating sparse matrix by pool: Pool { address: "AS112Wdy9pM4fvLNLHQXyf7uam9waMPdG5ekr4vxCyQHPkrMMPPY", a_token: Token { address: "AS12FW5Rs5YN2zdpEnqwj4iHUUPt9R4Eqjq2qtpJFNKW3mn33RuLU", name: "Wrapped Massa", symbol: "WMAS", decimals: 9, dex: Dusa }, b_token: Token { address: "AS12N76WPYB3QNYKGhV2jZuQs1djdhNJLQgnm7m52pHWecvvj1fCQ", name: "USD Coin", symbol: "USDC", decimals: 6, dex: Dusa }, a_reserve: 10957.449171446, b_reserve: 6049.613491, dex: Dusa, swap_fee: 0.0, a_price: 0.5206450285625964, b_price: 1.9206944177702283, active_bin_id: 8384824, bin_step: 20, bins: {8384823: Bin { id: 8384823, a_reserve: 0.0, b_reserve: 256.595466 }, 8384824: Bin { id: 8384824, a_reserve: 93.2516406, b_reserve: 208.557653 }, 8384825: Bin { id: 8384825, a_reserve: 493.827160493, b_reserve: 0.0 }} }
2025-07-01T00:14:50.856140Z  INFO quedge::spawns::swap_fetcher: Swap event processed successfully
2025-07-01T00:14:50.856368Z DEBUG quedge::helpers: Detecting arbitrage cycles...
2025-07-01T00:14:50.856613Z DEBUG quedge::helpers: Arbitrage cycles found (3 total):
2025-07-01T00:14:50.856757Z DEBUG quedge::helpers: Cycle path Inisde Simulation Task:
2025-07-01T00:14:50.856851Z DEBUG quedge::helpers::display: Path: ["WMAS-EagleFi", "USDC-EagleFi", "USDC-Dusa", "WMAS-Dusa", "WMAS-EagleFi"]
2025-07-01T00:14:50.856969Z DEBUG quedge::helpers: Cycle path Inisde Simulation Task:
2025-07-01T00:14:50.857060Z  INFO quedge::helpers: Default start amount for token AS12FW5Rs5YN2zdpEnqwj4iHUUPt9R4Eqjq2qtpJFNKW3mn33RuLU is 10
2025-07-01T00:14:50.857132Z DEBUG quedge::helpers::display: Path: ["WMAS-Dusa", "WMAS-EagleFi", "USDC-EagleFi", "USDC-Dusa", "WMAS-Dusa"]
2025-07-01T00:14:50.857225Z  INFO quedge::helpers: Offchain Amount out after Offchain simulation: 10.069043039504903
2025-07-01T00:14:50.857301Z DEBUG quedge::helpers: Cycle path Inisde Simulation Task:
2025-07-01T00:14:50.857379Z  INFO quedge::helpers: Default start amount for token AS12FW5Rs5YN2zdpEnqwj4iHUUPt9R4Eqjq2qtpJFNKW3mn33RuLU is 10
2025-07-01T00:14:50.857452Z  INFO quedge::helpers: Offchain Earning percentage: 0.6904303950490309
2025-07-01T00:14:50.857534Z DEBUG quedge::helpers::display: Path: ["USDC-Dusa", "WMAS-Dusa", "WMAS-EagleFi", "USDC-EagleFi", "USDC-Dusa"]
2025-07-01T00:14:50.857606Z  INFO quedge::helpers: Offchain Amount out after Offchain simulation: 10.069043039504903
2025-07-01T00:14:50.857706Z  INFO quedge::helpers: Offchain Earning percentage: 0.6904303950490309
2025-07-01T00:14:51.013037Z  INFO quedge::helpers: Amount out after Onchain simulation: 10.048904172
2025-07-01T00:14:51.013168Z  INFO quedge::helpers: Earning percentage: 0.48904172000000296
2025-07-01T00:14:51.014774Z  INFO quedge::helpers: Amount out after Onchain simulation: 10.048904172
2025-07-01T00:14:51.014855Z  INFO quedge::helpers: Earning percentage: 0.48904172000000296
2025-07-01T00:14:51.014995Z  INFO quedge::helpers: Found 2 valid cycles
2025-07-01T00:14:51.015150Z  INFO quedge::executor: Executing arbitrage with start amount 10 for cycle path: ["WMAS-Dusa", "WMAS-EagleFi", "USDC-EagleFi", "USDC-Dusa", "WMAS-Dusa"]...
2025-07-01T00:15:06.393086Z  INFO quedge::spawns::swap_fetcher: Found 2 Swap events from the operation O12CLWwKJGGCBBwc6vzcezfxVFmoWQsui6bq75WeuvE7REw7jYVA. Processing them...
2025-07-01T00:15:06.393533Z  INFO quedge::spawns::swap_fetcher: New detected swap on pool: AS12dny2u8p6DmcPnYurnzqrAG7kMjYUcSD6bDCdJfbYeadYqXaL5
2025-07-01T00:15:06.539379Z  INFO quedge::strategies::bellman_ford_matrix: Updating sparse matrix by pool: Pool { address: "AS12dny2u8p6DmcPnYurnzqrAG7kMjYUcSD6bDCdJfbYeadYqXaL5", a_token: Token { address: "AS12N76WPYB3QNYKGhV2jZuQs1djdhNJLQgnm7m52pHWecvvj1fCQ", name: "USD Coin", symbol: "USDC", decimals: 6, dex: EagleFi }, b_token: Token { address: "AS12FW5Rs5YN2zdpEnqwj4iHUUPt9R4Eqjq2qtpJFNKW3mn33RuLU", name: "Wrapped Massa", symbol: "WMAS", decimals: 9, dex: EagleFi }, a_reserve: 3052.806567, b_reserve: 5815.825271648, dex: EagleFi, swap_fee: 0.3, a_price: 1.9050749348207885, b_price: 0.5249137352668338, active_bin_id: 0, bin_step: 0, bins: {} }
2025-07-01T00:15:06.539567Z  WARN quedge::spawns::swap_fetcher: Skipping arbitrage detection after updating the sparse matrix because of an order from the events stream
2025-07-01T00:15:06.539691Z  INFO quedge::spawns::swap_fetcher: Swap event processed successfully
2025-07-01T00:15:06.539814Z DEBUG quedge::spawns::swap_fetcher: Processing last event of the operation O12CLWwKJGGCBBwc6vzcezfxVFmoWQsui6bq75WeuvE7REw7jYVA...
2025-07-01T00:15:06.539923Z  WARN quedge::spawns::swap_fetcher: Pool AS1VJvdvuU5AZFFKWLD2yxyHsS1CE65imQxQUShqNEf4BqZ2ZHLT not found in the pools hash map
2025-07-01T00:15:06.540012Z  WARN quedge::spawns::swap_fetcher: Pool AS1fewbVp4FMTEJH81e1BNaH9B3SmeE7sNpZQ2QqwP1wGBzLKJGF not found in the pools hash map
2025-07-01T00:15:06.540106Z  WARN quedge::spawns::swap_fetcher: Pool AS1VJvdvuU5AZFFKWLD2yxyHsS1CE65imQxQUShqNEf4BqZ2ZHLT not found in the pools hash map
2025-07-01T00:15:06.540185Z  WARN quedge::spawns::swap_fetcher: Pool AS1XqtvX3rz2RWbnqLfaYVKEjM3VS5pny9yKDdXcmJ5C1vrcLEFd not found in the pools hash map
2025-07-01T00:15:06.540272Z  INFO quedge::spawns::swap_fetcher: New detected swap on pool: AS112Wdy9pM4fvLNLHQXyf7uam9waMPdG5ekr4vxCyQHPkrMMPPY
2025-07-01T00:15:06.560672Z DEBUG quedge::executor: Showing all operation events data for operation id: O12CLWwKJGGCBBwc6vzcezfxVFmoWQsui6bq75WeuvE7REw7jYVA ...
2025-07-01T00:15:06.560913Z  INFO quedge::executor: Flash Loan Arbitrage executed successfully with start amount 10. Operation id: O12CLWwKJGGCBBwc6vzcezfxVFmoWQsui6bq75WeuvE7REw7jYVA Cycle path: ["WMAS-Dusa", "WMAS-EagleFi", "USDC-EagleFi", "USDC-Dusa", "WMAS-Dusa"]
2025-07-01T00:15:06.842528Z  INFO quedge::strategies::bellman_ford_matrix: Updating sparse matrix by pool: Pool { address: "AS112Wdy9pM4fvLNLHQXyf7uam9waMPdG5ekr4vxCyQHPkrMMPPY", a_token: Token { address: "AS12FW5Rs5YN2zdpEnqwj4iHUUPt9R4Eqjq2qtpJFNKW3mn33RuLU", name: "Wrapped Massa", symbol: "WMAS", decimals: 9, dex: Dusa }, b_token: Token { address: "AS12N76WPYB3QNYKGhV2jZuQs1djdhNJLQgnm7m52pHWecvvj1fCQ", name: "USD Coin", symbol: "USDC", decimals: 6, dex: Dusa }, a_reserve: 10947.400267274, b_reserve: 6054.845403, dex: Dusa, swap_fee: 0.0, a_price: 0.5206450285625964, b_price: 1.9206944177702283, active_bin_id: 8384824, bin_step: 20, bins: {8384823: Bin { id: 8384823, a_reserve: 0.0, b_reserve: 256.595466 }, 8384824: Bin { id: 8384824, a_reserve: 83.202736428, b_reserve: 213.789565 }, 8384825: Bin { id: 8384825, a_reserve: 493.827160493, b_reserve: 0.0 }} }
2025-07-01T00:15:06.842788Z  INFO quedge::spawns::swap_fetcher: Swap event processed successfully
2025-07-01T00:15:06.843090Z DEBUG quedge::helpers: Detecting arbitrage cycles...
2025-07-01T00:15:06.843295Z DEBUG quedge::helpers: Arbitrage cycles found (3 total):
2025-07-01T00:15:06.843414Z DEBUG quedge::helpers: Cycle path Inisde Simulation Task:
2025-07-01T00:15:06.843524Z DEBUG quedge::helpers::display: Path: ["WMAS-EagleFi", "USDC-EagleFi", "USDC-Dusa", "WMAS-Dusa", "WMAS-EagleFi"]
2025-07-01T00:15:06.843667Z DEBUG quedge::helpers: Cycle path Inisde Simulation Task:
2025-07-01T00:15:06.843752Z  INFO quedge::helpers: Default start amount for token AS12FW5Rs5YN2zdpEnqwj4iHUUPt9R4Eqjq2qtpJFNKW3mn33RuLU is 10
2025-07-01T00:15:06.843834Z DEBUG quedge::helpers::display: Path: ["WMAS-Dusa", "WMAS-EagleFi", "USDC-EagleFi", "USDC-Dusa", "WMAS-Dusa"]
2025-07-01T00:15:06.843938Z  INFO quedge::helpers: Offchain Amount out after Offchain simulation: 10.03454075124098
2025-07-01T00:15:06.844027Z DEBUG quedge::helpers: Cycle path Inisde Simulation Task:
2025-07-01T00:15:06.844111Z  INFO quedge::helpers: Default start amount for token AS12FW5Rs5YN2zdpEnqwj4iHUUPt9R4Eqjq2qtpJFNKW3mn33RuLU is 10
2025-07-01T00:15:06.844244Z  INFO quedge::helpers: Offchain Earning percentage: 0.3454075124098033
2025-07-01T00:15:06.844319Z DEBUG quedge::helpers::display: Path: ["USDC-Dusa", "WMAS-Dusa", "WMAS-EagleFi", "USDC-EagleFi", "USDC-Dusa"]
2025-07-01T00:15:06.844406Z  INFO quedge::helpers: Offchain Amount out after Offchain simulation: 10.03454075124098
2025-07-01T00:15:06.844502Z  INFO quedge::helpers: Offchain Earning percentage: 0.3454075124098033
2025-07-01T00:15:07.021583Z  INFO quedge::helpers: Amount out after Onchain simulation: 10.014469963
2025-07-01T00:15:07.021669Z  INFO quedge::helpers: Amount out after Onchain simulation: 10.014469963
2025-07-01T00:15:07.021758Z  INFO quedge::helpers: Earning percentage: 0.14469962999999808
2025-07-01T00:15:07.021847Z  INFO quedge::helpers: Earning percentage: 0.14469962999999808
2025-07-01T00:15:07.021986Z  INFO quedge::helpers: Found 2 valid cycles
2025-07-01T00:15:07.022168Z  INFO quedge::executor: Executing arbitrage with start amount 10 for cycle path: ["WMAS-Dusa", "WMAS-EagleFi", "USDC-EagleFi", "USDC-Dusa", "WMAS-Dusa"]...
2025-07-01T00:15:22.365508Z  INFO quedge::spawns::swap_fetcher: Found 2 Swap events from the operation O1Ev9rWR1yhimK8SAh8Ygt6vdqwqAoeshyHi8MCCHeTAHq9wt9D. Processing them...
2025-07-01T00:15:22.365645Z  INFO quedge::spawns::swap_fetcher: New detected swap on pool: AS12dny2u8p6DmcPnYurnzqrAG7kMjYUcSD6bDCdJfbYeadYqXaL5
2025-07-01T00:15:22.522505Z DEBUG quedge::executor: Showing all operation events data for operation id: O1Ev9rWR1yhimK8SAh8Ygt6vdqwqAoeshyHi8MCCHeTAHq9wt9D ...
2025-07-01T00:15:22.522719Z  INFO quedge::executor: Flash Loan Arbitrage executed successfully with start amount 10. Operation id: O1Ev9rWR1yhimK8SAh8Ygt6vdqwqAoeshyHi8MCCHeTAHq9wt9D Cycle path: ["WMAS-Dusa", "WMAS-EagleFi", "USDC-EagleFi", "USDC-Dusa", "WMAS-Dusa"]
2025-07-01T00:15:22.535140Z  INFO quedge::strategies::bellman_ford_matrix: Updating sparse matrix by pool: Pool { address: "AS12dny2u8p6DmcPnYurnzqrAG7kMjYUcSD6bDCdJfbYeadYqXaL5", a_token: Token { address: "AS12N76WPYB3QNYKGhV2jZuQs1djdhNJLQgnm7m52pHWecvvj1fCQ", name: "USD Coin", symbol: "USDC", decimals: 6, dex: EagleFi }, b_token: Token { address: "AS12FW5Rs5YN2zdpEnqwj4iHUUPt9R4Eqjq2qtpJFNKW3mn33RuLU", name: "Wrapped Massa", symbol: "WMAS", decimals: 9, dex: EagleFi }, a_reserve: 3047.582134, b_reserve: 5825.817771648, dex: EagleFi, swap_fee: 0.3, a_price: 1.911619610396364, b_price: 0.5231166255888405, active_bin_id: 0, bin_step: 0, bins: {} }
2025-07-01T00:15:22.535289Z  WARN quedge::spawns::swap_fetcher: Skipping arbitrage detection after updating the sparse matrix because of an order from the events stream
2025-07-01T00:15:22.535383Z  INFO quedge::spawns::swap_fetcher: Swap event processed successfully
2025-07-01T00:15:22.535472Z DEBUG quedge::spawns::swap_fetcher: Processing last event of the operation O1Ev9rWR1yhimK8SAh8Ygt6vdqwqAoeshyHi8MCCHeTAHq9wt9D...
2025-07-01T00:15:22.535555Z  WARN quedge::spawns::swap_fetcher: Pool AS1VJvdvuU5AZFFKWLD2yxyHsS1CE65imQxQUShqNEf4BqZ2ZHLT not found in the pools hash map
2025-07-01T00:15:22.535634Z  WARN quedge::spawns::swap_fetcher: Pool AS1fewbVp4FMTEJH81e1BNaH9B3SmeE7sNpZQ2QqwP1wGBzLKJGF not found in the pools hash map
2025-07-01T00:15:22.535721Z  WARN quedge::spawns::swap_fetcher: Pool AS1VJvdvuU5AZFFKWLD2yxyHsS1CE65imQxQUShqNEf4BqZ2ZHLT not found in the pools hash map
2025-07-01T00:15:22.535809Z  WARN quedge::spawns::swap_fetcher: Pool AS1XqtvX3rz2RWbnqLfaYVKEjM3VS5pny9yKDdXcmJ5C1vrcLEFd not found in the pools hash map
2025-07-01T00:15:22.535895Z  INFO quedge::spawns::swap_fetcher: New detected swap on pool: AS112Wdy9pM4fvLNLHQXyf7uam9waMPdG5ekr4vxCyQHPkrMMPPY
2025-07-01T00:15:22.840091Z  INFO quedge::strategies::bellman_ford_matrix: Updating sparse matrix by pool: Pool { address: "AS112Wdy9pM4fvLNLHQXyf7uam9waMPdG5ekr4vxCyQHPkrMMPPY", a_token: Token { address: "AS12FW5Rs5YN2zdpEnqwj4iHUUPt9R4Eqjq2qtpJFNKW3mn33RuLU", name: "Wrapped Massa", symbol: "WMAS", decimals: 9, dex: Dusa }, b_token: Token { address: "AS12N76WPYB3QNYKGhV2jZuQs1djdhNJLQgnm7m52pHWecvvj1fCQ", name: "USD Coin", symbol: "USDC", decimals: 6, dex: Dusa }, a_reserve: 10937.385797311, b_reserve: 6060.059387, dex: Dusa, swap_fee: 0.0, a_price: 0.5206450285625964, b_price: 1.9206944177702283, active_bin_id: 8384824, bin_step: 20, bins: {8384823: Bin { id: 8384823, a_reserve: 0.0, b_reserve: 256.595466 }, 8384824: Bin { id: 8384824, a_reserve: 73.188266465, b_reserve: 219.003549 }, 8384825: Bin { id: 8384825, a_reserve: 493.827160493, b_reserve: 0.0 }} }
2025-07-01T00:15:22.840281Z  INFO quedge::spawns::swap_fetcher: Swap event processed successfully
2025-07-01T00:15:22.840474Z DEBUG quedge::helpers: Detecting arbitrage cycles...
2025-07-01T00:15:22.840642Z DEBUG quedge::helpers: Arbitrage cycles found (3 total):
2025-07-01T00:15:22.840732Z DEBUG quedge::helpers: Cycle path Inisde Simulation Task:
2025-07-01T00:15:22.840813Z DEBUG quedge::helpers::display: Path: ["WMAS-EagleFi", "USDC-EagleFi", "USDC-Dusa", "WMAS-Dusa", "WMAS-EagleFi"]
2025-07-01T00:15:22.840904Z DEBUG quedge::helpers: Cycle path Inisde Simulation Task:
2025-07-01T00:15:22.840974Z DEBUG quedge::helpers::display: Path: ["WMAS-Dusa", "WMAS-EagleFi", "USDC-EagleFi", "USDC-Dusa", "WMAS-Dusa"]
2025-07-01T00:15:22.841100Z DEBUG quedge::helpers: Cycle path Inisde Simulation Task:
2025-07-01T00:15:22.841183Z  INFO quedge::helpers: Default start amount for token AS12FW5Rs5YN2zdpEnqwj4iHUUPt9R4Eqjq2qtpJFNKW3mn33RuLU is 10
2025-07-01T00:15:22.841260Z DEBUG quedge::helpers::display: Path: ["USDC-Dusa", "WMAS-Dusa", "WMAS-EagleFi", "USDC-EagleFi", "USDC-Dusa"]
2025-07-01T00:15:22.841369Z  INFO quedge::helpers: Offchain Amount out after Offchain simulation: 10.000215564461204
2025-07-01T00:15:22.841458Z  INFO quedge::helpers: Default start amount for token AS12FW5Rs5YN2zdpEnqwj4iHUUPt9R4Eqjq2qtpJFNKW3mn33RuLU is 10
2025-07-01T00:15:22.841524Z  INFO quedge::helpers: Offchain Earning percentage: 0.002155644612038543
2025-07-01T00:15:22.841589Z  INFO quedge::helpers: Offchain Amount out after Offchain simulation: 10.000215564461204
2025-07-01T00:15:22.841654Z  WARN quedge::helpers: Not Valid Cycle Because Offchain Earning Percentage is less than margin 0.002155644612038543 < 0.1
2025-07-01T00:15:22.841712Z  INFO quedge::helpers: Offchain Earning percentage: 0.002155644612038543
2025-07-01T00:15:22.841786Z  WARN quedge::helpers: Not Valid Cycle Because Offchain Earning Percentage is less than margin 0.002155644612038543 < 0.1
2025-07-01T00:15:22.841876Z  INFO quedge::helpers: Found 0 valid cycles
2025-07-01T00:15:22.841953Z  WARN quedge::helpers: No profitable arbitrage cycle found.
2025-07-01T07:48:59.177978Z  INFO quedge: Logger initialized Successfully
2025-07-01T07:49:07.028277Z  INFO quedge: Allowed start tokens: ["AS125oPLYRTtfVjpWisPZVTLjBhCFfQ1jDsi75XNtRm1NZux54eCj", "AS124vf3YfAJCSCQVYKczzuWWpXrximFpbTmX4rheLs5uNSftiiRY", "AS12U4TZfNK7qoLyEERBBRDMu8nm5MKoRzPXDXans4v9wdATZedz9", "AS12fr54YtBY575Dfhtt7yftpT8KXgXb1ia5Pn1LofoLFLf9WcjGL", "AS12LKs9txoSSy8JgFJgV96m8k5z9pgzjYMYSshwN67mFVuj3bdUV", "AS1nqHKXpnFXqhDExTskXmBbbVpVpUbCQVtNSXLCqUDSUXihdWRq", "AS1hCJXjndR4c9vekLWsXGnrdigp4AaZ7uYG3UKFzzKnWVsrNLPJ", "AS133eqPPaPttJ6hJnk3sfoG5cjFFqBDi1VGxdo2wzWkq8AfZnan"]
2025-07-01T07:49:07.028807Z  INFO quedge: Exporting the sparse matrix to a png file...
2025-07-01T07:49:10.441007Z  INFO quedge: Exported the sparse matrix to a png file successfully!
2025-07-01T07:49:10.441385Z  INFO quedge::spawns::swap_fetcher: Swap event fetcher working in candidate state
2025-07-01T07:49:10.441498Z DEBUG quedge::helpers: Detecting arbitrage cycles...
2025-07-01T07:49:10.458453Z DEBUG quedge::helpers: Arbitrage cycles found (7 total):
2025-07-01T07:49:10.458793Z DEBUG quedge::helpers: Cycle path Inisde Simulation Task:
2025-07-01T07:49:10.458938Z DEBUG quedge::helpers::display: Path: ["POM-Dusa", "WMAS-Dusa", "USDC.e-Dusa", "USDC.e-EagleFi", "WMAS-EagleFi", "POM-EagleFi", "POM-Dusa"]
2025-07-01T07:49:10.459152Z DEBUG quedge::helpers: Cycle path Inisde Simulation Task:
2025-07-01T07:49:10.459270Z DEBUG quedge::helpers::display: Path: ["USDC.e-Dusa", "PUR-Dusa", "PUR-EagleFi", "WMAS-EagleFi", "POM-EagleFi", "POM-Dusa", "WMAS-Dusa", "USDC.e-Dusa"]
2025-07-01T07:49:10.459465Z DEBUG quedge::helpers: Cycle path Inisde Simulation Task:
2025-07-01T07:49:10.459607Z DEBUG quedge::helpers::display: Path: ["POM-EagleFi", "POM-Dusa", "WMAS-Dusa", "USDC.e-Dusa", "PUR-Dusa", "PUR-EagleFi", "WMAS-EagleFi", "POM-EagleFi"]
2025-07-01T07:49:10.459712Z  INFO quedge::helpers: Default start amount for token AS1nqHKXpnFXqhDExTskXmBbbVpVpUbCQVtNSXLCqUDSUXihdWRq is 40207
2025-07-01T07:49:10.459816Z DEBUG quedge::helpers: Cycle path Inisde Simulation Task:
2025-07-01T07:49:10.459904Z  INFO quedge::helpers: Offchain Amount out after Offchain simulation: 0.00007765593260236748
2025-07-01T07:49:10.459995Z  INFO quedge::helpers: Default start amount for token AS1hCJXjndR4c9vekLWsXGnrdigp4AaZ7uYG3UKFzzKnWVsrNLPJ is 1
2025-07-01T07:49:10.460083Z DEBUG quedge::helpers::display: Path: ["POM-Dusa", "WMAS-Dusa", "USDC.e-Dusa", "PUR-Dusa", "PUR-EagleFi", "WMAS-EagleFi", "POM-EagleFi", "POM-Dusa"]
2025-07-01T07:49:10.460188Z  INFO quedge::helpers: Offchain Earning percentage: -99.99999980685968
2025-07-01T07:49:10.460276Z  INFO quedge::helpers: Offchain Amount out after Offchain simulation: 0.00000000021831144267020036
2025-07-01T07:49:10.460383Z DEBUG quedge::helpers: Cycle path Inisde Simulation Task:
2025-07-01T07:49:10.460478Z  WARN quedge::helpers: Not Valid Cycle Because Offchain Earning Percentage is less than margin -99.99999980685968 < 0.1
2025-07-01T07:49:10.460575Z  INFO quedge::helpers: Default start amount for token AS1nqHKXpnFXqhDExTskXmBbbVpVpUbCQVtNSXLCqUDSUXihdWRq is 40207
2025-07-01T07:49:10.460662Z  INFO quedge::helpers: Offchain Earning percentage: -99.99999997816886
2025-07-01T07:49:10.460758Z DEBUG quedge::helpers::display: Path: ["WMAS-Dusa", "USDC.e-Dusa", "PUR-Dusa", "PUR-EagleFi", "WMAS-EagleFi", "POM-EagleFi", "POM-Dusa", "WMAS-Dusa"]
2025-07-01T07:49:10.460865Z  INFO quedge::helpers: Offchain Amount out after Offchain simulation: 0.0000776559326078874
2025-07-01T07:49:10.460970Z  WARN quedge::helpers: Not Valid Cycle Because Offchain Earning Percentage is less than margin -99.99999997816886 < 0.1
2025-07-01T07:49:10.461066Z DEBUG quedge::helpers: Cycle path Inisde Simulation Task:
2025-07-01T07:49:10.461160Z  INFO quedge::helpers: Offchain Earning percentage: -99.99999980685968
2025-07-01T07:49:10.461255Z  INFO quedge::helpers: Default start amount for token AS1nqHKXpnFXqhDExTskXmBbbVpVpUbCQVtNSXLCqUDSUXihdWRq is 40207
2025-07-01T07:49:10.461353Z DEBUG quedge::helpers::display: Path: ["PUR-EagleFi", "WMAS-EagleFi", "POM-EagleFi", "POM-Dusa", "WMAS-Dusa", "USDC.e-Dusa", "PUR-Dusa", "PUR-EagleFi"]
2025-07-01T07:49:10.461449Z  WARN quedge::helpers: Not Valid Cycle Because Offchain Earning Percentage is less than margin -99.99999980685968 < 0.1
2025-07-01T07:49:10.461540Z  INFO quedge::helpers: Offchain Amount out after Offchain simulation: 0.0000776559326078874
2025-07-01T07:49:10.461635Z  INFO quedge::helpers: Default start amount for token AS12U4TZfNK7qoLyEERBBRDMu8nm5MKoRzPXDXans4v9wdATZedz9 is 100
2025-07-01T07:49:10.461737Z DEBUG quedge::helpers: Cycle path Inisde Simulation Task:
2025-07-01T07:49:10.461832Z  INFO quedge::helpers: Offchain Earning percentage: -99.99999980685968
2025-07-01T07:49:10.461924Z  INFO quedge::helpers: Offchain Amount out after Offchain simulation: 0.00000001563070140668977
2025-07-01T07:49:10.462021Z DEBUG quedge::helpers::display: Path: ["WMAS-EagleFi", "POM-EagleFi", "POM-Dusa", "WMAS-Dusa", "USDC.e-Dusa", "PUR-Dusa", "PUR-EagleFi", "WMAS-EagleFi"]
2025-07-01T07:49:10.462124Z  WARN quedge::helpers: Not Valid Cycle Because Offchain Earning Percentage is less than margin -99.99999980685968 < 0.1
2025-07-01T07:49:10.462237Z  INFO quedge::helpers: Offchain Earning percentage: -99.9999999843693
2025-07-01T07:49:10.462331Z  INFO quedge::helpers: Default start amount for token AS133eqPPaPttJ6hJnk3sfoG5cjFFqBDi1VGxdo2wzWkq8AfZnan is 20000000
2025-07-01T07:49:10.462422Z  INFO quedge::helpers: Default start amount for token AS12U4TZfNK7qoLyEERBBRDMu8nm5MKoRzPXDXans4v9wdATZedz9 is 100
2025-07-01T07:49:10.462518Z  WARN quedge::helpers: Not Valid Cycle Because Offchain Earning Percentage is less than margin -99.9999999843693 < 0.1
2025-07-01T07:49:10.462616Z  INFO quedge::helpers: Offchain Amount out after Offchain simulation: 0.016564994676675538
2025-07-01T07:49:10.462704Z  INFO quedge::helpers: Offchain Amount out after Offchain simulation: 0.00000001653277140762374
2025-07-01T07:49:10.462804Z  INFO quedge::helpers: Offchain Earning percentage: -99.99999991717502
2025-07-01T07:49:10.462898Z  INFO quedge::helpers: Offchain Earning percentage: -99.99999998346723
2025-07-01T07:49:10.462999Z  WARN quedge::helpers: Not Valid Cycle Because Offchain Earning Percentage is less than margin -99.99999991717502 < 0.1
2025-07-01T07:49:10.463098Z  WARN quedge::helpers: Not Valid Cycle Because Offchain Earning Percentage is less than margin -99.99999998346723 < 0.1
2025-07-01T07:49:10.463246Z  INFO quedge::helpers: Found 0 valid cycles
2025-07-01T07:49:10.463358Z  WARN quedge::helpers: No profitable arbitrage cycle found.
2025-07-01T07:49:10.657648Z DEBUG quedge::spawns::balance_notifier: Balance of AU16raXifo3FExijWrhUBLBneaHnWGicWbShgyXgAKB1bnj1yLvH is 110.33030328
2025-07-01T07:49:10.902684Z  INFO quedge::spawns::swap_fetcher: Start waitiing for an arbitrage opportunity...
2025-07-01T07:52:50.581157Z  WARN quedge::spawns::swap_fetcher: Operation O12f6JHeHcp9h1c6AC4FnJPxUsN6jJZYGJpv3spxjnMZnNavMk8g is not successful, skipping
2025-07-01T07:54:20.660423Z  INFO quedge: Logger initialized Successfully
2025-07-01T07:54:28.825837Z  INFO quedge: Allowed start tokens: ["AS124vf3YfAJCSCQVYKczzuWWpXrximFpbTmX4rheLs5uNSftiiRY", "AS125oPLYRTtfVjpWisPZVTLjBhCFfQ1jDsi75XNtRm1NZux54eCj", "AS12U4TZfNK7qoLyEERBBRDMu8nm5MKoRzPXDXans4v9wdATZedz9", "AS12LKs9txoSSy8JgFJgV96m8k5z9pgzjYMYSshwN67mFVuj3bdUV", "AS133eqPPaPttJ6hJnk3sfoG5cjFFqBDi1VGxdo2wzWkq8AfZnan", "AS1nqHKXpnFXqhDExTskXmBbbVpVpUbCQVtNSXLCqUDSUXihdWRq", "AS1hCJXjndR4c9vekLWsXGnrdigp4AaZ7uYG3UKFzzKnWVsrNLPJ", "AS12fr54YtBY575Dfhtt7yftpT8KXgXb1ia5Pn1LofoLFLf9WcjGL"]
2025-07-01T07:54:28.826214Z  INFO quedge: Exporting the sparse matrix to a png file...
2025-07-01T07:54:32.108590Z  INFO quedge: Exported the sparse matrix to a png file successfully!
2025-07-01T07:54:32.108922Z  INFO quedge::spawns::swap_fetcher: Swap event fetcher working in candidate state
2025-07-01T07:54:32.109004Z DEBUG quedge::helpers: Detecting arbitrage cycles...
2025-07-01T07:54:32.121429Z DEBUG quedge::helpers: Arbitrage cycles found (4 total):
2025-07-01T07:54:32.121700Z DEBUG quedge::helpers: Cycle path Inisde Simulation Task:
2025-07-01T07:54:32.121816Z DEBUG quedge::helpers::display: Path: ["WMAS-Dusa", "WMAS-EagleFi", "POM-EagleFi", "POM-Dusa", "WMAS-Dusa"]
2025-07-01T07:54:32.122002Z DEBUG quedge::helpers: Cycle path Inisde Simulation Task:
2025-07-01T07:54:32.122108Z DEBUG quedge::helpers::display: Path: ["POM-Dusa", "WMAS-Dusa", "WMAS-EagleFi", "POM-EagleFi", "POM-Dusa"]
2025-07-01T07:54:32.122306Z DEBUG quedge::helpers: Cycle path Inisde Simulation Task:
2025-07-01T07:54:32.122429Z  INFO quedge::helpers: Default start amount for token AS12U4TZfNK7qoLyEERBBRDMu8nm5MKoRzPXDXans4v9wdATZedz9 is 100
2025-07-01T07:54:32.122551Z DEBUG quedge::helpers::display: Path: ["WMAS-EagleFi", "POM-EagleFi", "POM-Dusa", "WMAS-Dusa", "WMAS-EagleFi"]
2025-07-01T07:54:32.122689Z  INFO quedge::helpers: Offchain Amount out after Offchain simulation: 0.000000015630701406604442
2025-07-01T07:54:32.122799Z DEBUG quedge::helpers: Cycle path Inisde Simulation Task:
2025-07-01T07:54:32.122902Z  INFO quedge::helpers: Default start amount for token AS1nqHKXpnFXqhDExTskXmBbbVpVpUbCQVtNSXLCqUDSUXihdWRq is 40207
2025-07-01T07:54:32.123007Z  INFO quedge::helpers: Offchain Earning percentage: -99.9999999843693
2025-07-01T07:54:32.123092Z DEBUG quedge::helpers::display: Path: ["POM-EagleFi", "POM-Dusa", "WMAS-Dusa", "WMAS-EagleFi", "POM-EagleFi"]
2025-07-01T07:54:32.123192Z  INFO quedge::helpers: Offchain Amount out after Offchain simulation: 0.00007765593260211698
2025-07-01T07:54:32.123304Z  WARN quedge::helpers: Not Valid Cycle Because Offchain Earning Percentage is less than margin -99.9999999843693 < 0.1
2025-07-01T07:54:32.123394Z  INFO quedge::helpers: Default start amount for token AS1nqHKXpnFXqhDExTskXmBbbVpVpUbCQVtNSXLCqUDSUXihdWRq is 40207
2025-07-01T07:54:32.123486Z  INFO quedge::helpers: Offchain Earning percentage: -99.99999980685968
2025-07-01T07:54:32.123571Z  INFO quedge::helpers: Default start amount for token AS12U4TZfNK7qoLyEERBBRDMu8nm5MKoRzPXDXans4v9wdATZedz9 is 100
2025-07-01T07:54:32.123663Z  INFO quedge::helpers: Offchain Amount out after Offchain simulation: 0.00007765593260211698
2025-07-01T07:54:32.123793Z  WARN quedge::helpers: Not Valid Cycle Because Offchain Earning Percentage is less than margin -99.99999980685968 < 0.1
2025-07-01T07:54:32.123881Z  INFO quedge::helpers: Offchain Amount out after Offchain simulation: 0.000000015630701406604442
2025-07-01T07:54:32.123955Z  INFO quedge::helpers: Offchain Earning percentage: -99.99999980685968
2025-07-01T07:54:32.124028Z  INFO quedge::helpers: Offchain Earning percentage: -99.9999999843693
2025-07-01T07:54:32.124103Z  WARN quedge::helpers: Not Valid Cycle Because Offchain Earning Percentage is less than margin -99.99999980685968 < 0.1
2025-07-01T07:54:32.124169Z  WARN quedge::helpers: Not Valid Cycle Because Offchain Earning Percentage is less than margin -99.9999999843693 < 0.1
2025-07-01T07:54:32.124295Z  INFO quedge::helpers: Found 0 valid cycles
2025-07-01T07:54:32.124376Z  WARN quedge::helpers: No profitable arbitrage cycle found.
2025-07-01T07:54:32.244589Z  INFO quedge::spawns::swap_fetcher: Start waitiing for an arbitrage opportunity...
2025-07-01T07:54:32.653596Z DEBUG quedge::spawns::balance_notifier: Balance of AU16raXifo3FExijWrhUBLBneaHnWGicWbShgyXgAKB1bnj1yLvH is 110.32030328
2025-07-01T07:56:08.583083Z  INFO quedge::spawns::swap_fetcher: Found 2 Swap events from the operation O1Q3AxWqNKxTU6R7VwbaJv6L1EeQdErb77ELWC9aAJCkbRzpXnu. Processing them...
2025-07-01T07:56:08.583367Z  INFO quedge::spawns::swap_fetcher: New detected swap on pool: AS1Ba1T2mMpHvLEhTvsNkSDqm4djno2ezVojGrtLf1wRd8ThfZD3
2025-07-01T07:56:09.208973Z  INFO quedge::strategies::bellman_ford_matrix: Updating sparse matrix by pool: Pool { address: "AS1Ba1T2mMpHvLEhTvsNkSDqm4djno2ezVojGrtLf1wRd8ThfZD3", a_token: Token { address: "AS124vf3YfAJCSCQVYKczzuWWpXrximFpbTmX4rheLs5uNSftiiRY", name: "Wrapped Ether", symbol: "WETH.e", decimals: 18, dex: Dusa }, b_token: Token { address: "AS12U4TZfNK7qoLyEERBBRDMu8nm5MKoRzPXDXans4v9wdATZedz9", name: "Wrapped Massa", symbol: "WMAS", decimals: 9, dex: Dusa }, a_reserve: 2.3044655015559017, b_reserve: 530222.08777317, dex: Dusa, swap_fee: 0.0, a_price: 175913.6194988621, b_price: 5.684608177858955e-6, active_bin_id: 8382840, bin_step: 15, bins: {8382839: Bin { id: 8382839, a_reserve: 0.0, b_reserve: 2400.64508266 }, 8382840: Bin { id: 8382840, a_reserve: 0.005041515434024973, b_reserve: 777.558080531 }, 8382841: Bin { id: 8382841, a_reserve: 0.009474737904929352, b_reserve: 0.0 }} }
2025-07-01T07:56:09.209256Z  WARN quedge::spawns::swap_fetcher: Skipping arbitrage detection after updating the sparse matrix because of an order from the events stream
2025-07-01T07:56:09.209415Z  INFO quedge::spawns::swap_fetcher: Swap event processed successfully
2025-07-01T07:56:09.209572Z DEBUG quedge::spawns::swap_fetcher: Processing last event of the operation O1Q3AxWqNKxTU6R7VwbaJv6L1EeQdErb77ELWC9aAJCkbRzpXnu...
2025-07-01T07:56:09.209705Z  WARN quedge::spawns::swap_fetcher: Pool AS12UMSUxgpRBB6ArZDJ19arHoxNkkpdfofQGekAiAJqsuE6PEFJy not found in the pools hash map
2025-07-01T07:56:09.209834Z  INFO quedge::spawns::swap_fetcher: New detected swap on pool: AS1Ba1T2mMpHvLEhTvsNkSDqm4djno2ezVojGrtLf1wRd8ThfZD3
2025-07-01T07:56:09.643242Z  INFO quedge::strategies::bellman_ford_matrix: Updating sparse matrix by pool: Pool { address: "AS1Ba1T2mMpHvLEhTvsNkSDqm4djno2ezVojGrtLf1wRd8ThfZD3", a_token: Token { address: "AS124vf3YfAJCSCQVYKczzuWWpXrximFpbTmX4rheLs5uNSftiiRY", name: "Wrapped Ether", symbol: "WETH.e", decimals: 18, dex: Dusa }, b_token: Token { address: "AS12U4TZfNK7qoLyEERBBRDMu8nm5MKoRzPXDXans4v9wdATZedz9", name: "Wrapped Massa", symbol: "WMAS", decimals: 9, dex: Dusa }, a_reserve: 2.3044655015559017, b_reserve: 530222.08777317, dex: Dusa, swap_fee: 0.0, a_price: 175913.6194988621, b_price: 5.684608177858955e-6, active_bin_id: 8382840, bin_step: 15, bins: {8382839: Bin { id: 8382839, a_reserve: 0.0, b_reserve: 2400.64508266 }, 8382840: Bin { id: 8382840, a_reserve: 0.005041515434024973, b_reserve: 777.558080531 }, 8382841: Bin { id: 8382841, a_reserve: 0.009474737904929352, b_reserve: 0.0 }} }
2025-07-01T07:56:09.643824Z  INFO quedge::spawns::swap_fetcher: Swap event processed successfully
2025-07-01T07:56:09.644709Z DEBUG quedge::helpers: Detecting arbitrage cycles...
2025-07-01T07:56:09.661458Z DEBUG quedge::helpers: Arbitrage cycles found (4 total):
2025-07-01T07:56:09.661691Z DEBUG quedge::helpers: Cycle path Inisde Simulation Task:
2025-07-01T07:56:09.661822Z DEBUG quedge::helpers::display: Path: ["WMAS-Dusa", "WMAS-EagleFi", "POM-EagleFi", "POM-Dusa", "WMAS-Dusa"]
2025-07-01T07:56:09.662032Z DEBUG quedge::helpers: Cycle path Inisde Simulation Task:
2025-07-01T07:56:09.662158Z  INFO quedge::helpers: Default start amount for token AS12U4TZfNK7qoLyEERBBRDMu8nm5MKoRzPXDXans4v9wdATZedz9 is 100
2025-07-01T07:56:09.662251Z DEBUG quedge::helpers::display: Path: ["POM-Dusa", "WMAS-Dusa", "WMAS-EagleFi", "POM-EagleFi", "POM-Dusa"]
2025-07-01T07:56:09.662368Z  INFO quedge::helpers: Offchain Amount out after Offchain simulation: 0.000000015630701406604442
2025-07-01T07:56:09.662480Z DEBUG quedge::helpers: Cycle path Inisde Simulation Task:
2025-07-01T07:56:09.662577Z  INFO quedge::helpers: Offchain Earning percentage: -99.9999999843693
2025-07-01T07:56:09.662680Z  INFO quedge::helpers: Default start amount for token AS1nqHKXpnFXqhDExTskXmBbbVpVpUbCQVtNSXLCqUDSUXihdWRq is 40207
2025-07-01T07:56:09.662773Z DEBUG quedge::helpers::display: Path: ["WMAS-EagleFi", "POM-EagleFi", "POM-Dusa", "WMAS-Dusa", "WMAS-EagleFi"]
2025-07-01T07:56:09.662873Z  WARN quedge::helpers: Not Valid Cycle Because Offchain Earning Percentage is less than margin -99.9999999843693 < 0.1
2025-07-01T07:56:09.662966Z  INFO quedge::helpers: Offchain Amount out after Offchain simulation: 0.00007765593260211698
2025-07-01T07:56:09.663059Z DEBUG quedge::helpers: Cycle path Inisde Simulation Task:
2025-07-01T07:56:09.663161Z  INFO quedge::helpers: Default start amount for token AS12U4TZfNK7qoLyEERBBRDMu8nm5MKoRzPXDXans4v9wdATZedz9 is 100
2025-07-01T07:56:09.663257Z  INFO quedge::helpers: Offchain Earning percentage: -99.99999980685968
2025-07-01T07:56:09.663345Z DEBUG quedge::helpers::display: Path: ["POM-EagleFi", "POM-Dusa", "WMAS-Dusa", "WMAS-EagleFi", "POM-EagleFi"]
2025-07-01T07:56:09.663449Z  INFO quedge::helpers: Offchain Amount out after Offchain simulation: 0.000000015630701406604442
2025-07-01T07:56:09.663543Z  WARN quedge::helpers: Not Valid Cycle Because Offchain Earning Percentage is less than margin -99.99999980685968 < 0.1
2025-07-01T07:56:09.663632Z  INFO quedge::helpers: Default start amount for token AS1nqHKXpnFXqhDExTskXmBbbVpVpUbCQVtNSXLCqUDSUXihdWRq is 40207
2025-07-01T07:56:09.663721Z  INFO quedge::helpers: Offchain Earning percentage: -99.9999999843693
2025-07-01T07:56:09.663815Z  INFO quedge::helpers: Offchain Amount out after Offchain simulation: 0.00007765593260211698
2025-07-01T07:56:09.663908Z  WARN quedge::helpers: Not Valid Cycle Because Offchain Earning Percentage is less than margin -99.9999999843693 < 0.1
2025-07-01T07:56:09.663992Z  INFO quedge::helpers: Offchain Earning percentage: -99.99999980685968
2025-07-01T07:56:09.664098Z  WARN quedge::helpers: Not Valid Cycle Because Offchain Earning Percentage is less than margin -99.99999980685968 < 0.1
2025-07-01T07:56:09.664239Z  INFO quedge::helpers: Found 0 valid cycles
2025-07-01T07:56:09.664365Z  WARN quedge::helpers: No profitable arbitrage cycle found.
2025-07-01T07:56:18.600215Z  WARN quedge::spawns::swap_fetcher: Operation O12F5qu2CtFcNLCyCwdY8sZBpb4Z7jEW5Xw5kb4K8NCyDwKaWS3g is not successful, skipping
2025-07-01T07:56:41.091737Z  INFO quedge::spawns::swap_fetcher: Found 2 Swap events from the operation O12rTL3pWzo68NGBH6xANFSAiiKEb2aQNM2tbEazWbNF8UAb6jyE. Processing them...
2025-07-01T07:56:41.091954Z  INFO quedge::spawns::swap_fetcher: New detected swap on pool: AS1F2SAbhvknGLQKzfjaizKiZhEEd6mS1HLpZqPc2atzKE4VTNv9
2025-07-01T07:56:41.349061Z  INFO quedge::strategies::bellman_ford_matrix: Updating sparse matrix by pool: Pool { address: "AS1F2SAbhvknGLQKzfjaizKiZhEEd6mS1HLpZqPc2atzKE4VTNv9", a_token: Token { address: "AS12LKs9txoSSy8JgFJgV96m8k5z9pgzjYMYSshwN67mFVuj3bdUV", name: "Bsc USDT", symbol: "USDT.b", decimals: 18, dex: Dusa }, b_token: Token { address: "AS1hCJXjndR4c9vekLWsXGnrdigp4AaZ7uYG3UKFzzKnWVsrNLPJ", name: "USD Coin", symbol: "USDC.e", decimals: 6, dex: Dusa }, a_reserve: 372.9569555951591, b_reserve: 31262.804094, dex: Dusa, swap_fee: 0.0, a_price: 1.0010030965990384, b_price: 0.9989979085954415, active_bin_id: 8112294, bin_step: 1, bins: {8112293: Bin { id: 8112293, a_reserve: 0.0, b_reserve: 25544.979119 }, 8112294: Bin { id: 8112294, a_reserve: 249.80367895440978, b_reserve: 13.423131 }, 8112295: Bin { id: 8112295, a_reserve: 26.31295936386564, b_reserve: 0.0 }} }
2025-07-01T07:56:41.349201Z  WARN quedge::spawns::swap_fetcher: Skipping arbitrage detection after updating the sparse matrix because of an order from the events stream
2025-07-01T07:56:41.349284Z  INFO quedge::spawns::swap_fetcher: Swap event processed successfully
2025-07-01T07:56:41.349365Z DEBUG quedge::spawns::swap_fetcher: Processing last event of the operation O12rTL3pWzo68NGBH6xANFSAiiKEb2aQNM2tbEazWbNF8UAb6jyE...
2025-07-01T07:56:41.349445Z  WARN quedge::spawns::swap_fetcher: Pool AS12UMSUxgpRBB6ArZDJ19arHoxNkkpdfofQGekAiAJqsuE6PEFJy not found in the pools hash map
2025-07-01T07:56:41.349574Z  INFO quedge::spawns::swap_fetcher: New detected swap on pool: AS1F2SAbhvknGLQKzfjaizKiZhEEd6mS1HLpZqPc2atzKE4VTNv9
2025-07-01T07:56:41.884549Z  INFO quedge::strategies::bellman_ford_matrix: Updating sparse matrix by pool: Pool { address: "AS1F2SAbhvknGLQKzfjaizKiZhEEd6mS1HLpZqPc2atzKE4VTNv9", a_token: Token { address: "AS12LKs9txoSSy8JgFJgV96m8k5z9pgzjYMYSshwN67mFVuj3bdUV", name: "Bsc USDT", symbol: "USDT.b", decimals: 18, dex: Dusa }, b_token: Token { address: "AS1hCJXjndR4c9vekLWsXGnrdigp4AaZ7uYG3UKFzzKnWVsrNLPJ", name: "USD Coin", symbol: "USDC.e", decimals: 6, dex: Dusa }, a_reserve: 372.9569555951591, b_reserve: 31262.804094, dex: Dusa, swap_fee: 0.0, a_price: 1.0010030965990384, b_price: 0.9989979085954415, active_bin_id: 8112294, bin_step: 1, bins: {8112293: Bin { id: 8112293, a_reserve: 0.0, b_reserve: 25544.979119 }, 8112294: Bin { id: 8112294, a_reserve: 249.80367895440978, b_reserve: 13.423131 }, 8112295: Bin { id: 8112295, a_reserve: 26.31295936386564, b_reserve: 0.0 }} }
2025-07-01T07:56:41.884768Z  INFO quedge::spawns::swap_fetcher: Swap event processed successfully
2025-07-01T07:56:41.885089Z DEBUG quedge::helpers: Detecting arbitrage cycles...
2025-07-01T07:56:41.897057Z DEBUG quedge::helpers: Arbitrage cycles found (4 total):
2025-07-01T07:56:41.897263Z DEBUG quedge::helpers: Cycle path Inisde Simulation Task:
2025-07-01T07:56:41.897373Z DEBUG quedge::helpers::display: Path: ["WMAS-Dusa", "WMAS-EagleFi", "POM-EagleFi", "POM-Dusa", "WMAS-Dusa"]
2025-07-01T07:56:41.897528Z DEBUG quedge::helpers: Cycle path Inisde Simulation Task:
2025-07-01T07:56:41.897641Z DEBUG quedge::helpers::display: Path: ["POM-Dusa", "WMAS-Dusa", "WMAS-EagleFi", "POM-EagleFi", "POM-Dusa"]
2025-07-01T07:56:41.897793Z DEBUG quedge::helpers: Cycle path Inisde Simulation Task:
2025-07-01T07:56:41.897914Z  INFO quedge::helpers: Default start amount for token AS12U4TZfNK7qoLyEERBBRDMu8nm5MKoRzPXDXans4v9wdATZedz9 is 100
2025-07-01T07:56:41.898012Z DEBUG quedge::helpers::display: Path: ["WMAS-EagleFi", "POM-EagleFi", "POM-Dusa", "WMAS-Dusa", "WMAS-EagleFi"]
2025-07-01T07:56:41.898087Z  INFO quedge::helpers: Offchain Amount out after Offchain simulation: 0.000000015630701406604442
2025-07-01T07:56:41.898162Z DEBUG quedge::helpers: Cycle path Inisde Simulation Task:
2025-07-01T07:56:41.898244Z  INFO quedge::helpers: Offchain Earning percentage: -99.9999999843693
2025-07-01T07:56:41.898317Z  INFO quedge::helpers: Default start amount for token AS1nqHKXpnFXqhDExTskXmBbbVpVpUbCQVtNSXLCqUDSUXihdWRq is 40207
2025-07-01T07:56:41.898389Z DEBUG quedge::helpers::display: Path: ["POM-EagleFi", "POM-Dusa", "WMAS-Dusa", "WMAS-EagleFi", "POM-EagleFi"]
2025-07-01T07:56:41.898457Z  WARN quedge::helpers: Not Valid Cycle Because Offchain Earning Percentage is less than margin -99.9999999843693 < 0.1
2025-07-01T07:56:41.898551Z  INFO quedge::helpers: Offchain Amount out after Offchain simulation: 0.00007765593260211698
2025-07-01T07:56:41.898620Z  INFO quedge::helpers: Default start amount for token AS1nqHKXpnFXqhDExTskXmBbbVpVpUbCQVtNSXLCqUDSUXihdWRq is 40207
2025-07-01T07:56:41.898691Z  INFO quedge::helpers: Default start amount for token AS12U4TZfNK7qoLyEERBBRDMu8nm5MKoRzPXDXans4v9wdATZedz9 is 100
2025-07-01T07:56:41.898752Z  INFO quedge::helpers: Offchain Earning percentage: -99.99999980685968
2025-07-01T07:56:41.898822Z  INFO quedge::helpers: Offchain Amount out after Offchain simulation: 0.00007765593260211698
2025-07-01T07:56:41.898891Z  INFO quedge::helpers: Offchain Amount out after Offchain simulation: 0.000000015630701406604442
2025-07-01T07:56:41.898969Z  WARN quedge::helpers: Not Valid Cycle Because Offchain Earning Percentage is less than margin -99.99999980685968 < 0.1
2025-07-01T07:56:41.899040Z  INFO quedge::helpers: Offchain Earning percentage: -99.99999980685968
2025-07-01T07:56:41.899108Z  INFO quedge::helpers: Offchain Earning percentage: -99.9999999843693
2025-07-01T07:56:41.899176Z  WARN quedge::helpers: Not Valid Cycle Because Offchain Earning Percentage is less than margin -99.99999980685968 < 0.1
2025-07-01T07:56:41.899239Z  WARN quedge::helpers: Not Valid Cycle Because Offchain Earning Percentage is less than margin -99.9999999843693 < 0.1
2025-07-01T07:56:41.899347Z  INFO quedge::helpers: Found 0 valid cycles
2025-07-01T07:56:41.899421Z  WARN quedge::helpers: No profitable arbitrage cycle found.
2025-07-01T07:56:50.743334Z  WARN quedge::spawns::swap_fetcher: Operation O12Mj7VvwS7F4pznwkxThtXYDSFurFvs4SNEbxotoPtef4kS69AQ is not successful, skipping
2025-07-01T07:57:14.577839Z  INFO quedge::spawns::swap_fetcher: Found 1 Swap events from the operation O1qTUevFmWncoj8aBkfoeFSNu1U7QTnrRBpMXuHPgeQk3Wgm6Aw. Processing them...
2025-07-01T07:57:14.578010Z DEBUG quedge::spawns::swap_fetcher: Processing last event of the operation O1qTUevFmWncoj8aBkfoeFSNu1U7QTnrRBpMXuHPgeQk3Wgm6Aw...
2025-07-01T07:57:14.578119Z  WARN quedge::spawns::swap_fetcher: Pool AS12UMSUxgpRBB6ArZDJ19arHoxNkkpdfofQGekAiAJqsuE6PEFJy not found in the pools hash map
2025-07-01T07:57:14.578224Z  INFO quedge::spawns::swap_fetcher: New detected swap on pool: AS1Ba1T2mMpHvLEhTvsNkSDqm4djno2ezVojGrtLf1wRd8ThfZD3
2025-07-01T07:57:14.996317Z  INFO quedge::strategies::bellman_ford_matrix: Updating sparse matrix by pool: Pool { address: "AS1Ba1T2mMpHvLEhTvsNkSDqm4djno2ezVojGrtLf1wRd8ThfZD3", a_token: Token { address: "AS124vf3YfAJCSCQVYKczzuWWpXrximFpbTmX4rheLs5uNSftiiRY", name: "Wrapped Ether", symbol: "WETH.e", decimals: 18, dex: Dusa }, b_token: Token { address: "AS12U4TZfNK7qoLyEERBBRDMu8nm5MKoRzPXDXans4v9wdATZedz9", name: "Wrapped Massa", symbol: "WMAS", decimals: 9, dex: Dusa }, a_reserve: 2.343347222171239, b_reserve: 537241.007658068, dex: Dusa, swap_fee: 0.0, a_price: 175913.6194988621, b_price: 5.684608177858955e-6, active_bin_id: 8382840, bin_step: 15, bins: {8382839: Bin { id: 8382839, a_reserve: 0.0, b_reserve: 2847.708809227 }, 8382840: Bin { id: 8382840, a_reserve: 0.005802504308267736, b_reserve: 1090.522066923 }, 8382841: Bin { id: 8382841, a_reserve: 0.012016120021002307, b_reserve: 0.0 }} }
2025-07-01T07:57:14.996528Z  INFO quedge::spawns::swap_fetcher: Swap event processed successfully
2025-07-01T07:57:14.996740Z DEBUG quedge::helpers: Detecting arbitrage cycles...
2025-07-01T07:57:15.008053Z DEBUG quedge::helpers: Arbitrage cycles found (4 total):
2025-07-01T07:57:15.008181Z DEBUG quedge::helpers: Cycle path Inisde Simulation Task:
2025-07-01T07:57:15.008298Z DEBUG quedge::helpers::display: Path: ["WMAS-Dusa", "WMAS-EagleFi", "POM-EagleFi", "POM-Dusa", "WMAS-Dusa"]
2025-07-01T07:57:15.008485Z DEBUG quedge::helpers: Cycle path Inisde Simulation Task:
2025-07-01T07:57:15.008639Z DEBUG quedge::helpers::display: Path: ["POM-Dusa", "WMAS-Dusa", "WMAS-EagleFi", "POM-EagleFi", "POM-Dusa"]
2025-07-01T07:57:15.008797Z DEBUG quedge::helpers: Cycle path Inisde Simulation Task:
2025-07-01T07:57:15.008895Z  INFO quedge::helpers: Default start amount for token AS12U4TZfNK7qoLyEERBBRDMu8nm5MKoRzPXDXans4v9wdATZedz9 is 100
2025-07-01T07:57:15.009006Z DEBUG quedge::helpers::display: Path: ["WMAS-EagleFi", "POM-EagleFi", "POM-Dusa", "WMAS-Dusa", "WMAS-EagleFi"]
2025-07-01T07:57:15.009171Z  INFO quedge::helpers: Offchain Amount out after Offchain simulation: 0.000000015630701406604442
2025-07-01T07:57:15.009295Z DEBUG quedge::helpers: Cycle path Inisde Simulation Task:
2025-07-01T07:57:15.009374Z  INFO quedge::helpers: Default start amount for token AS1nqHKXpnFXqhDExTskXmBbbVpVpUbCQVtNSXLCqUDSUXihdWRq is 40207
2025-07-01T07:57:15.009454Z  INFO quedge::helpers: Offchain Earning percentage: -99.9999999843693
2025-07-01T07:57:15.009524Z DEBUG quedge::helpers::display: Path: ["POM-EagleFi", "POM-Dusa", "WMAS-Dusa", "WMAS-EagleFi", "POM-EagleFi"]
2025-07-01T07:57:15.009624Z  INFO quedge::helpers: Offchain Amount out after Offchain simulation: 0.00007765593260211698
2025-07-01T07:57:15.009718Z  WARN quedge::helpers: Not Valid Cycle Because Offchain Earning Percentage is less than margin -99.9999999843693 < 0.1
2025-07-01T07:57:15.009795Z  INFO quedge::helpers: Default start amount for token AS1nqHKXpnFXqhDExTskXmBbbVpVpUbCQVtNSXLCqUDSUXihdWRq is 40207
2025-07-01T07:57:15.009864Z  INFO quedge::helpers: Default start amount for token AS12U4TZfNK7qoLyEERBBRDMu8nm5MKoRzPXDXans4v9wdATZedz9 is 100
2025-07-01T07:57:15.009927Z  INFO quedge::helpers: Offchain Earning percentage: -99.99999980685968
2025-07-01T07:57:15.009993Z  INFO quedge::helpers: Offchain Amount out after Offchain simulation: 0.00007765593260211698
2025-07-01T07:57:15.010078Z  INFO quedge::helpers: Offchain Amount out after Offchain simulation: 0.000000015630701406604442
2025-07-01T07:57:15.010145Z  WARN quedge::helpers: Not Valid Cycle Because Offchain Earning Percentage is less than margin -99.99999980685968 < 0.1
2025-07-01T07:57:15.010214Z  INFO quedge::helpers: Offchain Earning percentage: -99.99999980685968
2025-07-01T07:57:15.010277Z  INFO quedge::helpers: Offchain Earning percentage: -99.9999999843693
2025-07-01T07:57:15.010341Z  WARN quedge::helpers: Not Valid Cycle Because Offchain Earning Percentage is less than margin -99.99999980685968 < 0.1
2025-07-01T07:57:15.010444Z  WARN quedge::helpers: Not Valid Cycle Because Offchain Earning Percentage is less than margin -99.9999999843693 < 0.1
2025-07-01T07:57:15.010546Z  INFO quedge::helpers: Found 0 valid cycles
2025-07-01T07:57:15.010617Z  WARN quedge::helpers: No profitable arbitrage cycle found.
2025-07-01T07:57:22.590311Z  WARN quedge::spawns::swap_fetcher: Operation O1TWYrfiYJTh1wFaE8SeXvf21Wfuu6DgzCrNjYHjnd86HErSAZr is not successful, skipping
2025-07-01T07:57:45.069491Z  WARN quedge::spawns::swap_fetcher: Operation O1PCCZcZQqteE6Gh3xxjxxUikpZK3DYC8RrVLrtpyvFPufxBaTc is not successful, skipping
2025-07-01T08:01:45.306178Z  INFO quedge::spawns::swap_fetcher: Found 1 Swap events from the operation O12CQCxMbMdgTErRPQpm9mxL1siZEVDERTrxDzWDQ5eYm6UDcTQm. Processing them...
2025-07-01T08:01:45.306364Z DEBUG quedge::spawns::swap_fetcher: Processing last event of the operation O12CQCxMbMdgTErRPQpm9mxL1siZEVDERTrxDzWDQ5eYm6UDcTQm...
2025-07-01T08:01:45.306492Z  WARN quedge::spawns::swap_fetcher: Pool AS12UMSUxgpRBB6ArZDJ19arHoxNkkpdfofQGekAiAJqsuE6PEFJy not found in the pools hash map
2025-07-01T08:01:45.306622Z  INFO quedge::spawns::swap_fetcher: New detected swap on pool: AS1F2SAbhvknGLQKzfjaizKiZhEEd6mS1HLpZqPc2atzKE4VTNv9
2025-07-01T08:01:45.887783Z  INFO quedge::strategies::bellman_ford_matrix: Updating sparse matrix by pool: Pool { address: "AS1F2SAbhvknGLQKzfjaizKiZhEEd6mS1HLpZqPc2atzKE4VTNv9", a_token: Token { address: "AS12LKs9txoSSy8JgFJgV96m8k5z9pgzjYMYSshwN67mFVuj3bdUV", name: "Bsc USDT", symbol: "USDT.b", decimals: 18, dex: Dusa }, b_token: Token { address: "AS1hCJXjndR4c9vekLWsXGnrdigp4AaZ7uYG3UKFzzKnWVsrNLPJ", name: "USD Coin", symbol: "USDC.e", decimals: 6, dex: Dusa }, a_reserve: 304.3910588128227, b_reserve: 31331.438769, dex: Dusa, swap_fee: 0.0, a_price: 1.0010030965990384, b_price: 0.9989979085954415, active_bin_id: 8112294, bin_step: 1, bins: {8112293: Bin { id: 8112293, a_reserve: 0.0, b_reserve: 25544.979119 }, 8112294: Bin { id: 8112294, a_reserve: 181.23778217207334, b_reserve: 82.057806 }, 8112295: Bin { id: 8112295, a_reserve: 26.31295936386564, b_reserve: 0.0 }} }
2025-07-01T08:01:45.888009Z  INFO quedge::spawns::swap_fetcher: Swap event processed successfully
2025-07-01T08:01:45.888283Z DEBUG quedge::helpers: Detecting arbitrage cycles...
2025-07-01T08:01:45.899998Z DEBUG quedge::helpers: Arbitrage cycles found (4 total):
2025-07-01T08:01:45.900145Z DEBUG quedge::helpers: Cycle path Inisde Simulation Task:
2025-07-01T08:01:45.900229Z DEBUG quedge::helpers::display: Path: ["WMAS-Dusa", "WMAS-EagleFi", "POM-EagleFi", "POM-Dusa", "WMAS-Dusa"]
2025-07-01T08:01:45.900383Z DEBUG quedge::helpers: Cycle path Inisde Simulation Task:
2025-07-01T08:01:45.900471Z  INFO quedge::helpers: Default start amount for token AS12U4TZfNK7qoLyEERBBRDMu8nm5MKoRzPXDXans4v9wdATZedz9 is 100
2025-07-01T08:01:45.900536Z DEBUG quedge::helpers::display: Path: ["POM-Dusa", "WMAS-Dusa", "WMAS-EagleFi", "POM-EagleFi", "POM-Dusa"]
2025-07-01T08:01:45.900604Z  INFO quedge::helpers: Offchain Amount out after Offchain simulation: 0.000000015630701406604442
2025-07-01T08:01:45.900671Z DEBUG quedge::helpers: Cycle path Inisde Simulation Task:
2025-07-01T08:01:45.900745Z  INFO quedge::helpers: Offchain Earning percentage: -99.9999999843693
2025-07-01T08:01:45.900814Z  INFO quedge::helpers: Default start amount for token AS1nqHKXpnFXqhDExTskXmBbbVpVpUbCQVtNSXLCqUDSUXihdWRq is 40207
2025-07-01T08:01:45.900884Z DEBUG quedge::helpers::display: Path: ["WMAS-EagleFi", "POM-EagleFi", "POM-Dusa", "WMAS-Dusa", "WMAS-EagleFi"]
2025-07-01T08:01:45.900963Z  WARN quedge::helpers: Not Valid Cycle Because Offchain Earning Percentage is less than margin -99.9999999843693 < 0.1
2025-07-01T08:01:45.901044Z  INFO quedge::helpers: Offchain Amount out after Offchain simulation: 0.00007765593260211698
2025-07-01T08:01:45.901109Z DEBUG quedge::helpers: Cycle path Inisde Simulation Task:
2025-07-01T08:01:45.901174Z  INFO quedge::helpers: Default start amount for token AS12U4TZfNK7qoLyEERBBRDMu8nm5MKoRzPXDXans4v9wdATZedz9 is 100
2025-07-01T08:01:45.901239Z  INFO quedge::helpers: Offchain Earning percentage: -99.99999980685968
2025-07-01T08:01:45.901304Z DEBUG quedge::helpers::display: Path: ["POM-EagleFi", "POM-Dusa", "WMAS-Dusa", "WMAS-EagleFi", "POM-EagleFi"]
2025-07-01T08:01:45.901372Z  INFO quedge::helpers: Offchain Amount out after Offchain simulation: 0.000000015630701406604442
2025-07-01T08:01:45.901441Z  WARN quedge::helpers: Not Valid Cycle Because Offchain Earning Percentage is less than margin -99.99999980685968 < 0.1
2025-07-01T08:01:45.901508Z  INFO quedge::helpers: Default start amount for token AS1nqHKXpnFXqhDExTskXmBbbVpVpUbCQVtNSXLCqUDSUXihdWRq is 40207
2025-07-01T08:01:45.901580Z  INFO quedge::helpers: Offchain Earning percentage: -99.9999999843693
2025-07-01T08:01:45.901654Z  INFO quedge::helpers: Offchain Amount out after Offchain simulation: 0.00007765593260211698
2025-07-01T08:01:45.901720Z  WARN quedge::helpers: Not Valid Cycle Because Offchain Earning Percentage is less than margin -99.9999999843693 < 0.1
2025-07-01T08:01:45.901789Z  INFO quedge::helpers: Offchain Earning percentage: -99.99999980685968
2025-07-01T08:01:45.901863Z  WARN quedge::helpers: Not Valid Cycle Because Offchain Earning Percentage is less than margin -99.99999980685968 < 0.1
2025-07-01T08:01:45.901955Z  INFO quedge::helpers: Found 0 valid cycles
2025-07-01T08:01:45.902023Z  WARN quedge::helpers: No profitable arbitrage cycle found.
2025-07-01T08:01:54.635206Z  WARN quedge::spawns::swap_fetcher: Operation O12QULxyXNKhJxu4JccnDpibfbozDnTBivTcYcZwSKCsbBaGuYYv is not successful, skipping
2025-07-01T08:06:10.115104Z  INFO quedge::spawns::swap_fetcher: Found 1 Swap events from the operation O12mQ1xmXfsXtpinYcgPW14xwiWLC7cLdWCC2Ks6p6C3tjLbXjnw. Processing them...
2025-07-01T08:06:10.115254Z DEBUG quedge::spawns::swap_fetcher: Processing last event of the operation O12mQ1xmXfsXtpinYcgPW14xwiWLC7cLdWCC2Ks6p6C3tjLbXjnw...
2025-07-01T08:06:10.115377Z  WARN quedge::spawns::swap_fetcher: Pool AS12UMSUxgpRBB6ArZDJ19arHoxNkkpdfofQGekAiAJqsuE6PEFJy not found in the pools hash map
2025-07-01T08:06:10.115471Z  INFO quedge::spawns::swap_fetcher: New detected swap on pool: AS127CXxgeuu6A8UitxJwdnZ3SoXJFzTzEiKTZtaxzmEXXeoE1Wxc
2025-07-01T08:06:10.589072Z  INFO quedge::strategies::bellman_ford_matrix: Updating sparse matrix by pool: Pool { address: "AS127CXxgeuu6A8UitxJwdnZ3SoXJFzTzEiKTZtaxzmEXXeoE1Wxc", a_token: Token { address: "AS133eqPPaPttJ6hJnk3sfoG5cjFFqBDi1VGxdo2wzWkq8AfZnan", name: "Purrfect Universe", symbol: "PUR", decimals: 18, dex: Dusa }, b_token: Token { address: "AS12U4TZfNK7qoLyEERBBRDMu8nm5MKoRzPXDXans4v9wdATZedz9", name: "Wrapped Massa", symbol: "WMAS", decimals: 9, dex: Dusa }, a_reserve: 2694854589130.3384, b_reserve: 153622.*********, dex: Dusa, swap_fee: 0.0, a_price: 9.814515220889808e-7, b_price: 1018899.0260787812, active_bin_id: 8385135, bin_step: 100, bins: {8385134: Bin { id: 8385134, a_reserve: 0.0, b_reserve: 5596.********* }, 8385135: Bin { id: 8385135, a_reserve: 3425441039.930905, b_reserve: 2111.********* }, 8385136: Bin { id: 8385136, a_reserve: 4993479003.370187, b_reserve: 0.0 }} }
2025-07-01T08:06:10.589346Z  INFO quedge::spawns::swap_fetcher: Swap event processed successfully
2025-07-01T08:06:10.589640Z DEBUG quedge::helpers: Detecting arbitrage cycles...
2025-07-01T08:06:10.601016Z DEBUG quedge::helpers: Arbitrage cycles found (4 total):
2025-07-01T08:06:10.601250Z DEBUG quedge::helpers: Cycle path Inisde Simulation Task:
2025-07-01T08:06:10.601409Z DEBUG quedge::helpers::display: Path: ["WMAS-Dusa", "WMAS-EagleFi", "POM-EagleFi", "POM-Dusa", "WMAS-Dusa"]
2025-07-01T08:06:10.601579Z DEBUG quedge::helpers: Cycle path Inisde Simulation Task:
2025-07-01T08:06:10.601713Z  INFO quedge::helpers: Default start amount for token AS12U4TZfNK7qoLyEERBBRDMu8nm5MKoRzPXDXans4v9wdATZedz9 is 100
2025-07-01T08:06:10.601804Z DEBUG quedge::helpers::display: Path: ["POM-Dusa", "WMAS-Dusa", "WMAS-EagleFi", "POM-EagleFi", "POM-Dusa"]
2025-07-01T08:06:10.601918Z  INFO quedge::helpers: Offchain Amount out after Offchain simulation: 0.000000015630701406604442
2025-07-01T08:06:10.602003Z DEBUG quedge::helpers: Cycle path Inisde Simulation Task:
2025-07-01T08:06:10.602080Z  INFO quedge::helpers: Default start amount for token AS1nqHKXpnFXqhDExTskXmBbbVpVpUbCQVtNSXLCqUDSUXihdWRq is 40207
2025-07-01T08:06:10.602166Z  INFO quedge::helpers: Offchain Earning percentage: -99.9999999843693
2025-07-01T08:06:10.602240Z DEBUG quedge::helpers::display: Path: ["WMAS-EagleFi", "POM-EagleFi", "POM-Dusa", "WMAS-Dusa", "WMAS-EagleFi"]
2025-07-01T08:06:10.602346Z  INFO quedge::helpers: Offchain Amount out after Offchain simulation: 0.00007765593260211698
2025-07-01T08:06:10.602416Z  WARN quedge::helpers: Not Valid Cycle Because Offchain Earning Percentage is less than margin -99.9999999843693 < 0.1
2025-07-01T08:06:10.602481Z DEBUG quedge::helpers: Cycle path Inisde Simulation Task:
2025-07-01T08:06:10.602552Z  INFO quedge::helpers: Default start amount for token AS12U4TZfNK7qoLyEERBBRDMu8nm5MKoRzPXDXans4v9wdATZedz9 is 100
2025-07-01T08:06:10.602622Z  INFO quedge::helpers: Offchain Earning percentage: -99.99999980685968
2025-07-01T08:06:10.602687Z DEBUG quedge::helpers::display: Path: ["POM-EagleFi", "POM-Dusa", "WMAS-Dusa", "WMAS-EagleFi", "POM-EagleFi"]
2025-07-01T08:06:10.602751Z  INFO quedge::helpers: Offchain Amount out after Offchain simulation: 0.000000015630701406604442
2025-07-01T08:06:10.602820Z  WARN quedge::helpers: Not Valid Cycle Because Offchain Earning Percentage is less than margin -99.99999980685968 < 0.1
2025-07-01T08:06:10.602891Z  INFO quedge::helpers: Default start amount for token AS1nqHKXpnFXqhDExTskXmBbbVpVpUbCQVtNSXLCqUDSUXihdWRq is 40207
2025-07-01T08:06:10.602957Z  INFO quedge::helpers: Offchain Earning percentage: -99.9999999843693
2025-07-01T08:06:10.603025Z  INFO quedge::helpers: Offchain Amount out after Offchain simulation: 0.00007765593260211698
2025-07-01T08:06:10.603088Z  WARN quedge::helpers: Not Valid Cycle Because Offchain Earning Percentage is less than margin -99.9999999843693 < 0.1
2025-07-01T08:06:10.603151Z  INFO quedge::helpers: Offchain Earning percentage: -99.99999980685968
2025-07-01T08:06:10.603224Z  WARN quedge::helpers: Not Valid Cycle Because Offchain Earning Percentage is less than margin -99.99999980685968 < 0.1
2025-07-01T08:06:10.603319Z  INFO quedge::helpers: Found 0 valid cycles
2025-07-01T08:06:10.603388Z  WARN quedge::helpers: No profitable arbitrage cycle found.
2025-07-01T08:06:49.113633Z  WARN quedge::spawns::swap_fetcher: Operation O1LCcTqoTbje42jAi1p6oubipATY8y18PgCyc5qTE9ZNe3d7EZt is not successful, skipping
2025-07-01T08:09:29.215540Z  INFO quedge::spawns::swap_fetcher: Found 2 Swap events from the operation O12tVVEh8SwNsgGiHssch1LNEQxjFWwCAnHDp2HhUKjBdeSyFSUh. Processing them...
2025-07-01T08:09:29.215702Z  INFO quedge::spawns::swap_fetcher: New detected swap on pool: AS1F2SAbhvknGLQKzfjaizKiZhEEd6mS1HLpZqPc2atzKE4VTNv9
2025-07-01T08:09:29.801691Z  INFO quedge::strategies::bellman_ford_matrix: Updating sparse matrix by pool: Pool { address: "AS1F2SAbhvknGLQKzfjaizKiZhEEd6mS1HLpZqPc2atzKE4VTNv9", a_token: Token { address: "AS12LKs9txoSSy8JgFJgV96m8k5z9pgzjYMYSshwN67mFVuj3bdUV", name: "Bsc USDT", symbol: "USDT.b", decimals: 18, dex: Dusa }, b_token: Token { address: "AS1hCJXjndR4c9vekLWsXGnrdigp4AaZ7uYG3UKFzzKnWVsrNLPJ", name: "USD Coin", symbol: "USDC.e", decimals: 6, dex: Dusa }, a_reserve: 401.18012589926093, b_reserve: 31234.554096, dex: Dusa, swap_fee: 0.0, a_price: 1.0009030062984086, b_price: 0.999097808386301, active_bin_id: 8112293, bin_step: 1, bins: {8112292: Bin { id: 8112292, a_reserve: 0.0, b_reserve: 27.010864 }, 8112293: Bin { id: 8112293, a_reserve: 14.813490508258385, b_reserve: 25530.152252 }, 8112294: Bin { id: 8112294, a_reserve: 263.21335875025324, b_reserve: 0.0 }} }
2025-07-01T08:09:29.801909Z  WARN quedge::spawns::swap_fetcher: Skipping arbitrage detection after updating the sparse matrix because of an order from the events stream
2025-07-01T08:09:29.802138Z  INFO quedge::spawns::swap_fetcher: Swap event processed successfully
2025-07-01T08:09:29.802292Z DEBUG quedge::spawns::swap_fetcher: Processing last event of the operation O12tVVEh8SwNsgGiHssch1LNEQxjFWwCAnHDp2HhUKjBdeSyFSUh...
2025-07-01T08:09:29.802420Z  WARN quedge::spawns::swap_fetcher: Pool AS12UMSUxgpRBB6ArZDJ19arHoxNkkpdfofQGekAiAJqsuE6PEFJy not found in the pools hash map
2025-07-01T08:09:29.802525Z  INFO quedge::spawns::swap_fetcher: New detected swap on pool: AS1F2SAbhvknGLQKzfjaizKiZhEEd6mS1HLpZqPc2atzKE4VTNv9
2025-07-01T08:09:30.503626Z  INFO quedge::strategies::bellman_ford_matrix: Updating sparse matrix by pool: Pool { address: "AS1F2SAbhvknGLQKzfjaizKiZhEEd6mS1HLpZqPc2atzKE4VTNv9", a_token: Token { address: "AS12LKs9txoSSy8JgFJgV96m8k5z9pgzjYMYSshwN67mFVuj3bdUV", name: "Bsc USDT", symbol: "USDT.b", decimals: 18, dex: Dusa }, b_token: Token { address: "AS1hCJXjndR4c9vekLWsXGnrdigp4AaZ7uYG3UKFzzKnWVsrNLPJ", name: "USD Coin", symbol: "USDC.e", decimals: 6, dex: Dusa }, a_reserve: 401.18012589926093, b_reserve: 31234.554096, dex: Dusa, swap_fee: 0.0, a_price: 1.0009030062984086, b_price: 0.999097808386301, active_bin_id: 8112293, bin_step: 1, bins: {8112292: Bin { id: 8112292, a_reserve: 0.0, b_reserve: 27.010864 }, 8112293: Bin { id: 8112293, a_reserve: 14.813490508258385, b_reserve: 25530.152252 }, 8112294: Bin { id: 8112294, a_reserve: 263.21335875025324, b_reserve: 0.0 }} }
2025-07-01T08:09:30.503883Z  INFO quedge::spawns::swap_fetcher: Swap event processed successfully
2025-07-01T08:09:30.504296Z DEBUG quedge::helpers: Detecting arbitrage cycles...
2025-07-01T08:09:30.516687Z DEBUG quedge::helpers: Arbitrage cycles found (4 total):
2025-07-01T08:09:30.516932Z DEBUG quedge::helpers: Cycle path Inisde Simulation Task:
2025-07-01T08:09:30.517066Z DEBUG quedge::helpers::display: Path: ["WMAS-Dusa", "WMAS-EagleFi", "POM-EagleFi", "POM-Dusa", "WMAS-Dusa"]
2025-07-01T08:09:30.517278Z DEBUG quedge::helpers: Cycle path Inisde Simulation Task:
2025-07-01T08:09:30.517395Z DEBUG quedge::helpers::display: Path: ["POM-Dusa", "WMAS-Dusa", "WMAS-EagleFi", "POM-EagleFi", "POM-Dusa"]
2025-07-01T08:09:30.517584Z DEBUG quedge::helpers: Cycle path Inisde Simulation Task:
2025-07-01T08:09:30.517682Z  INFO quedge::helpers: Default start amount for token AS12U4TZfNK7qoLyEERBBRDMu8nm5MKoRzPXDXans4v9wdATZedz9 is 100
2025-07-01T08:09:30.517777Z DEBUG quedge::helpers::display: Path: ["WMAS-EagleFi", "POM-EagleFi", "POM-Dusa", "WMAS-Dusa", "WMAS-EagleFi"]
2025-07-01T08:09:30.517915Z  INFO quedge::helpers: Offchain Amount out after Offchain simulation: 0.000000015630701406604442
2025-07-01T08:09:30.518024Z DEBUG quedge::helpers: Cycle path Inisde Simulation Task:
2025-07-01T08:09:30.518114Z  INFO quedge::helpers: Offchain Earning percentage: -99.9999999843693
2025-07-01T08:09:30.518210Z  INFO quedge::helpers: Default start amount for token AS1nqHKXpnFXqhDExTskXmBbbVpVpUbCQVtNSXLCqUDSUXihdWRq is 40207
2025-07-01T08:09:30.518301Z DEBUG quedge::helpers::display: Path: ["POM-EagleFi", "POM-Dusa", "WMAS-Dusa", "WMAS-EagleFi", "POM-EagleFi"]
2025-07-01T08:09:30.518408Z  WARN quedge::helpers: Not Valid Cycle Because Offchain Earning Percentage is less than margin -99.9999999843693 < 0.1
2025-07-01T08:09:30.518531Z  INFO quedge::helpers: Offchain Amount out after Offchain simulation: 0.00007765593260211698
2025-07-01T08:09:30.518647Z  INFO quedge::helpers: Default start amount for token AS1nqHKXpnFXqhDExTskXmBbbVpVpUbCQVtNSXLCqUDSUXihdWRq is 40207
2025-07-01T08:09:30.518742Z  INFO quedge::helpers: Default start amount for token AS12U4TZfNK7qoLyEERBBRDMu8nm5MKoRzPXDXans4v9wdATZedz9 is 100
2025-07-01T08:09:30.518848Z  INFO quedge::helpers: Offchain Earning percentage: -99.99999980685968
2025-07-01T08:09:30.518968Z  INFO quedge::helpers: Offchain Amount out after Offchain simulation: 0.00007765593260211698
2025-07-01T08:09:30.519057Z  INFO quedge::helpers: Offchain Amount out after Offchain simulation: 0.000000015630701406604442
2025-07-01T08:09:30.519142Z  WARN quedge::helpers: Not Valid Cycle Because Offchain Earning Percentage is less than margin -99.99999980685968 < 0.1
2025-07-01T08:09:30.519236Z  INFO quedge::helpers: Offchain Earning percentage: -99.99999980685968
2025-07-01T08:09:30.519315Z  INFO quedge::helpers: Offchain Earning percentage: -99.9999999843693
2025-07-01T08:09:30.519403Z  WARN quedge::helpers: Not Valid Cycle Because Offchain Earning Percentage is less than margin -99.99999980685968 < 0.1
2025-07-01T08:09:30.519485Z  WARN quedge::helpers: Not Valid Cycle Because Offchain Earning Percentage is less than margin -99.9999999843693 < 0.1
2025-07-01T08:09:30.519603Z  INFO quedge::helpers: Found 0 valid cycles
2025-07-01T08:09:30.519728Z  WARN quedge::helpers: No profitable arbitrage cycle found.
2025-07-01T08:09:38.838259Z  WARN quedge::spawns::swap_fetcher: Operation O124L5bkV4RSUEdzQGTvyLqppR8XnQyyU1kJqGQqJXKTyyU78Qp6 is not successful, skipping
2025-07-01T08:13:13.118045Z  WARN quedge::spawns::swap_fetcher: Operation O1EcVY5XChLmWKmuWrU3AVhtpqzbKqNzkJa9Zjfb3uZxH7qoZGf is not successful, skipping
2025-07-01T08:14:33.116913Z  INFO quedge::spawns::swap_fetcher: Found 2 Swap events from the operation O12F4Q26w5sRY9AMf4D3SUvM35ZuKYoabXyiqPEUMtffa8JcDEna. Processing them...
2025-07-01T08:14:33.117039Z  INFO quedge::spawns::swap_fetcher: New detected swap on pool: AS1F2SAbhvknGLQKzfjaizKiZhEEd6mS1HLpZqPc2atzKE4VTNv9
2025-07-01T08:14:33.574326Z  INFO quedge::strategies::bellman_ford_matrix: Updating sparse matrix by pool: Pool { address: "AS1F2SAbhvknGLQKzfjaizKiZhEEd6mS1HLpZqPc2atzKE4VTNv9", a_token: Token { address: "AS12LKs9txoSSy8JgFJgV96m8k5z9pgzjYMYSshwN67mFVuj3bdUV", name: "Bsc USDT", symbol: "USDT.b", decimals: 18, dex: Dusa }, b_token: Token { address: "AS1hCJXjndR4c9vekLWsXGnrdigp4AaZ7uYG3UKFzzKnWVsrNLPJ", name: "USD Coin", symbol: "USDC.e", decimals: 6, dex: Dusa }, a_reserve: 317.44961881573516, b_reserve: 31318.367111, dex: Dusa, swap_fee: 0.0, a_price: 1.0010030965990384, b_price: 0.9989979085954415, active_bin_id: 8112294, bin_step: 1, bins: {8112293: Bin { id: 8112293, a_reserve: 0.0, b_reserve: 25544.97912 }, 8112294: Bin { id: 8112294, a_reserve: 194.29634217498585, b_reserve: 68.986147 }, 8112295: Bin { id: 8112295, a_reserve: 26.31295936386564, b_reserve: 0.0 }} }
2025-07-01T08:14:33.574520Z  WARN quedge::spawns::swap_fetcher: Skipping arbitrage detection after updating the sparse matrix because of an order from the events stream
2025-07-01T08:14:33.574636Z  INFO quedge::spawns::swap_fetcher: Swap event processed successfully
2025-07-01T08:14:33.574730Z DEBUG quedge::spawns::swap_fetcher: Processing last event of the operation O12F4Q26w5sRY9AMf4D3SUvM35ZuKYoabXyiqPEUMtffa8JcDEna...
2025-07-01T08:14:33.574822Z  WARN quedge::spawns::swap_fetcher: Pool AS12UMSUxgpRBB6ArZDJ19arHoxNkkpdfofQGekAiAJqsuE6PEFJy not found in the pools hash map
2025-07-01T08:14:33.574919Z  INFO quedge::spawns::swap_fetcher: New detected swap on pool: AS1F2SAbhvknGLQKzfjaizKiZhEEd6mS1HLpZqPc2atzKE4VTNv9
2025-07-01T08:14:34.008119Z  INFO quedge::strategies::bellman_ford_matrix: Updating sparse matrix by pool: Pool { address: "AS1F2SAbhvknGLQKzfjaizKiZhEEd6mS1HLpZqPc2atzKE4VTNv9", a_token: Token { address: "AS12LKs9txoSSy8JgFJgV96m8k5z9pgzjYMYSshwN67mFVuj3bdUV", name: "Bsc USDT", symbol: "USDT.b", decimals: 18, dex: Dusa }, b_token: Token { address: "AS1hCJXjndR4c9vekLWsXGnrdigp4AaZ7uYG3UKFzzKnWVsrNLPJ", name: "USD Coin", symbol: "USDC.e", decimals: 6, dex: Dusa }, a_reserve: 317.44961881573516, b_reserve: 31318.367111, dex: Dusa, swap_fee: 0.0, a_price: 1.0010030965990384, b_price: 0.9989979085954415, active_bin_id: 8112294, bin_step: 1, bins: {8112293: Bin { id: 8112293, a_reserve: 0.0, b_reserve: 25544.97912 }, 8112294: Bin { id: 8112294, a_reserve: 194.29634217498585, b_reserve: 68.986147 }, 8112295: Bin { id: 8112295, a_reserve: 26.31295936386564, b_reserve: 0.0 }} }
2025-07-01T08:14:34.008302Z  INFO quedge::spawns::swap_fetcher: Swap event processed successfully
2025-07-01T08:14:34.008624Z DEBUG quedge::helpers: Detecting arbitrage cycles...
2025-07-01T08:14:34.020596Z DEBUG quedge::helpers: Arbitrage cycles found (4 total):
2025-07-01T08:14:34.020767Z DEBUG quedge::helpers: Cycle path Inisde Simulation Task:
2025-07-01T08:14:34.020879Z DEBUG quedge::helpers::display: Path: ["WMAS-Dusa", "WMAS-EagleFi", "POM-EagleFi", "POM-Dusa", "WMAS-Dusa"]
2025-07-01T08:14:34.021079Z DEBUG quedge::helpers: Cycle path Inisde Simulation Task:
2025-07-01T08:14:34.021161Z  INFO quedge::helpers: Default start amount for token AS12U4TZfNK7qoLyEERBBRDMu8nm5MKoRzPXDXans4v9wdATZedz9 is 100
2025-07-01T08:14:34.021242Z DEBUG quedge::helpers::display: Path: ["POM-Dusa", "WMAS-Dusa", "WMAS-EagleFi", "POM-EagleFi", "POM-Dusa"]
2025-07-01T08:14:34.021370Z  INFO quedge::helpers: Offchain Amount out after Offchain simulation: 0.000000015630701406604442
2025-07-01T08:14:34.021452Z  INFO quedge::helpers: Default start amount for token AS1nqHKXpnFXqhDExTskXmBbbVpVpUbCQVtNSXLCqUDSUXihdWRq is 40207
2025-07-01T08:14:34.021537Z DEBUG quedge::helpers: Cycle path Inisde Simulation Task:
2025-07-01T08:14:34.021617Z  INFO quedge::helpers: Offchain Earning percentage: -99.9999999843693
2025-07-01T08:14:34.021700Z  INFO quedge::helpers: Offchain Amount out after Offchain simulation: 0.00007765593260211698
2025-07-01T08:14:34.021778Z DEBUG quedge::helpers::display: Path: ["WMAS-EagleFi", "POM-EagleFi", "POM-Dusa", "WMAS-Dusa", "WMAS-EagleFi"]
2025-07-01T08:14:34.021890Z  WARN quedge::helpers: Not Valid Cycle Because Offchain Earning Percentage is less than margin -99.9999999843693 < 0.1
2025-07-01T08:14:34.021983Z  INFO quedge::helpers: Default start amount for token AS12U4TZfNK7qoLyEERBBRDMu8nm5MKoRzPXDXans4v9wdATZedz9 is 100
2025-07-01T08:14:34.022062Z DEBUG quedge::helpers: Cycle path Inisde Simulation Task:
2025-07-01T08:14:34.022133Z  INFO quedge::helpers: Offchain Earning percentage: -99.99999980685968
2025-07-01T08:14:34.022204Z  INFO quedge::helpers: Offchain Amount out after Offchain simulation: 0.000000015630701406604442
2025-07-01T08:14:34.022277Z DEBUG quedge::helpers::display: Path: ["POM-EagleFi", "POM-Dusa", "WMAS-Dusa", "WMAS-EagleFi", "POM-EagleFi"]
2025-07-01T08:14:34.022397Z  WARN quedge::helpers: Not Valid Cycle Because Offchain Earning Percentage is less than margin -99.99999980685968 < 0.1
2025-07-01T08:14:34.022518Z  INFO quedge::helpers: Offchain Earning percentage: -99.9999999843693
2025-07-01T08:14:34.022615Z  INFO quedge::helpers: Default start amount for token AS1nqHKXpnFXqhDExTskXmBbbVpVpUbCQVtNSXLCqUDSUXihdWRq is 40207
2025-07-01T08:14:34.022709Z  WARN quedge::helpers: Not Valid Cycle Because Offchain Earning Percentage is less than margin -99.9999999843693 < 0.1
2025-07-01T08:14:34.022808Z  INFO quedge::helpers: Offchain Amount out after Offchain simulation: 0.00007765593260211698
2025-07-01T08:14:34.022909Z  INFO quedge::helpers: Offchain Earning percentage: -99.99999980685968
2025-07-01T08:14:34.023015Z  WARN quedge::helpers: Not Valid Cycle Because Offchain Earning Percentage is less than margin -99.99999980685968 < 0.1
2025-07-01T08:14:34.023136Z  INFO quedge::helpers: Found 0 valid cycles
2025-07-01T08:14:34.023229Z  WARN quedge::helpers: No profitable arbitrage cycle found.
2025-07-01T08:14:42.647686Z  WARN quedge::spawns::swap_fetcher: Operation O1XrigVBwiwVEfizDa2SGgsSijpmAxawyFmdCVXMmnMQ4Vc8gGN is not successful, skipping
2025-07-01T08:17:45.111398Z  INFO quedge::spawns::swap_fetcher: Found 1 Swap events from the operation O1NsB4ufNxRQbCCCG7jXpVPfUMYkXqMtF4mSN8iAUyVCagUSR1Y. Processing them...
2025-07-01T08:17:45.111516Z DEBUG quedge::spawns::swap_fetcher: Processing last event of the operation O1NsB4ufNxRQbCCCG7jXpVPfUMYkXqMtF4mSN8iAUyVCagUSR1Y...
2025-07-01T08:17:45.111615Z  WARN quedge::spawns::swap_fetcher: Pool AS12UMSUxgpRBB6ArZDJ19arHoxNkkpdfofQGekAiAJqsuE6PEFJy not found in the pools hash map
2025-07-01T08:17:45.111705Z  INFO quedge::spawns::swap_fetcher: New detected swap on pool: AS1F2SAbhvknGLQKzfjaizKiZhEEd6mS1HLpZqPc2atzKE4VTNv9
2025-07-01T08:17:45.367632Z  INFO quedge::strategies::bellman_ford_matrix: Updating sparse matrix by pool: Pool { address: "AS1F2SAbhvknGLQKzfjaizKiZhEEd6mS1HLpZqPc2atzKE4VTNv9", a_token: Token { address: "AS12LKs9txoSSy8JgFJgV96m8k5z9pgzjYMYSshwN67mFVuj3bdUV", name: "Bsc USDT", symbol: "USDT.b", decimals: 18, dex: Dusa }, b_token: Token { address: "AS1hCJXjndR4c9vekLWsXGnrdigp4AaZ7uYG3UKFzzKnWVsrNLPJ", name: "USD Coin", symbol: "USDC.e", decimals: 6, dex: Dusa }, a_reserve: 352.09840704385556, b_reserve: 31283.683567, dex: Dusa, swap_fee: 0.0, a_price: 1.0010030965990384, b_price: 0.9989979085954415, active_bin_id: 8112294, bin_step: 1, bins: {8112293: Bin { id: 8112293, a_reserve: 0.0, b_reserve: 25544.97912 }, 8112294: Bin { id: 8112294, a_reserve: 228.94513040310622, b_reserve: 34.302603 }, 8112295: Bin { id: 8112295, a_reserve: 26.31295936386564, b_reserve: 0.0 }} }
2025-07-01T08:17:45.367815Z  INFO quedge::spawns::swap_fetcher: Swap event processed successfully
2025-07-01T08:17:45.368075Z DEBUG quedge::helpers: Detecting arbitrage cycles...
2025-07-01T08:17:45.379897Z DEBUG quedge::helpers: Arbitrage cycles found (4 total):
2025-07-01T08:17:45.380101Z DEBUG quedge::helpers: Cycle path Inisde Simulation Task:
2025-07-01T08:17:45.380224Z DEBUG quedge::helpers::display: Path: ["WMAS-Dusa", "WMAS-EagleFi", "POM-EagleFi", "POM-Dusa", "WMAS-Dusa"]
2025-07-01T08:17:45.380456Z DEBUG quedge::helpers: Cycle path Inisde Simulation Task:
2025-07-01T08:17:45.380547Z  INFO quedge::helpers: Default start amount for token AS12U4TZfNK7qoLyEERBBRDMu8nm5MKoRzPXDXans4v9wdATZedz9 is 100
2025-07-01T08:17:45.380633Z DEBUG quedge::helpers::display: Path: ["POM-Dusa", "WMAS-Dusa", "WMAS-EagleFi", "POM-EagleFi", "POM-Dusa"]
2025-07-01T08:17:45.380728Z  INFO quedge::helpers: Offchain Amount out after Offchain simulation: 0.000000015630701406604442
2025-07-01T08:17:45.380816Z DEBUG quedge::helpers: Cycle path Inisde Simulation Task:
2025-07-01T08:17:45.380900Z  INFO quedge::helpers: Offchain Earning percentage: -99.9999999843693
2025-07-01T08:17:45.380985Z  INFO quedge::helpers: Default start amount for token AS1nqHKXpnFXqhDExTskXmBbbVpVpUbCQVtNSXLCqUDSUXihdWRq is 40207
2025-07-01T08:17:45.381071Z DEBUG quedge::helpers::display: Path: ["WMAS-EagleFi", "POM-EagleFi", "POM-Dusa", "WMAS-Dusa", "WMAS-EagleFi"]
2025-07-01T08:17:45.381159Z  WARN quedge::helpers: Not Valid Cycle Because Offchain Earning Percentage is less than margin -99.9999999843693 < 0.1
2025-07-01T08:17:45.381240Z  INFO quedge::helpers: Offchain Amount out after Offchain simulation: 0.00007765593260211698
2025-07-01T08:17:45.381318Z DEBUG quedge::helpers: Cycle path Inisde Simulation Task:
2025-07-01T08:17:45.381397Z  INFO quedge::helpers: Default start amount for token AS12U4TZfNK7qoLyEERBBRDMu8nm5MKoRzPXDXans4v9wdATZedz9 is 100
2025-07-01T08:17:45.381470Z  INFO quedge::helpers: Offchain Earning percentage: -99.99999980685968
2025-07-01T08:17:45.381538Z DEBUG quedge::helpers::display: Path: ["POM-EagleFi", "POM-Dusa", "WMAS-Dusa", "WMAS-EagleFi", "POM-EagleFi"]
2025-07-01T08:17:45.381613Z  INFO quedge::helpers: Offchain Amount out after Offchain simulation: 0.000000015630701406604442
2025-07-01T08:17:45.381685Z  INFO quedge::helpers: Default start amount for token AS1nqHKXpnFXqhDExTskXmBbbVpVpUbCQVtNSXLCqUDSUXihdWRq is 40207
2025-07-01T08:17:45.381760Z  WARN quedge::helpers: Not Valid Cycle Because Offchain Earning Percentage is less than margin -99.99999980685968 < 0.1
2025-07-01T08:17:45.381836Z  INFO quedge::helpers: Offchain Earning percentage: -99.9999999843693
2025-07-01T08:17:45.381905Z  INFO quedge::helpers: Offchain Amount out after Offchain simulation: 0.00007765593260211698
2025-07-01T08:17:45.381976Z  WARN quedge::helpers: Not Valid Cycle Because Offchain Earning Percentage is less than margin -99.9999999843693 < 0.1
2025-07-01T08:17:45.382047Z  INFO quedge::helpers: Offchain Earning percentage: -99.99999980685968
2025-07-01T08:17:45.382125Z  WARN quedge::helpers: Not Valid Cycle Because Offchain Earning Percentage is less than margin -99.99999980685968 < 0.1
2025-07-01T08:17:45.382228Z  INFO quedge::helpers: Found 0 valid cycles
2025-07-01T08:17:45.382305Z  WARN quedge::helpers: No profitable arbitrage cycle found.
2025-07-01T08:17:54.664471Z  WARN quedge::spawns::swap_fetcher: Operation O12oi72fgwJNJ8gc9XGaU6xFJQDJ2Lq9tRPhXF2SBEXkSrMqpnau is not successful, skipping
2025-07-01T08:19:21.102095Z  WARN quedge::spawns::swap_fetcher: Operation O16CVkojJc3wg5cRRSRDJbuCjh7WVTSok94cMo1qWLRDgffRawh is not successful, skipping
2025-07-01T08:24:57.140532Z  WARN quedge::spawns::swap_fetcher: Operation O1NEsyimAZL7qjhiweq37mq6RgRXycpRjeHEU4NkWaeUQDqg9oy is not successful, skipping
2025-07-01T08:29:13.127107Z  WARN quedge::spawns::swap_fetcher: Operation O1YUeXcy6XqaUSMrnnXxEBVWVuaH5nE3BdEpv1EAKeq7zJwHxit is not successful, skipping
2025-07-01T08:31:53.126790Z  INFO quedge::spawns::swap_fetcher: Found 2 Swap events from the operation O12subWUSPAJvgwbkiGVvKT7qUQfXqFTbAax8qHASzpD9paep4hy. Processing them...
2025-07-01T08:31:53.126922Z  INFO quedge::spawns::swap_fetcher: New detected swap on pool: AS1F2SAbhvknGLQKzfjaizKiZhEEd6mS1HLpZqPc2atzKE4VTNv9
2025-07-01T08:31:53.846772Z  INFO quedge::strategies::bellman_ford_matrix: Updating sparse matrix by pool: Pool { address: "AS1F2SAbhvknGLQKzfjaizKiZhEEd6mS1HLpZqPc2atzKE4VTNv9", a_token: Token { address: "AS12LKs9txoSSy8JgFJgV96m8k5z9pgzjYMYSshwN67mFVuj3bdUV", name: "Bsc USDT", symbol: "USDT.b", decimals: 18, dex: Dusa }, b_token: Token { address: "AS1hCJXjndR4c9vekLWsXGnrdigp4AaZ7uYG3UKFzzKnWVsrNLPJ", name: "USD Coin", symbol: "USDC.e", decimals: 6, dex: Dusa }, a_reserve: 434.6548244270857, b_reserve: 31201.049171, dex: Dusa, swap_fee: 0.0, a_price: 1.0009030062984086, b_price: 0.999097808386301, active_bin_id: 8112293, bin_step: 1, bins: {8112292: Bin { id: 8112292, a_reserve: 0.0, b_reserve: 27.010864 }, 8112293: Bin { id: 8112293, a_reserve: 48.288188726746185, b_reserve: 25496.647327 }, 8112294: Bin { id: 8112294, a_reserve: 263.21335905959023, b_reserve: 0.0 }} }
2025-07-01T08:31:53.846954Z  WARN quedge::spawns::swap_fetcher: Skipping arbitrage detection after updating the sparse matrix because of an order from the events stream
2025-07-01T08:31:53.847072Z  INFO quedge::spawns::swap_fetcher: Swap event processed successfully
2025-07-01T08:31:53.847177Z DEBUG quedge::spawns::swap_fetcher: Processing last event of the operation O12subWUSPAJvgwbkiGVvKT7qUQfXqFTbAax8qHASzpD9paep4hy...
2025-07-01T08:31:53.847312Z  WARN quedge::spawns::swap_fetcher: Pool AS12UMSUxgpRBB6ArZDJ19arHoxNkkpdfofQGekAiAJqsuE6PEFJy not found in the pools hash map
2025-07-01T08:31:53.847463Z  INFO quedge::spawns::swap_fetcher: New detected swap on pool: AS1F2SAbhvknGLQKzfjaizKiZhEEd6mS1HLpZqPc2atzKE4VTNv9
2025-07-01T08:31:54.269403Z  INFO quedge::strategies::bellman_ford_matrix: Updating sparse matrix by pool: Pool { address: "AS1F2SAbhvknGLQKzfjaizKiZhEEd6mS1HLpZqPc2atzKE4VTNv9", a_token: Token { address: "AS12LKs9txoSSy8JgFJgV96m8k5z9pgzjYMYSshwN67mFVuj3bdUV", name: "Bsc USDT", symbol: "USDT.b", decimals: 18, dex: Dusa }, b_token: Token { address: "AS1hCJXjndR4c9vekLWsXGnrdigp4AaZ7uYG3UKFzzKnWVsrNLPJ", name: "USD Coin", symbol: "USDC.e", decimals: 6, dex: Dusa }, a_reserve: 434.6548244270857, b_reserve: 31201.049171, dex: Dusa, swap_fee: 0.0, a_price: 1.0009030062984086, b_price: 0.999097808386301, active_bin_id: 8112293, bin_step: 1, bins: {8112292: Bin { id: 8112292, a_reserve: 0.0, b_reserve: 27.010864 }, 8112293: Bin { id: 8112293, a_reserve: 48.288188726746185, b_reserve: 25496.647327 }, 8112294: Bin { id: 8112294, a_reserve: 263.21335905959023, b_reserve: 0.0 }} }
2025-07-01T08:31:54.269612Z  INFO quedge::spawns::swap_fetcher: Swap event processed successfully
2025-07-01T08:31:54.269945Z DEBUG quedge::helpers: Detecting arbitrage cycles...
2025-07-01T08:31:54.281587Z DEBUG quedge::helpers: Arbitrage cycles found (4 total):
2025-07-01T08:31:54.281750Z DEBUG quedge::helpers: Cycle path Inisde Simulation Task:
2025-07-01T08:31:54.281843Z DEBUG quedge::helpers::display: Path: ["WMAS-Dusa", "WMAS-EagleFi", "POM-EagleFi", "POM-Dusa", "WMAS-Dusa"]
2025-07-01T08:31:54.282031Z DEBUG quedge::helpers: Cycle path Inisde Simulation Task:
2025-07-01T08:31:54.282152Z  INFO quedge::helpers: Default start amount for token AS12U4TZfNK7qoLyEERBBRDMu8nm5MKoRzPXDXans4v9wdATZedz9 is 100
2025-07-01T08:31:54.282277Z DEBUG quedge::helpers::display: Path: ["POM-Dusa", "WMAS-Dusa", "WMAS-EagleFi", "POM-EagleFi", "POM-Dusa"]
2025-07-01T08:31:54.282390Z  INFO quedge::helpers: Offchain Amount out after Offchain simulation: 0.000000015630701406604442
2025-07-01T08:31:54.282465Z DEBUG quedge::helpers: Cycle path Inisde Simulation Task:
2025-07-01T08:31:54.282555Z  INFO quedge::helpers: Default start amount for token AS1nqHKXpnFXqhDExTskXmBbbVpVpUbCQVtNSXLCqUDSUXihdWRq is 40207
2025-07-01T08:31:54.282645Z  INFO quedge::helpers: Offchain Earning percentage: -99.9999999843693
2025-07-01T08:31:54.282744Z DEBUG quedge::helpers::display: Path: ["WMAS-EagleFi", "POM-EagleFi", "POM-Dusa", "WMAS-Dusa", "WMAS-EagleFi"]
2025-07-01T08:31:54.282843Z  INFO quedge::helpers: Offchain Amount out after Offchain simulation: 0.00007765593260211698
2025-07-01T08:31:54.282924Z  INFO quedge::helpers: Default start amount for token AS12U4TZfNK7qoLyEERBBRDMu8nm5MKoRzPXDXans4v9wdATZedz9 is 100
2025-07-01T08:31:54.282995Z DEBUG quedge::helpers: Cycle path Inisde Simulation Task:
2025-07-01T08:31:54.283071Z  WARN quedge::helpers: Not Valid Cycle Because Offchain Earning Percentage is less than margin -99.9999999843693 < 0.1
2025-07-01T08:31:54.283149Z  INFO quedge::helpers: Offchain Earning percentage: -99.99999980685968
2025-07-01T08:31:54.283224Z  INFO quedge::helpers: Offchain Amount out after Offchain simulation: 0.000000015630701406604442
2025-07-01T08:31:54.283304Z DEBUG quedge::helpers::display: Path: ["POM-EagleFi", "POM-Dusa", "WMAS-Dusa", "WMAS-EagleFi", "POM-EagleFi"]
2025-07-01T08:31:54.283374Z  WARN quedge::helpers: Not Valid Cycle Because Offchain Earning Percentage is less than margin -99.99999980685968 < 0.1
2025-07-01T08:31:54.283452Z  INFO quedge::helpers: Offchain Earning percentage: -99.9999999843693
2025-07-01T08:31:54.283524Z  INFO quedge::helpers: Default start amount for token AS1nqHKXpnFXqhDExTskXmBbbVpVpUbCQVtNSXLCqUDSUXihdWRq is 40207
2025-07-01T08:31:54.283591Z  WARN quedge::helpers: Not Valid Cycle Because Offchain Earning Percentage is less than margin -99.9999999843693 < 0.1
2025-07-01T08:31:54.283663Z  INFO quedge::helpers: Offchain Amount out after Offchain simulation: 0.00007765593260211698
2025-07-01T08:31:54.283746Z  INFO quedge::helpers: Offchain Earning percentage: -99.99999980685968
2025-07-01T08:31:54.283826Z  WARN quedge::helpers: Not Valid Cycle Because Offchain Earning Percentage is less than margin -99.99999980685968 < 0.1
2025-07-01T08:31:54.283930Z  INFO quedge::helpers: Found 0 valid cycles
2025-07-01T08:31:54.284015Z  WARN quedge::helpers: No profitable arbitrage cycle found.
2025-07-01T08:32:02.655667Z  WARN quedge::spawns::swap_fetcher: Operation O12pxfXAsmzxTkmue5vcmzxiRFAWrUbdFFrJKM1boNcVUYYvQ16a is not successful, skipping
2025-07-01T08:33:29.356298Z  WARN quedge::spawns::swap_fetcher: Operation O12hcNEXBQ8KiBAHaUfd578UQCbTyw4FuqiDCf7jnxR6NvTCNd4g is not successful, skipping
2025-07-01T08:34:55.637866Z  INFO quedge::spawns::swap_fetcher: Found 2 Swap events from the operation O1mpVGZSYxo8SEU9Bh5XRxGDgxraQ5ZLh5oUUH7HPt3J8Zfo6fH. Processing them...
2025-07-01T08:34:55.638002Z  INFO quedge::spawns::swap_fetcher: New detected swap on pool: AS1F2SAbhvknGLQKzfjaizKiZhEEd6mS1HLpZqPc2atzKE4VTNv9
2025-07-01T08:34:55.898674Z  INFO quedge::strategies::bellman_ford_matrix: Updating sparse matrix by pool: Pool { address: "AS1F2SAbhvknGLQKzfjaizKiZhEEd6mS1HLpZqPc2atzKE4VTNv9", a_token: Token { address: "AS12LKs9txoSSy8JgFJgV96m8k5z9pgzjYMYSshwN67mFVuj3bdUV", name: "Bsc USDT", symbol: "USDT.b", decimals: 18, dex: Dusa }, b_token: Token { address: "AS1hCJXjndR4c9vekLWsXGnrdigp4AaZ7uYG3UKFzzKnWVsrNLPJ", name: "USD Coin", symbol: "USDC.e", decimals: 6, dex: Dusa }, a_reserve: 563.1407515363031, b_reserve: 31072.447221, dex: Dusa, swap_fee: 0.0, a_price: 1.0009030062984086, b_price: 0.999097808386301, active_bin_id: 8112293, bin_step: 1, bins: {8112292: Bin { id: 8112292, a_reserve: 0.0, b_reserve: 27.010864 }, 8112293: Bin { id: 8112293, a_reserve: 176.7741158359636, b_reserve: 25368.045377 }, 8112294: Bin { id: 8112294, a_reserve: 263.21335905959023, b_reserve: 0.0 }} }
2025-07-01T08:34:55.898898Z  WARN quedge::spawns::swap_fetcher: Skipping arbitrage detection after updating the sparse matrix because of an order from the events stream
2025-07-01T08:34:55.899007Z  INFO quedge::spawns::swap_fetcher: Swap event processed successfully
2025-07-01T08:34:55.899102Z DEBUG quedge::spawns::swap_fetcher: Processing last event of the operation O1mpVGZSYxo8SEU9Bh5XRxGDgxraQ5ZLh5oUUH7HPt3J8Zfo6fH...
2025-07-01T08:34:55.899198Z  WARN quedge::spawns::swap_fetcher: Pool AS12UMSUxgpRBB6ArZDJ19arHoxNkkpdfofQGekAiAJqsuE6PEFJy not found in the pools hash map
2025-07-01T08:34:55.899304Z  INFO quedge::spawns::swap_fetcher: New detected swap on pool: AS12Q5NyCQUtEBTnnqqBwcGyYb18szbbKv5GArcdk9tm2HitTupHw
2025-07-01T08:34:56.470777Z  INFO quedge::strategies::bellman_ford_matrix: Updating sparse matrix by pool: Pool { address: "AS12Q5NyCQUtEBTnnqqBwcGyYb18szbbKv5GArcdk9tm2HitTupHw", a_token: Token { address: "AS12U4TZfNK7qoLyEERBBRDMu8nm5MKoRzPXDXans4v9wdATZedz9", name: "Wrapped Massa", symbol: "WMAS", decimals: 9, dex: Dusa }, b_token: Token { address: "AS1hCJXjndR4c9vekLWsXGnrdigp4AaZ7uYG3UKFzzKnWVsrNLPJ", name: "USD Coin", symbol: "USDC.e", decimals: 6, dex: Dusa }, a_reserve: 1985671.54993346, b_reserve: 4178.213774, dex: Dusa, swap_fee: 0.0, a_price: 0.013966835972304014, b_price: 71.59817742421993, active_bin_id: 8383013, bin_step: 20, bins: {8383012: Bin { id: 8383012, a_reserve: 0.0, b_reserve: 327.154441 }, 8383013: Bin { id: 8383013, a_reserve: 11280.79184662, b_reserve: 168.12363 }, 8383014: Bin { id: 8383014, a_reserve: 20974.225749574, b_reserve: 0.0 }} }
2025-07-01T08:34:56.470961Z  INFO quedge::spawns::swap_fetcher: Swap event processed successfully
2025-07-01T08:34:56.471223Z DEBUG quedge::helpers: Detecting arbitrage cycles...
2025-07-01T08:34:56.482988Z DEBUG quedge::helpers: Arbitrage cycles found (4 total):
2025-07-01T08:34:56.483148Z DEBUG quedge::helpers: Cycle path Inisde Simulation Task:
2025-07-01T08:34:56.483243Z DEBUG quedge::helpers::display: Path: ["WMAS-Dusa", "WMAS-EagleFi", "POM-EagleFi", "POM-Dusa", "WMAS-Dusa"]
2025-07-01T08:34:56.483382Z DEBUG quedge::helpers: Cycle path Inisde Simulation Task:
2025-07-01T08:34:56.483469Z DEBUG quedge::helpers::display: Path: ["POM-Dusa", "WMAS-Dusa", "WMAS-EagleFi", "POM-EagleFi", "POM-Dusa"]
2025-07-01T08:34:56.483607Z DEBUG quedge::helpers: Cycle path Inisde Simulation Task:
2025-07-01T08:34:56.483750Z  INFO quedge::helpers: Default start amount for token AS12U4TZfNK7qoLyEERBBRDMu8nm5MKoRzPXDXans4v9wdATZedz9 is 100
2025-07-01T08:34:56.483842Z DEBUG quedge::helpers::display: Path: ["WMAS-EagleFi", "POM-EagleFi", "POM-Dusa", "WMAS-Dusa", "WMAS-EagleFi"]
2025-07-01T08:34:56.483939Z  INFO quedge::helpers: Offchain Amount out after Offchain simulation: 0.000000015630701406604442
2025-07-01T08:34:56.484024Z DEBUG quedge::helpers: Cycle path Inisde Simulation Task:
2025-07-01T08:34:56.484112Z  INFO quedge::helpers: Offchain Earning percentage: -99.9999999843693
2025-07-01T08:34:56.484192Z  INFO quedge::helpers: Default start amount for token AS1nqHKXpnFXqhDExTskXmBbbVpVpUbCQVtNSXLCqUDSUXihdWRq is 40207
2025-07-01T08:34:56.484269Z DEBUG quedge::helpers::display: Path: ["POM-EagleFi", "POM-Dusa", "WMAS-Dusa", "WMAS-EagleFi", "POM-EagleFi"]
2025-07-01T08:34:56.484346Z  WARN quedge::helpers: Not Valid Cycle Because Offchain Earning Percentage is less than margin -99.9999999843693 < 0.1
2025-07-01T08:34:56.484423Z  INFO quedge::helpers: Offchain Amount out after Offchain simulation: 0.00007765593260211698
2025-07-01T08:34:56.484502Z  INFO quedge::helpers: Default start amount for token AS1nqHKXpnFXqhDExTskXmBbbVpVpUbCQVtNSXLCqUDSUXihdWRq is 40207
2025-07-01T08:34:56.484574Z  INFO quedge::helpers: Default start amount for token AS12U4TZfNK7qoLyEERBBRDMu8nm5MKoRzPXDXans4v9wdATZedz9 is 100
2025-07-01T08:34:56.484647Z  INFO quedge::helpers: Offchain Earning percentage: -99.99999980685968
2025-07-01T08:34:56.484715Z  INFO quedge::helpers: Offchain Amount out after Offchain simulation: 0.00007765593260211698
2025-07-01T08:34:56.484790Z  INFO quedge::helpers: Offchain Amount out after Offchain simulation: 0.000000015630701406604442
2025-07-01T08:34:56.484859Z  WARN quedge::helpers: Not Valid Cycle Because Offchain Earning Percentage is less than margin -99.99999980685968 < 0.1
2025-07-01T08:34:56.484953Z  INFO quedge::helpers: Offchain Earning percentage: -99.99999980685968
2025-07-01T08:34:56.485026Z  INFO quedge::helpers: Offchain Earning percentage: -99.9999999843693
2025-07-01T08:34:56.485104Z  WARN quedge::helpers: Not Valid Cycle Because Offchain Earning Percentage is less than margin -99.99999980685968 < 0.1
2025-07-01T08:34:56.485172Z  WARN quedge::helpers: Not Valid Cycle Because Offchain Earning Percentage is less than margin -99.9999999843693 < 0.1
2025-07-01T08:34:56.485284Z  INFO quedge::helpers: Found 0 valid cycles
2025-07-01T08:34:56.485361Z  WARN quedge::helpers: No profitable arbitrage cycle found.
2025-07-01T08:34:58.651404Z  WARN quedge::spawns::swap_fetcher: Operation O1j3NyBwhzJGDerA9Nc12rwM6TM8Pye781XYBGJ6BbuCmzqb6gn is not successful, skipping
2025-07-01T08:36:09.125535Z  WARN quedge::spawns::swap_fetcher: Operation O12ie5sqMSQBqw2ri7d7opMCVxY9NGm5yqRX6efF85fHwkrHgJP is not successful, skipping
2025-07-01T08:37:29.129390Z  WARN quedge::spawns::swap_fetcher: Operation O12cY4TqXxRy5jzaqvoWPMjnQ34UFFh3hB4BShJkoptmEaG1U8JS is not successful, skipping
2025-07-01T08:39:05.139560Z  WARN quedge::spawns::swap_fetcher: Operation O1eTkkxNzCasuZTVnyDVqqqVidChUHNZVFtnZcCw1KdwEdzzHDY is not successful, skipping
2025-07-01T08:41:13.139655Z  INFO quedge::spawns::swap_fetcher: Found 1 Swap events from the operation O1BBJLmfAitf4xuYPgRDfJQbU5s5wm3Pe9qMA228EURSk1mvirM. Processing them...
2025-07-01T08:41:13.139774Z DEBUG quedge::spawns::swap_fetcher: Processing last event of the operation O1BBJLmfAitf4xuYPgRDfJQbU5s5wm3Pe9qMA228EURSk1mvirM...
2025-07-01T08:41:13.139866Z  WARN quedge::spawns::swap_fetcher: Pool AS12UMSUxgpRBB6ArZDJ19arHoxNkkpdfofQGekAiAJqsuE6PEFJy not found in the pools hash map
2025-07-01T08:41:13.139959Z  INFO quedge::spawns::swap_fetcher: New detected swap on pool: AS1F2SAbhvknGLQKzfjaizKiZhEEd6mS1HLpZqPc2atzKE4VTNv9
2025-07-01T08:41:13.554047Z  INFO quedge::strategies::bellman_ford_matrix: Updating sparse matrix by pool: Pool { address: "AS1F2SAbhvknGLQKzfjaizKiZhEEd6mS1HLpZqPc2atzKE4VTNv9", a_token: Token { address: "AS12LKs9txoSSy8JgFJgV96m8k5z9pgzjYMYSshwN67mFVuj3bdUV", name: "Bsc USDT", symbol: "USDT.b", decimals: 18, dex: Dusa }, b_token: Token { address: "AS1hCJXjndR4c9vekLWsXGnrdigp4AaZ7uYG3UKFzzKnWVsrNLPJ", name: "USD Coin", symbol: "USDC.e", decimals: 6, dex: Dusa }, a_reserve: 659.8953348116946, b_reserve: 30975.605268, dex: Dusa, swap_fee: 0.0, a_price: 1.0009030062984086, b_price: 0.999097808386301, active_bin_id: 8112293, bin_step: 1, bins: {8112292: Bin { id: 8112292, a_reserve: 0.0, b_reserve: 27.010864 }, 8112293: Bin { id: 8112293, a_reserve: 273.528699111355, b_reserve: 25271.203424 }, 8112294: Bin { id: 8112294, a_reserve: 263.21335905959023, b_reserve: 0.0 }} }
2025-07-01T08:41:13.554340Z  INFO quedge::spawns::swap_fetcher: Swap event processed successfully
2025-07-01T08:41:13.555125Z DEBUG quedge::helpers: Detecting arbitrage cycles...
2025-07-01T08:41:13.566892Z DEBUG quedge::helpers: Arbitrage cycles found (4 total):
2025-07-01T08:41:13.567066Z DEBUG quedge::helpers: Cycle path Inisde Simulation Task:
2025-07-01T08:41:13.567170Z DEBUG quedge::helpers::display: Path: ["WMAS-Dusa", "WMAS-EagleFi", "POM-EagleFi", "POM-Dusa", "WMAS-Dusa"]
2025-07-01T08:41:13.567379Z DEBUG quedge::helpers: Cycle path Inisde Simulation Task:
2025-07-01T08:41:13.567483Z  INFO quedge::helpers: Default start amount for token AS12U4TZfNK7qoLyEERBBRDMu8nm5MKoRzPXDXans4v9wdATZedz9 is 100
2025-07-01T08:41:13.567593Z DEBUG quedge::helpers::display: Path: ["POM-Dusa", "WMAS-Dusa", "WMAS-EagleFi", "POM-EagleFi", "POM-Dusa"]
2025-07-01T08:41:13.567696Z  INFO quedge::helpers: Offchain Amount out after Offchain simulation: 0.000000015630701406604442
2025-07-01T08:41:13.567806Z DEBUG quedge::helpers: Cycle path Inisde Simulation Task:
2025-07-01T08:41:13.567896Z  INFO quedge::helpers: Default start amount for token AS1nqHKXpnFXqhDExTskXmBbbVpVpUbCQVtNSXLCqUDSUXihdWRq is 40207
2025-07-01T08:41:13.567975Z  INFO quedge::helpers: Offchain Earning percentage: -99.9999999843693
2025-07-01T08:41:13.568044Z DEBUG quedge::helpers::display: Path: ["WMAS-EagleFi", "POM-EagleFi", "POM-Dusa", "WMAS-Dusa", "WMAS-EagleFi"]
2025-07-01T08:41:13.568128Z  INFO quedge::helpers: Offchain Amount out after Offchain simulation: 0.00007765593260211698
2025-07-01T08:41:13.568204Z  WARN quedge::helpers: Not Valid Cycle Because Offchain Earning Percentage is less than margin -99.9999999843693 < 0.1
2025-07-01T08:41:13.568273Z DEBUG quedge::helpers: Cycle path Inisde Simulation Task:
2025-07-01T08:41:13.568337Z  INFO quedge::helpers: Offchain Earning percentage: -99.99999980685968
2025-07-01T08:41:13.568405Z  INFO quedge::helpers: Default start amount for token AS12U4TZfNK7qoLyEERBBRDMu8nm5MKoRzPXDXans4v9wdATZedz9 is 100
2025-07-01T08:41:13.568475Z DEBUG quedge::helpers::display: Path: ["POM-EagleFi", "POM-Dusa", "WMAS-Dusa", "WMAS-EagleFi", "POM-EagleFi"]
2025-07-01T08:41:13.568577Z  WARN quedge::helpers: Not Valid Cycle Because Offchain Earning Percentage is less than margin -99.99999980685968 < 0.1
2025-07-01T08:41:13.568666Z  INFO quedge::helpers: Offchain Amount out after Offchain simulation: 0.000000015630701406604442
2025-07-01T08:41:13.568738Z  INFO quedge::helpers: Default start amount for token AS1nqHKXpnFXqhDExTskXmBbbVpVpUbCQVtNSXLCqUDSUXihdWRq is 40207
2025-07-01T08:41:13.568816Z  INFO quedge::helpers: Offchain Earning percentage: -99.9999999843693
2025-07-01T08:41:13.568893Z  INFO quedge::helpers: Offchain Amount out after Offchain simulation: 0.00007765593260211698
2025-07-01T08:41:13.568962Z  WARN quedge::helpers: Not Valid Cycle Because Offchain Earning Percentage is less than margin -99.9999999843693 < 0.1
2025-07-01T08:41:13.569030Z  INFO quedge::helpers: Offchain Earning percentage: -99.99999980685968
2025-07-01T08:41:13.569109Z  WARN quedge::helpers: Not Valid Cycle Because Offchain Earning Percentage is less than margin -99.99999980685968 < 0.1
2025-07-01T08:41:13.569218Z  INFO quedge::helpers: Found 0 valid cycles
2025-07-01T08:41:13.569293Z  WARN quedge::helpers: No profitable arbitrage cycle found.
2025-07-01T08:41:22.659587Z  WARN quedge::spawns::swap_fetcher: Operation O1mHu7CrYsWVChSd1afFteiSrT4aB98BCCr7X4n5jeguGivqZXu is not successful, skipping
2025-07-01T08:42:49.140181Z  INFO quedge::spawns::swap_fetcher: Found 1 Swap events from the operation O1ak8ttM9Gf9JqtDDQy2EGo7pL1VYABtj5XVnk9RGwRxb9b2mW6. Processing them...
2025-07-01T08:42:49.140355Z DEBUG quedge::spawns::swap_fetcher: Processing last event of the operation O1ak8ttM9Gf9JqtDDQy2EGo7pL1VYABtj5XVnk9RGwRxb9b2mW6...
2025-07-01T08:42:49.140559Z  WARN quedge::spawns::swap_fetcher: Pool AS12UMSUxgpRBB6ArZDJ19arHoxNkkpdfofQGekAiAJqsuE6PEFJy not found in the pools hash map
2025-07-01T08:42:49.140720Z  INFO quedge::spawns::swap_fetcher: New detected swap on pool: AS1F2SAbhvknGLQKzfjaizKiZhEEd6mS1HLpZqPc2atzKE4VTNv9
2025-07-01T08:42:49.449928Z  INFO quedge::strategies::bellman_ford_matrix: Updating sparse matrix by pool: Pool { address: "AS1F2SAbhvknGLQKzfjaizKiZhEEd6mS1HLpZqPc2atzKE4VTNv9", a_token: Token { address: "AS12LKs9txoSSy8JgFJgV96m8k5z9pgzjYMYSshwN67mFVuj3bdUV", name: "Bsc USDT", symbol: "USDT.b", decimals: 18, dex: Dusa }, b_token: Token { address: "AS1hCJXjndR4c9vekLWsXGnrdigp4AaZ7uYG3UKFzzKnWVsrNLPJ", name: "USD Coin", symbol: "USDC.e", decimals: 6, dex: Dusa }, a_reserve: 586.4254745580231, b_reserve: 31049.141472, dex: Dusa, swap_fee: 0.0, a_price: 1.0009030062984086, b_price: 0.999097808386301, active_bin_id: 8112293, bin_step: 1, bins: {8112292: Bin { id: 8112292, a_reserve: 0.0, b_reserve: 27.010864 }, 8112293: Bin { id: 8112293, a_reserve: 200.05883885768355, b_reserve: 25344.739628 }, 8112294: Bin { id: 8112294, a_reserve: 263.21335905959023, b_reserve: 0.0 }} }
2025-07-01T08:42:49.450200Z  INFO quedge::spawns::swap_fetcher: Swap event processed successfully
2025-07-01T08:42:49.450425Z DEBUG quedge::helpers: Detecting arbitrage cycles...
2025-07-01T08:42:49.463453Z DEBUG quedge::helpers: Arbitrage cycles found (4 total):
2025-07-01T08:42:49.463647Z DEBUG quedge::helpers: Cycle path Inisde Simulation Task:
2025-07-01T08:42:49.463757Z DEBUG quedge::helpers::display: Path: ["WMAS-Dusa", "WMAS-EagleFi", "POM-EagleFi", "POM-Dusa", "WMAS-Dusa"]
2025-07-01T08:42:49.463936Z DEBUG quedge::helpers: Cycle path Inisde Simulation Task:
2025-07-01T08:42:49.464045Z DEBUG quedge::helpers::display: Path: ["POM-Dusa", "WMAS-Dusa", "WMAS-EagleFi", "POM-EagleFi", "POM-Dusa"]
2025-07-01T08:42:49.464191Z DEBUG quedge::helpers: Cycle path Inisde Simulation Task:
2025-07-01T08:42:49.464273Z  INFO quedge::helpers: Default start amount for token AS12U4TZfNK7qoLyEERBBRDMu8nm5MKoRzPXDXans4v9wdATZedz9 is 100
2025-07-01T08:42:49.464347Z DEBUG quedge::helpers::display: Path: ["WMAS-EagleFi", "POM-EagleFi", "POM-Dusa", "WMAS-Dusa", "WMAS-EagleFi"]
2025-07-01T08:42:49.464416Z  INFO quedge::helpers: Offchain Amount out after Offchain simulation: 0.000000015630701406604442
2025-07-01T08:42:49.464501Z DEBUG quedge::helpers: Cycle path Inisde Simulation Task:
2025-07-01T08:42:49.464586Z  INFO quedge::helpers: Offchain Earning percentage: -99.9999999843693
2025-07-01T08:42:49.464659Z  INFO quedge::helpers: Default start amount for token AS1nqHKXpnFXqhDExTskXmBbbVpVpUbCQVtNSXLCqUDSUXihdWRq is 40207
2025-07-01T08:42:49.464731Z DEBUG quedge::helpers::display: Path: ["POM-EagleFi", "POM-Dusa", "WMAS-Dusa", "WMAS-EagleFi", "POM-EagleFi"]
2025-07-01T08:42:49.464806Z  WARN quedge::helpers: Not Valid Cycle Because Offchain Earning Percentage is less than margin -99.9999999843693 < 0.1
2025-07-01T08:42:49.464879Z  INFO quedge::helpers: Default start amount for token AS1nqHKXpnFXqhDExTskXmBbbVpVpUbCQVtNSXLCqUDSUXihdWRq is 40207
2025-07-01T08:42:49.464978Z  INFO quedge::helpers: Offchain Amount out after Offchain simulation: 0.00007765593260211698
2025-07-01T08:42:49.465056Z  INFO quedge::helpers: Default start amount for token AS12U4TZfNK7qoLyEERBBRDMu8nm5MKoRzPXDXans4v9wdATZedz9 is 100
2025-07-01T08:42:49.465127Z  INFO quedge::helpers: Offchain Amount out after Offchain simulation: 0.00007765593260211698
2025-07-01T08:42:49.465198Z  INFO quedge::helpers: Offchain Earning percentage: -99.99999980685968
2025-07-01T08:42:49.465268Z  INFO quedge::helpers: Offchain Amount out after Offchain simulation: 0.000000015630701406604442
2025-07-01T08:42:49.465338Z  INFO quedge::helpers: Offchain Earning percentage: -99.99999980685968
2025-07-01T08:42:49.465407Z  WARN quedge::helpers: Not Valid Cycle Because Offchain Earning Percentage is less than margin -99.99999980685968 < 0.1
2025-07-01T08:42:49.465476Z  INFO quedge::helpers: Offchain Earning percentage: -99.9999999843693
2025-07-01T08:42:49.465549Z  WARN quedge::helpers: Not Valid Cycle Because Offchain Earning Percentage is less than margin -99.99999980685968 < 0.1
2025-07-01T08:42:49.465627Z  WARN quedge::helpers: Not Valid Cycle Because Offchain Earning Percentage is less than margin -99.9999999843693 < 0.1
2025-07-01T08:42:49.465737Z  INFO quedge::helpers: Found 0 valid cycles
2025-07-01T08:42:49.465814Z  WARN quedge::helpers: No profitable arbitrage cycle found.
2025-07-01T08:42:58.654203Z  WARN quedge::spawns::swap_fetcher: Operation O123ECTQV6y7NeWRJG4bo54Hw9tKfwrEiYnZ5wJo8c5ua4NFVd6b is not successful, skipping
2025-07-01T08:45:13.143867Z  WARN quedge::spawns::swap_fetcher: Operation O1239BtXBux2qjoNqKMPAT1CpZyZ5HfYPBJFhndKUEkSaZwq1C72 is not successful, skipping
2025-07-01T08:47:37.144291Z  WARN quedge::spawns::swap_fetcher: Operation O12hD6NXdRBvQSH19kxBqnvrxB7PVjaD8uXNq3jGMFHbW2qqfYPZ is not successful, skipping
2025-07-01T08:49:29.164626Z  INFO quedge::spawns::swap_fetcher: Found 1 Swap events from the operation O12NCW49EHj3WvMyrQvJpjFTdDJ1PRNiksbwgzcL1fYh1w7rswRM. Processing them...
2025-07-01T08:49:29.164758Z DEBUG quedge::spawns::swap_fetcher: Processing last event of the operation O12NCW49EHj3WvMyrQvJpjFTdDJ1PRNiksbwgzcL1fYh1w7rswRM...
2025-07-01T08:49:29.164864Z  WARN quedge::spawns::swap_fetcher: Pool AS12UMSUxgpRBB6ArZDJ19arHoxNkkpdfofQGekAiAJqsuE6PEFJy not found in the pools hash map
2025-07-01T08:49:29.164961Z  INFO quedge::spawns::swap_fetcher: New detected swap on pool: AS1F2SAbhvknGLQKzfjaizKiZhEEd6mS1HLpZqPc2atzKE4VTNv9
2025-07-01T08:49:29.596820Z  INFO quedge::strategies::bellman_ford_matrix: Updating sparse matrix by pool: Pool { address: "AS1F2SAbhvknGLQKzfjaizKiZhEEd6mS1HLpZqPc2atzKE4VTNv9", a_token: Token { address: "AS12LKs9txoSSy8JgFJgV96m8k5z9pgzjYMYSshwN67mFVuj3bdUV", name: "Bsc USDT", symbol: "USDT.b", decimals: 18, dex: Dusa }, b_token: Token { address: "AS1hCJXjndR4c9vekLWsXGnrdigp4AaZ7uYG3UKFzzKnWVsrNLPJ", name: "USD Coin", symbol: "USDC.e", decimals: 6, dex: Dusa }, a_reserve: 514.7407443207088, b_reserve: 31120.890934, dex: Dusa, swap_fee: 0.0, a_price: 1.0009030062984086, b_price: 0.999097808386301, active_bin_id: 8112293, bin_step: 1, bins: {8112292: Bin { id: 8112292, a_reserve: 0.0, b_reserve: 27.010864 }, 8112293: Bin { id: 8112293, a_reserve: 128.37410862036924, b_reserve: 25416.48909 }, 8112294: Bin { id: 8112294, a_reserve: 263.21335905959023, b_reserve: 0.0 }} }
2025-07-01T08:49:29.597045Z  INFO quedge::spawns::swap_fetcher: Swap event processed successfully
2025-07-01T08:49:29.597360Z DEBUG quedge::helpers: Detecting arbitrage cycles...
2025-07-01T08:49:29.609313Z DEBUG quedge::helpers: Arbitrage cycles found (4 total):
2025-07-01T08:49:29.609479Z DEBUG quedge::helpers: Cycle path Inisde Simulation Task:
2025-07-01T08:49:29.609588Z DEBUG quedge::helpers::display: Path: ["WMAS-Dusa", "WMAS-EagleFi", "POM-EagleFi", "POM-Dusa", "WMAS-Dusa"]
2025-07-01T08:49:29.609812Z DEBUG quedge::helpers: Cycle path Inisde Simulation Task:
2025-07-01T08:49:29.609942Z  INFO quedge::helpers: Default start amount for token AS12U4TZfNK7qoLyEERBBRDMu8nm5MKoRzPXDXans4v9wdATZedz9 is 100
2025-07-01T08:49:29.610085Z DEBUG quedge::helpers::display: Path: ["POM-Dusa", "WMAS-Dusa", "WMAS-EagleFi", "POM-EagleFi", "POM-Dusa"]
2025-07-01T08:49:29.610218Z  INFO quedge::helpers: Offchain Amount out after Offchain simulation: 0.000000015630701406604442
2025-07-01T08:49:29.610388Z DEBUG quedge::helpers: Cycle path Inisde Simulation Task:
2025-07-01T08:49:29.610520Z  INFO quedge::helpers: Default start amount for token AS1nqHKXpnFXqhDExTskXmBbbVpVpUbCQVtNSXLCqUDSUXihdWRq is 40207
2025-07-01T08:49:29.610652Z  INFO quedge::helpers: Offchain Amount out after Offchain simulation: 0.00007765593260211698
2025-07-01T08:49:29.610765Z DEBUG quedge::helpers::display: Path: ["WMAS-EagleFi", "POM-EagleFi", "POM-Dusa", "WMAS-Dusa", "WMAS-EagleFi"]
2025-07-01T08:49:29.610910Z  INFO quedge::helpers: Offchain Earning percentage: -99.9999999843693
2025-07-01T08:49:29.611023Z  INFO quedge::helpers: Offchain Earning percentage: -99.99999980685968
2025-07-01T08:49:29.611108Z DEBUG quedge::helpers: Cycle path Inisde Simulation Task:
2025-07-01T08:49:29.611191Z  INFO quedge::helpers: Default start amount for token AS12U4TZfNK7qoLyEERBBRDMu8nm5MKoRzPXDXans4v9wdATZedz9 is 100
2025-07-01T08:49:29.611271Z  WARN quedge::helpers: Not Valid Cycle Because Offchain Earning Percentage is less than margin -99.9999999843693 < 0.1
2025-07-01T08:49:29.611350Z  WARN quedge::helpers: Not Valid Cycle Because Offchain Earning Percentage is less than margin -99.99999980685968 < 0.1
2025-07-01T08:49:29.611422Z DEBUG quedge::helpers::display: Path: ["POM-EagleFi", "POM-Dusa", "WMAS-Dusa", "WMAS-EagleFi", "POM-EagleFi"]
2025-07-01T08:49:29.611491Z  INFO quedge::helpers: Offchain Amount out after Offchain simulation: 0.000000015630701406604442
2025-07-01T08:49:29.611593Z  INFO quedge::helpers: Default start amount for token AS1nqHKXpnFXqhDExTskXmBbbVpVpUbCQVtNSXLCqUDSUXihdWRq is 40207
2025-07-01T08:49:29.611722Z  INFO quedge::helpers: Offchain Earning percentage: -99.9999999843693
2025-07-01T08:49:29.611804Z  INFO quedge::helpers: Offchain Amount out after Offchain simulation: 0.00007765593260211698
2025-07-01T08:49:29.611873Z  WARN quedge::helpers: Not Valid Cycle Because Offchain Earning Percentage is less than margin -99.9999999843693 < 0.1
2025-07-01T08:49:29.611935Z  INFO quedge::helpers: Offchain Earning percentage: -99.99999980685968
2025-07-01T08:49:29.612022Z  WARN quedge::helpers: Not Valid Cycle Because Offchain Earning Percentage is less than margin -99.99999980685968 < 0.1
2025-07-01T08:49:29.612130Z  INFO quedge::helpers: Found 0 valid cycles
2025-07-01T08:49:29.612209Z  WARN quedge::helpers: No profitable arbitrage cycle found.
2025-07-01T08:49:38.807145Z  WARN quedge::spawns::swap_fetcher: Operation O12j3rfkiMMUTyP8WJ7mW19oETVAnPZ3rxtPsoqXNhK52Esncj3u is not successful, skipping
2025-07-01T08:51:05.155049Z  INFO quedge::spawns::swap_fetcher: Found 1 Swap events from the operation O1jYfF9TqKqCeMBhvQNLiWxVXXeYrLR94kVKrXQFikJ3nA5RPTW. Processing them...
2025-07-01T08:51:05.155296Z DEBUG quedge::spawns::swap_fetcher: Processing last event of the operation O1jYfF9TqKqCeMBhvQNLiWxVXXeYrLR94kVKrXQFikJ3nA5RPTW...
2025-07-01T08:51:05.155418Z  WARN quedge::spawns::swap_fetcher: Pool AS12UMSUxgpRBB6ArZDJ19arHoxNkkpdfofQGekAiAJqsuE6PEFJy not found in the pools hash map
2025-07-01T08:51:05.155518Z  INFO quedge::spawns::swap_fetcher: New detected swap on pool: AS1F2SAbhvknGLQKzfjaizKiZhEEd6mS1HLpZqPc2atzKE4VTNv9
2025-07-01T08:51:05.588083Z  INFO quedge::strategies::bellman_ford_matrix: Updating sparse matrix by pool: Pool { address: "AS1F2SAbhvknGLQKzfjaizKiZhEEd6mS1HLpZqPc2atzKE4VTNv9", a_token: Token { address: "AS12LKs9txoSSy8JgFJgV96m8k5z9pgzjYMYSshwN67mFVuj3bdUV", name: "Bsc USDT", symbol: "USDT.b", decimals: 18, dex: Dusa }, b_token: Token { address: "AS1hCJXjndR4c9vekLWsXGnrdigp4AaZ7uYG3UKFzzKnWVsrNLPJ", name: "USD Coin", symbol: "USDC.e", decimals: 6, dex: Dusa }, a_reserve: 581.3992690064371, b_reserve: 31054.172217, dex: Dusa, swap_fee: 0.0, a_price: 1.0009030062984086, b_price: 0.999097808386301, active_bin_id: 8112293, bin_step: 1, bins: {8112292: Bin { id: 8112292, a_reserve: 0.0, b_reserve: 27.010864 }, 8112293: Bin { id: 8112293, a_reserve: 195.0326333060975, b_reserve: 25349.770373 }, 8112294: Bin { id: 8112294, a_reserve: 263.21335905959023, b_reserve: 0.0 }} }
2025-07-01T08:51:05.588278Z  INFO quedge::spawns::swap_fetcher: Swap event processed successfully
2025-07-01T08:51:05.588463Z DEBUG quedge::helpers: Detecting arbitrage cycles...
2025-07-01T08:51:05.600241Z DEBUG quedge::helpers: Arbitrage cycles found (4 total):
2025-07-01T08:51:05.600406Z DEBUG quedge::helpers: Cycle path Inisde Simulation Task:
2025-07-01T08:51:05.600501Z DEBUG quedge::helpers::display: Path: ["WMAS-Dusa", "WMAS-EagleFi", "POM-EagleFi", "POM-Dusa", "WMAS-Dusa"]
2025-07-01T08:51:05.600673Z DEBUG quedge::helpers: Cycle path Inisde Simulation Task:
2025-07-01T08:51:05.600763Z  INFO quedge::helpers: Default start amount for token AS12U4TZfNK7qoLyEERBBRDMu8nm5MKoRzPXDXans4v9wdATZedz9 is 100
2025-07-01T08:51:05.600850Z DEBUG quedge::helpers::display: Path: ["POM-Dusa", "WMAS-Dusa", "WMAS-EagleFi", "POM-EagleFi", "POM-Dusa"]
2025-07-01T08:51:05.600970Z  INFO quedge::helpers: Offchain Amount out after Offchain simulation: 0.000000015630701406604442
2025-07-01T08:51:05.601065Z  INFO quedge::helpers: Default start amount for token AS1nqHKXpnFXqhDExTskXmBbbVpVpUbCQVtNSXLCqUDSUXihdWRq is 40207
2025-07-01T08:51:05.601141Z DEBUG quedge::helpers: Cycle path Inisde Simulation Task:
2025-07-01T08:51:05.601214Z  INFO quedge::helpers: Offchain Earning percentage: -99.9999999843693
2025-07-01T08:51:05.601285Z  INFO quedge::helpers: Offchain Amount out after Offchain simulation: 0.00007765593260211698
2025-07-01T08:51:05.601356Z DEBUG quedge::helpers::display: Path: ["WMAS-EagleFi", "POM-EagleFi", "POM-Dusa", "WMAS-Dusa", "WMAS-EagleFi"]
2025-07-01T08:51:05.601482Z  WARN quedge::helpers: Not Valid Cycle Because Offchain Earning Percentage is less than margin -99.9999999843693 < 0.1
2025-07-01T08:51:05.601564Z  INFO quedge::helpers: Offchain Earning percentage: -99.99999980685968
2025-07-01T08:51:05.601635Z DEBUG quedge::helpers: Cycle path Inisde Simulation Task:
2025-07-01T08:51:05.601707Z  INFO quedge::helpers: Default start amount for token AS12U4TZfNK7qoLyEERBBRDMu8nm5MKoRzPXDXans4v9wdATZedz9 is 100
2025-07-01T08:51:05.601776Z  WARN quedge::helpers: Not Valid Cycle Because Offchain Earning Percentage is less than margin -99.99999980685968 < 0.1
2025-07-01T08:51:05.601897Z DEBUG quedge::helpers::display: Path: ["POM-EagleFi", "POM-Dusa", "WMAS-Dusa", "WMAS-EagleFi", "POM-EagleFi"]
2025-07-01T08:51:05.602029Z  INFO quedge::helpers: Offchain Amount out after Offchain simulation: 0.000000015630701406604442
2025-07-01T08:51:05.602112Z  INFO quedge::helpers: Default start amount for token AS1nqHKXpnFXqhDExTskXmBbbVpVpUbCQVtNSXLCqUDSUXihdWRq is 40207
2025-07-01T08:51:05.602189Z  INFO quedge::helpers: Offchain Earning percentage: -99.9999999843693
2025-07-01T08:51:05.602301Z  INFO quedge::helpers: Offchain Amount out after Offchain simulation: 0.00007765593260211698
2025-07-01T08:51:05.602373Z  WARN quedge::helpers: Not Valid Cycle Because Offchain Earning Percentage is less than margin -99.9999999843693 < 0.1
2025-07-01T08:51:05.602439Z  INFO quedge::helpers: Offchain Earning percentage: -99.99999980685968
2025-07-01T08:51:05.602530Z  WARN quedge::helpers: Not Valid Cycle Because Offchain Earning Percentage is less than margin -99.99999980685968 < 0.1
2025-07-01T08:51:05.602639Z  INFO quedge::helpers: Found 0 valid cycles
2025-07-01T08:51:05.602726Z  WARN quedge::helpers: No profitable arbitrage cycle found.
2025-07-01T08:51:14.796027Z  WARN quedge::spawns::swap_fetcher: Operation O1ALAX3zfePrXn6E2ed9zZFzNMjXX3wbMgVkX8VRSuXhUQNVNT6 is not successful, skipping
2025-07-01T08:53:13.160944Z  WARN quedge::spawns::swap_fetcher: Operation O1xra8vYuKmoXTK9JDsiT9jBVZUEDSPVZhLZjtfaqRk7VMGwzok is not successful, skipping
2025-07-01T08:54:32.420967Z DEBUG quedge::spawns::balance_notifier: Balance of AU16raXifo3FExijWrhUBLBneaHnWGicWbShgyXgAKB1bnj1yLvH is 110.19030328
2025-07-01T08:55:21.176840Z  INFO quedge::spawns::swap_fetcher: Found 1 Swap events from the operation O12BVWjh4Z61KHoANuMkyaV7aWBkX3tpqULhLemd8zp5AsyYVNS4. Processing them...
2025-07-01T08:55:21.176952Z DEBUG quedge::spawns::swap_fetcher: Processing last event of the operation O12BVWjh4Z61KHoANuMkyaV7aWBkX3tpqULhLemd8zp5AsyYVNS4...
2025-07-01T08:55:21.177043Z  WARN quedge::spawns::swap_fetcher: Pool AS12UMSUxgpRBB6ArZDJ19arHoxNkkpdfofQGekAiAJqsuE6PEFJy not found in the pools hash map
2025-07-01T08:55:21.177135Z  INFO quedge::spawns::swap_fetcher: New detected swap on pool: AS1F2SAbhvknGLQKzfjaizKiZhEEd6mS1HLpZqPc2atzKE4VTNv9
2025-07-01T08:55:21.786816Z  INFO quedge::strategies::bellman_ford_matrix: Updating sparse matrix by pool: Pool { address: "AS1F2SAbhvknGLQKzfjaizKiZhEEd6mS1HLpZqPc2atzKE4VTNv9", a_token: Token { address: "AS12LKs9txoSSy8JgFJgV96m8k5z9pgzjYMYSshwN67mFVuj3bdUV", name: "Bsc USDT", symbol: "USDT.b", decimals: 18, dex: Dusa }, b_token: Token { address: "AS1hCJXjndR4c9vekLWsXGnrdigp4AaZ7uYG3UKFzzKnWVsrNLPJ", name: "USD Coin", symbol: "USDC.e", decimals: 6, dex: Dusa }, a_reserve: 552.8293308404591, b_reserve: 31082.767954, dex: Dusa, swap_fee: 0.0, a_price: 1.0009030062984086, b_price: 0.999097808386301, active_bin_id: 8112293, bin_step: 1, bins: {8112292: Bin { id: 8112292, a_reserve: 0.0, b_reserve: 27.010864 }, 8112293: Bin { id: 8112293, a_reserve: 166.4626951401195, b_reserve: 25378.36611 }, 8112294: Bin { id: 8112294, a_reserve: 263.21335905959023, b_reserve: 0.0 }} }
2025-07-01T08:55:21.787079Z  INFO quedge::spawns::swap_fetcher: Swap event processed successfully
2025-07-01T08:55:21.787439Z DEBUG quedge::helpers: Detecting arbitrage cycles...
2025-07-01T08:55:21.799598Z DEBUG quedge::helpers: Arbitrage cycles found (4 total):
2025-07-01T08:55:21.799821Z DEBUG quedge::helpers: Cycle path Inisde Simulation Task:
2025-07-01T08:55:21.799960Z DEBUG quedge::helpers::display: Path: ["WMAS-Dusa", "WMAS-EagleFi", "POM-EagleFi", "POM-Dusa", "WMAS-Dusa"]
2025-07-01T08:55:21.800256Z DEBUG quedge::helpers: Cycle path Inisde Simulation Task:
2025-07-01T08:55:21.800433Z  INFO quedge::helpers: Default start amount for token AS12U4TZfNK7qoLyEERBBRDMu8nm5MKoRzPXDXans4v9wdATZedz9 is 100
2025-07-01T08:55:21.800579Z DEBUG quedge::helpers::display: Path: ["POM-Dusa", "WMAS-Dusa", "WMAS-EagleFi", "POM-EagleFi", "POM-Dusa"]
2025-07-01T08:55:21.800755Z  INFO quedge::helpers: Offchain Amount out after Offchain simulation: 0.000000015630701406604442
2025-07-01T08:55:21.800884Z DEBUG quedge::helpers: Cycle path Inisde Simulation Task:
2025-07-01T08:55:21.800994Z  INFO quedge::helpers: Default start amount for token AS1nqHKXpnFXqhDExTskXmBbbVpVpUbCQVtNSXLCqUDSUXihdWRq is 40207
2025-07-01T08:55:21.801101Z  INFO quedge::helpers: Offchain Earning percentage: -99.9999999843693
2025-07-01T08:55:21.801189Z DEBUG quedge::helpers::display: Path: ["WMAS-EagleFi", "POM-EagleFi", "POM-Dusa", "WMAS-Dusa", "WMAS-EagleFi"]
2025-07-01T08:55:21.801268Z  INFO quedge::helpers: Offchain Amount out after Offchain simulation: 0.00007765593260211698
2025-07-01T08:55:21.801339Z  WARN quedge::helpers: Not Valid Cycle Because Offchain Earning Percentage is less than margin -99.9999999843693 < 0.1
2025-07-01T08:55:21.801416Z DEBUG quedge::helpers: Cycle path Inisde Simulation Task:
2025-07-01T08:55:21.801490Z  INFO quedge::helpers: Offchain Earning percentage: -99.99999980685968
2025-07-01T08:55:21.801560Z  INFO quedge::helpers: Default start amount for token AS12U4TZfNK7qoLyEERBBRDMu8nm5MKoRzPXDXans4v9wdATZedz9 is 100
2025-07-01T08:55:21.801633Z DEBUG quedge::helpers::display: Path: ["POM-EagleFi", "POM-Dusa", "WMAS-Dusa", "WMAS-EagleFi", "POM-EagleFi"]
2025-07-01T08:55:21.801715Z  WARN quedge::helpers: Not Valid Cycle Because Offchain Earning Percentage is less than margin -99.99999980685968 < 0.1
2025-07-01T08:55:21.801797Z  INFO quedge::helpers: Offchain Amount out after Offchain simulation: 0.000000015630701406604442
2025-07-01T08:55:21.801868Z  INFO quedge::helpers: Default start amount for token AS1nqHKXpnFXqhDExTskXmBbbVpVpUbCQVtNSXLCqUDSUXihdWRq is 40207
2025-07-01T08:55:21.801938Z  INFO quedge::helpers: Offchain Earning percentage: -99.9999999843693
2025-07-01T08:55:21.802008Z  INFO quedge::helpers: Offchain Amount out after Offchain simulation: 0.00007765593260211698
2025-07-01T08:55:21.802079Z  WARN quedge::helpers: Not Valid Cycle Because Offchain Earning Percentage is less than margin -99.9999999843693 < 0.1
2025-07-01T08:55:21.802148Z  INFO quedge::helpers: Offchain Earning percentage: -99.99999980685968
2025-07-01T08:55:21.802224Z  WARN quedge::helpers: Not Valid Cycle Because Offchain Earning Percentage is less than margin -99.99999980685968 < 0.1
2025-07-01T08:55:21.802331Z  INFO quedge::helpers: Found 0 valid cycles
2025-07-01T08:55:21.802406Z  WARN quedge::helpers: No profitable arbitrage cycle found.
2025-07-01T08:55:46.842428Z  WARN quedge::spawns::swap_fetcher: Operation O1tQ2U13SMkBmd3sLhzyJdWN7A2wVncZWRq2c7fnyeH5d57evnz is not successful, skipping
2025-07-01T08:56:41.166750Z  INFO quedge::spawns::swap_fetcher: Found 1 Swap events from the operation O1BrZs8hw9ffqgqtkabpsR1P9M3WeQtynpSPwCuEXYDvQirhd4i. Processing them...
2025-07-01T08:56:41.166891Z DEBUG quedge::spawns::swap_fetcher: Processing last event of the operation O1BrZs8hw9ffqgqtkabpsR1P9M3WeQtynpSPwCuEXYDvQirhd4i...
2025-07-01T08:56:41.166987Z  WARN quedge::spawns::swap_fetcher: Pool AS12UMSUxgpRBB6ArZDJ19arHoxNkkpdfofQGekAiAJqsuE6PEFJy not found in the pools hash map
2025-07-01T08:56:41.167069Z  INFO quedge::spawns::swap_fetcher: New detected swap on pool: AS1F2SAbhvknGLQKzfjaizKiZhEEd6mS1HLpZqPc2atzKE4VTNv9
2025-07-01T08:56:41.530479Z  INFO quedge::strategies::bellman_ford_matrix: Updating sparse matrix by pool: Pool { address: "AS1F2SAbhvknGLQKzfjaizKiZhEEd6mS1HLpZqPc2atzKE4VTNv9", a_token: Token { address: "AS12LKs9txoSSy8JgFJgV96m8k5z9pgzjYMYSshwN67mFVuj3bdUV", name: "Bsc USDT", symbol: "USDT.b", decimals: 18, dex: Dusa }, b_token: Token { address: "AS1hCJXjndR4c9vekLWsXGnrdigp4AaZ7uYG3UKFzzKnWVsrNLPJ", name: "USD Coin", symbol: "USDC.e", decimals: 6, dex: Dusa }, a_reserve: 619.0016563751294, b_reserve: 31016.535875, dex: Dusa, swap_fee: 0.0, a_price: 1.0009030062984086, b_price: 0.999097808386301, active_bin_id: 8112293, bin_step: 1, bins: {8112292: Bin { id: 8112292, a_reserve: 0.0, b_reserve: 27.010864 }, 8112293: Bin { id: 8112293, a_reserve: 232.6350206747899, b_reserve: 25312.134031 }, 8112294: Bin { id: 8112294, a_reserve: 263.21335905959023, b_reserve: 0.0 }} }
2025-07-01T08:56:41.530701Z  INFO quedge::spawns::swap_fetcher: Swap event processed successfully
2025-07-01T08:56:41.530914Z DEBUG quedge::helpers: Detecting arbitrage cycles...
2025-07-01T08:56:41.542423Z DEBUG quedge::helpers: Arbitrage cycles found (4 total):
2025-07-01T08:56:41.542566Z DEBUG quedge::helpers: Cycle path Inisde Simulation Task:
2025-07-01T08:56:41.542684Z DEBUG quedge::helpers::display: Path: ["WMAS-Dusa", "WMAS-EagleFi", "POM-EagleFi", "POM-Dusa", "WMAS-Dusa"]
2025-07-01T08:56:41.542884Z DEBUG quedge::helpers: Cycle path Inisde Simulation Task:
2025-07-01T08:56:41.543025Z  INFO quedge::helpers: Default start amount for token AS12U4TZfNK7qoLyEERBBRDMu8nm5MKoRzPXDXans4v9wdATZedz9 is 100
2025-07-01T08:56:41.543095Z DEBUG quedge::helpers::display: Path: ["POM-Dusa", "WMAS-Dusa", "WMAS-EagleFi", "POM-EagleFi", "POM-Dusa"]
2025-07-01T08:56:41.543274Z  INFO quedge::helpers: Offchain Amount out after Offchain simulation: 0.000000015630701406604442
2025-07-01T08:56:41.543377Z DEBUG quedge::helpers: Cycle path Inisde Simulation Task:
2025-07-01T08:56:41.543469Z  INFO quedge::helpers: Default start amount for token AS1nqHKXpnFXqhDExTskXmBbbVpVpUbCQVtNSXLCqUDSUXihdWRq is 40207
2025-07-01T08:56:41.543544Z  INFO quedge::helpers: Offchain Earning percentage: -99.9999999843693
2025-07-01T08:56:41.543613Z DEBUG quedge::helpers::display: Path: ["WMAS-EagleFi", "POM-EagleFi", "POM-Dusa", "WMAS-Dusa", "WMAS-EagleFi"]
2025-07-01T08:56:41.543729Z  INFO quedge::helpers: Offchain Amount out after Offchain simulation: 0.00007765593260211698
2025-07-01T08:56:41.543801Z  WARN quedge::helpers: Not Valid Cycle Because Offchain Earning Percentage is less than margin -99.9999999843693 < 0.1
2025-07-01T08:56:41.543869Z DEBUG quedge::helpers: Cycle path Inisde Simulation Task:
2025-07-01T08:56:41.543942Z  INFO quedge::helpers: Default start amount for token AS12U4TZfNK7qoLyEERBBRDMu8nm5MKoRzPXDXans4v9wdATZedz9 is 100
2025-07-01T08:56:41.544011Z  INFO quedge::helpers: Offchain Earning percentage: -99.99999980685968
2025-07-01T08:56:41.544079Z DEBUG quedge::helpers::display: Path: ["POM-EagleFi", "POM-Dusa", "WMAS-Dusa", "WMAS-EagleFi", "POM-EagleFi"]
2025-07-01T08:56:41.544189Z  INFO quedge::helpers: Offchain Amount out after Offchain simulation: 0.000000015630701406604442
2025-07-01T08:56:41.544278Z  WARN quedge::helpers: Not Valid Cycle Because Offchain Earning Percentage is less than margin -99.99999980685968 < 0.1
2025-07-01T08:56:41.544373Z  INFO quedge::helpers: Default start amount for token AS1nqHKXpnFXqhDExTskXmBbbVpVpUbCQVtNSXLCqUDSUXihdWRq is 40207
2025-07-01T08:56:41.544443Z  INFO quedge::helpers: Offchain Earning percentage: -99.9999999843693
2025-07-01T08:56:41.544519Z  INFO quedge::helpers: Offchain Amount out after Offchain simulation: 0.00007765593260211698
2025-07-01T08:56:41.544629Z  WARN quedge::helpers: Not Valid Cycle Because Offchain Earning Percentage is less than margin -99.9999999843693 < 0.1
2025-07-01T08:56:41.544703Z  INFO quedge::helpers: Offchain Earning percentage: -99.99999980685968
2025-07-01T08:56:41.544786Z  WARN quedge::helpers: Not Valid Cycle Because Offchain Earning Percentage is less than margin -99.99999980685968 < 0.1
2025-07-01T08:56:41.544889Z  INFO quedge::helpers: Found 0 valid cycles
2025-07-01T08:56:41.544964Z  WARN quedge::helpers: No profitable arbitrage cycle found.
2025-07-01T08:56:50.677932Z  WARN quedge::spawns::swap_fetcher: Operation O128jn6iC1epJeLK4mLUxf7JQxuaVXagAsbZ84ingNKZ3yfBsVE6 is not successful, skipping
2025-07-01T09:03:53.166553Z  WARN quedge::spawns::swap_fetcher: Operation O12T7urxWLmG8nBX8PedLqdTnQRuFHHugxp8yGTo4Zu122PAjkaG is not successful, skipping
2025-07-01T09:06:01.178417Z  WARN quedge::spawns::swap_fetcher: Operation O12d3d1NQbPcHcAwYPZ55G5BVLJPoAHqfyzB2QdUb8DvDRENsQiV is not successful, skipping
2025-07-01T09:10:33.572526Z  WARN quedge::spawns::swap_fetcher: Operation O1xtkhyooghN4aCbwGBEssWTUHkNNjTZsC6moJvBgoBCmQ1aG57 is not successful, skipping
2025-07-01T09:15:21.371586Z  WARN quedge::spawns::swap_fetcher: Operation O1D7p8RN2q53BJEBSFJxArFiLHei9yPzgSRR715kpz9uBNUiu3A is not successful, skipping
2025-07-01T09:17:29.189683Z  WARN quedge::spawns::swap_fetcher: Operation O12M4BVSs5SKmpEXVmFRodmDe6SranENZb1iRLh4dqxqWLQ4KAtE is not successful, skipping
2025-07-01T09:19:37.196202Z  WARN quedge::spawns::swap_fetcher: Operation O1vY8cJJiB7Thy1Xygv8mqUA366Gum2J7iQ8sEb9JQCzqhjLxLP is not successful, skipping
2025-07-01T09:21:45.205328Z  INFO quedge::spawns::swap_fetcher: Found 1 Swap events from the operation O1sBAEYK6LHJtEarUiUGNt4ze1UyPhuBvCEMaKtCTn8QQrUwkos. Processing them...
2025-07-01T09:21:45.205484Z DEBUG quedge::spawns::swap_fetcher: Processing last event of the operation O1sBAEYK6LHJtEarUiUGNt4ze1UyPhuBvCEMaKtCTn8QQrUwkos...
2025-07-01T09:21:45.205618Z  WARN quedge::spawns::swap_fetcher: Pool AS12UMSUxgpRBB6ArZDJ19arHoxNkkpdfofQGekAiAJqsuE6PEFJy not found in the pools hash map
2025-07-01T09:21:45.205753Z  INFO quedge::spawns::swap_fetcher: New detected swap on pool: AS1F2SAbhvknGLQKzfjaizKiZhEEd6mS1HLpZqPc2atzKE4VTNv9
2025-07-01T09:21:45.635692Z  INFO quedge::strategies::bellman_ford_matrix: Updating sparse matrix by pool: Pool { address: "AS1F2SAbhvknGLQKzfjaizKiZhEEd6mS1HLpZqPc2atzKE4VTNv9", a_token: Token { address: "AS12LKs9txoSSy8JgFJgV96m8k5z9pgzjYMYSshwN67mFVuj3bdUV", name: "Bsc USDT", symbol: "USDT.b", decimals: 18, dex: Dusa }, b_token: Token { address: "AS1hCJXjndR4c9vekLWsXGnrdigp4AaZ7uYG3UKFzzKnWVsrNLPJ", name: "USD Coin", symbol: "USDC.e", decimals: 6, dex: Dusa }, a_reserve: 581.8556664378902, b_reserve: 31053.715408, dex: Dusa, swap_fee: 0.0, a_price: 1.0009030062984086, b_price: 0.999097808386301, active_bin_id: 8112293, bin_step: 1, bins: {8112292: Bin { id: 8112292, a_reserve: 0.0, b_reserve: 27.010864 }, 8112293: Bin { id: 8112293, a_reserve: 195.4890307375507, b_reserve: 25349.313564 }, 8112294: Bin { id: 8112294, a_reserve: 263.21335905959023, b_reserve: 0.0 }} }
2025-07-01T09:21:45.635984Z  INFO quedge::spawns::swap_fetcher: Swap event processed successfully
2025-07-01T09:21:45.636239Z DEBUG quedge::helpers: Detecting arbitrage cycles...
2025-07-01T09:21:45.648547Z DEBUG quedge::helpers: Arbitrage cycles found (4 total):
2025-07-01T09:21:45.648757Z DEBUG quedge::helpers: Cycle path Inisde Simulation Task:
2025-07-01T09:21:45.648913Z DEBUG quedge::helpers::display: Path: ["WMAS-Dusa", "WMAS-EagleFi", "POM-EagleFi", "POM-Dusa", "WMAS-Dusa"]
2025-07-01T09:21:45.649140Z DEBUG quedge::helpers: Cycle path Inisde Simulation Task:
2025-07-01T09:21:45.649286Z  INFO quedge::helpers: Default start amount for token AS12U4TZfNK7qoLyEERBBRDMu8nm5MKoRzPXDXans4v9wdATZedz9 is 100
2025-07-01T09:21:45.649376Z DEBUG quedge::helpers::display: Path: ["POM-Dusa", "WMAS-Dusa", "WMAS-EagleFi", "POM-EagleFi", "POM-Dusa"]
2025-07-01T09:21:45.649470Z  INFO quedge::helpers: Offchain Amount out after Offchain simulation: 0.000000015630701406604442
2025-07-01T09:21:45.649574Z DEBUG quedge::helpers: Cycle path Inisde Simulation Task:
2025-07-01T09:21:45.649695Z  INFO quedge::helpers: Offchain Earning percentage: -99.9999999843693
2025-07-01T09:21:45.649820Z  INFO quedge::helpers: Default start amount for token AS1nqHKXpnFXqhDExTskXmBbbVpVpUbCQVtNSXLCqUDSUXihdWRq is 40207
2025-07-01T09:21:45.649909Z DEBUG quedge::helpers::display: Path: ["WMAS-EagleFi", "POM-EagleFi", "POM-Dusa", "WMAS-Dusa", "WMAS-EagleFi"]
2025-07-01T09:21:45.650013Z  WARN quedge::helpers: Not Valid Cycle Because Offchain Earning Percentage is less than margin -99.9999999843693 < 0.1
2025-07-01T09:21:45.650129Z  INFO quedge::helpers: Offchain Amount out after Offchain simulation: 0.00007765593260211698
2025-07-01T09:21:45.650217Z DEBUG quedge::helpers: Cycle path Inisde Simulation Task:
2025-07-01T09:21:45.650301Z  INFO quedge::helpers: Default start amount for token AS12U4TZfNK7qoLyEERBBRDMu8nm5MKoRzPXDXans4v9wdATZedz9 is 100
2025-07-01T09:21:45.650416Z  INFO quedge::helpers: Offchain Earning percentage: -99.99999980685968
2025-07-01T09:21:45.650503Z DEBUG quedge::helpers::display: Path: ["POM-EagleFi", "POM-Dusa", "WMAS-Dusa", "WMAS-EagleFi", "POM-EagleFi"]
2025-07-01T09:21:45.650603Z  INFO quedge::helpers: Offchain Amount out after Offchain simulation: 0.000000015630701406604442
2025-07-01T09:21:45.650691Z  INFO quedge::helpers: Default start amount for token AS1nqHKXpnFXqhDExTskXmBbbVpVpUbCQVtNSXLCqUDSUXihdWRq is 40207
2025-07-01T09:21:45.650779Z  WARN quedge::helpers: Not Valid Cycle Because Offchain Earning Percentage is less than margin -99.99999980685968 < 0.1
2025-07-01T09:21:45.650872Z  INFO quedge::helpers: Offchain Earning percentage: -99.9999999843693
2025-07-01T09:21:45.651005Z  INFO quedge::helpers: Offchain Amount out after Offchain simulation: 0.00007765593260211698
2025-07-01T09:21:45.651090Z  WARN quedge::helpers: Not Valid Cycle Because Offchain Earning Percentage is less than margin -99.9999999843693 < 0.1
2025-07-01T09:21:45.651167Z  INFO quedge::helpers: Offchain Earning percentage: -99.99999980685968
2025-07-01T09:21:45.651260Z  WARN quedge::helpers: Not Valid Cycle Because Offchain Earning Percentage is less than margin -99.99999980685968 < 0.1
2025-07-01T09:21:45.651422Z  INFO quedge::helpers: Found 0 valid cycles
2025-07-01T09:21:45.651527Z  WARN quedge::helpers: No profitable arbitrage cycle found.
2025-07-01T09:21:54.717681Z  WARN quedge::spawns::swap_fetcher: Operation O1TtamnkSWDtydaZL58DJ2yUDqmYERYPUjYxVDLoFhiukqdxgFf is not successful, skipping
2025-07-01T09:25:13.204817Z  INFO quedge::spawns::swap_fetcher: Found 1 Swap events from the operation O1zdXQmacn6SAUHUbRZKMwKTYbuQfUw8wVanuzFJpzXvmHavPpx. Processing them...
2025-07-01T09:25:13.204983Z DEBUG quedge::spawns::swap_fetcher: Processing last event of the operation O1zdXQmacn6SAUHUbRZKMwKTYbuQfUw8wVanuzFJpzXvmHavPpx...
2025-07-01T09:25:13.205140Z  WARN quedge::spawns::swap_fetcher: Pool AS12UMSUxgpRBB6ArZDJ19arHoxNkkpdfofQGekAiAJqsuE6PEFJy not found in the pools hash map
2025-07-01T09:25:13.205250Z  INFO quedge::spawns::swap_fetcher: New detected swap on pool: AS1F2SAbhvknGLQKzfjaizKiZhEEd6mS1HLpZqPc2atzKE4VTNv9
2025-07-01T09:25:13.618646Z  INFO quedge::strategies::bellman_ford_matrix: Updating sparse matrix by pool: Pool { address: "AS1F2SAbhvknGLQKzfjaizKiZhEEd6mS1HLpZqPc2atzKE4VTNv9", a_token: Token { address: "AS12LKs9txoSSy8JgFJgV96m8k5z9pgzjYMYSshwN67mFVuj3bdUV", name: "Bsc USDT", symbol: "USDT.b", decimals: 18, dex: Dusa }, b_token: Token { address: "AS1hCJXjndR4c9vekLWsXGnrdigp4AaZ7uYG3UKFzzKnWVsrNLPJ", name: "USD Coin", symbol: "USDC.e", decimals: 6, dex: Dusa }, a_reserve: 484.93292228602326, b_reserve: 31150.725674, dex: Dusa, swap_fee: 0.0, a_price: 1.0009030062984086, b_price: 0.999097808386301, active_bin_id: 8112293, bin_step: 1, bins: {8112292: Bin { id: 8112292, a_reserve: 0.0, b_reserve: 27.010864 }, 8112293: Bin { id: 8112293, a_reserve: 98.56628658568373, b_reserve: 25446.32383 }, 8112294: Bin { id: 8112294, a_reserve: 263.21335905959023, b_reserve: 0.0 }} }
2025-07-01T09:25:13.618856Z  INFO quedge::spawns::swap_fetcher: Swap event processed successfully
2025-07-01T09:25:13.619054Z DEBUG quedge::helpers: Detecting arbitrage cycles...
2025-07-01T09:25:13.630848Z DEBUG quedge::helpers: Arbitrage cycles found (4 total):
2025-07-01T09:25:13.631014Z DEBUG quedge::helpers: Cycle path Inisde Simulation Task:
2025-07-01T09:25:13.631112Z DEBUG quedge::helpers::display: Path: ["WMAS-Dusa", "WMAS-EagleFi", "POM-EagleFi", "POM-Dusa", "WMAS-Dusa"]
2025-07-01T09:25:13.631273Z DEBUG quedge::helpers: Cycle path Inisde Simulation Task:
2025-07-01T09:25:13.631367Z  INFO quedge::helpers: Default start amount for token AS12U4TZfNK7qoLyEERBBRDMu8nm5MKoRzPXDXans4v9wdATZedz9 is 100
2025-07-01T09:25:13.631435Z DEBUG quedge::helpers::display: Path: ["POM-Dusa", "WMAS-Dusa", "WMAS-EagleFi", "POM-EagleFi", "POM-Dusa"]
2025-07-01T09:25:13.631544Z  INFO quedge::helpers: Offchain Amount out after Offchain simulation: 0.000000015630701406604442
2025-07-01T09:25:13.631627Z DEBUG quedge::helpers: Cycle path Inisde Simulation Task:
2025-07-01T09:25:13.631701Z  INFO quedge::helpers: Default start amount for token AS1nqHKXpnFXqhDExTskXmBbbVpVpUbCQVtNSXLCqUDSUXihdWRq is 40207
2025-07-01T09:25:13.631777Z  INFO quedge::helpers: Offchain Earning percentage: -99.9999999843693
2025-07-01T09:25:13.631849Z DEBUG quedge::helpers::display: Path: ["WMAS-EagleFi", "POM-EagleFi", "POM-Dusa", "WMAS-Dusa", "WMAS-EagleFi"]
2025-07-01T09:25:13.631972Z  INFO quedge::helpers: Offchain Amount out after Offchain simulation: 0.00007765593260211698
2025-07-01T09:25:13.632047Z  WARN quedge::helpers: Not Valid Cycle Because Offchain Earning Percentage is less than margin -99.9999999843693 < 0.1
2025-07-01T09:25:13.632116Z DEBUG quedge::helpers: Cycle path Inisde Simulation Task:
2025-07-01T09:25:13.632183Z  INFO quedge::helpers: Default start amount for token AS12U4TZfNK7qoLyEERBBRDMu8nm5MKoRzPXDXans4v9wdATZedz9 is 100
2025-07-01T09:25:13.632254Z  INFO quedge::helpers: Offchain Earning percentage: -99.99999980685968
2025-07-01T09:25:13.632322Z DEBUG quedge::helpers::display: Path: ["POM-EagleFi", "POM-Dusa", "WMAS-Dusa", "WMAS-EagleFi", "POM-EagleFi"]
2025-07-01T09:25:13.632421Z  INFO quedge::helpers: Offchain Amount out after Offchain simulation: 0.000000015630701406604442
2025-07-01T09:25:13.632491Z  WARN quedge::helpers: Not Valid Cycle Because Offchain Earning Percentage is less than margin -99.99999980685968 < 0.1
2025-07-01T09:25:13.632560Z  INFO quedge::helpers: Default start amount for token AS1nqHKXpnFXqhDExTskXmBbbVpVpUbCQVtNSXLCqUDSUXihdWRq is 40207
2025-07-01T09:25:13.632623Z  INFO quedge::helpers: Offchain Earning percentage: -99.9999999843693
2025-07-01T09:25:13.632687Z  INFO quedge::helpers: Offchain Amount out after Offchain simulation: 0.00007765593260211698
2025-07-01T09:25:13.632752Z  WARN quedge::helpers: Not Valid Cycle Because Offchain Earning Percentage is less than margin -99.9999999843693 < 0.1
2025-07-01T09:25:13.632818Z  INFO quedge::helpers: Offchain Earning percentage: -99.99999980685968
2025-07-01T09:25:13.632912Z  WARN quedge::helpers: Not Valid Cycle Because Offchain Earning Percentage is less than margin -99.99999980685968 < 0.1
2025-07-01T09:25:13.633010Z  INFO quedge::helpers: Found 0 valid cycles
2025-07-01T09:25:13.633084Z  WARN quedge::helpers: No profitable arbitrage cycle found.
2025-07-01T09:25:23.351669Z  WARN quedge::spawns::swap_fetcher: Operation O19M1mocNj3sM9HdwskWNnXJHybGv8hziob7nURi66ryPi2AQg9 is not successful, skipping
2025-07-01T09:28:25.202002Z  WARN quedge::spawns::swap_fetcher: Operation O16r2CNYf3WdzXvvMmekWdtL9HmXDJvzirSTX8xWgdWCQ62JoUS is not successful, skipping
2025-07-01T09:30:01.212403Z  INFO quedge::spawns::swap_fetcher: Found 1 Swap events from the operation O1Xa4z7bQb2VGzgCf5PA7dJqhDntnuYJdkhR9gwEXh7ctNFFFuD. Processing them...
2025-07-01T09:30:01.212537Z DEBUG quedge::spawns::swap_fetcher: Processing last event of the operation O1Xa4z7bQb2VGzgCf5PA7dJqhDntnuYJdkhR9gwEXh7ctNFFFuD...
2025-07-01T09:30:01.212656Z  WARN quedge::spawns::swap_fetcher: Pool AS12UMSUxgpRBB6ArZDJ19arHoxNkkpdfofQGekAiAJqsuE6PEFJy not found in the pools hash map
2025-07-01T09:30:01.212759Z  INFO quedge::spawns::swap_fetcher: New detected swap on pool: AS1F2SAbhvknGLQKzfjaizKiZhEEd6mS1HLpZqPc2atzKE4VTNv9
2025-07-01T09:30:01.600412Z  INFO quedge::strategies::bellman_ford_matrix: Updating sparse matrix by pool: Pool { address: "AS1F2SAbhvknGLQKzfjaizKiZhEEd6mS1HLpZqPc2atzKE4VTNv9", a_token: Token { address: "AS12LKs9txoSSy8JgFJgV96m8k5z9pgzjYMYSshwN67mFVuj3bdUV", name: "Bsc USDT", symbol: "USDT.b", decimals: 18, dex: Dusa }, b_token: Token { address: "AS1hCJXjndR4c9vekLWsXGnrdigp4AaZ7uYG3UKFzzKnWVsrNLPJ", name: "USD Coin", symbol: "USDC.e", decimals: 6, dex: Dusa }, a_reserve: 449.6271785948389, b_reserve: 31186.063299, dex: Dusa, swap_fee: 0.0, a_price: 1.0009030062984086, b_price: 0.999097808386301, active_bin_id: 8112293, bin_step: 1, bins: {8112292: Bin { id: 8112292, a_reserve: 0.0, b_reserve: 27.010864 }, 8112293: Bin { id: 8112293, a_reserve: 63.26054289449934, b_reserve: 25481.661455 }, 8112294: Bin { id: 8112294, a_reserve: 263.21335905959023, b_reserve: 0.0 }} }
2025-07-01T09:30:01.600659Z  INFO quedge::spawns::swap_fetcher: Swap event processed successfully
2025-07-01T09:30:01.600885Z DEBUG quedge::helpers: Detecting arbitrage cycles...
2025-07-01T09:30:01.613237Z DEBUG quedge::helpers: Arbitrage cycles found (4 total):
2025-07-01T09:30:01.613409Z DEBUG quedge::helpers: Cycle path Inisde Simulation Task:
2025-07-01T09:30:01.613509Z DEBUG quedge::helpers::display: Path: ["WMAS-Dusa", "WMAS-EagleFi", "POM-EagleFi", "POM-Dusa", "WMAS-Dusa"]
2025-07-01T09:30:01.613665Z DEBUG quedge::helpers: Cycle path Inisde Simulation Task:
2025-07-01T09:30:01.613754Z DEBUG quedge::helpers::display: Path: ["POM-Dusa", "WMAS-Dusa", "WMAS-EagleFi", "POM-EagleFi", "POM-Dusa"]
2025-07-01T09:30:01.613935Z DEBUG quedge::helpers: Cycle path Inisde Simulation Task:
2025-07-01T09:30:01.614028Z  INFO quedge::helpers: Default start amount for token AS12U4TZfNK7qoLyEERBBRDMu8nm5MKoRzPXDXans4v9wdATZedz9 is 100
2025-07-01T09:30:01.614111Z DEBUG quedge::helpers::display: Path: ["WMAS-EagleFi", "POM-EagleFi", "POM-Dusa", "WMAS-Dusa", "WMAS-EagleFi"]
2025-07-01T09:30:01.614221Z  INFO quedge::helpers: Offchain Amount out after Offchain simulation: 0.000000015630701406604442
2025-07-01T09:30:01.614288Z  INFO quedge::helpers: Default start amount for token AS1nqHKXpnFXqhDExTskXmBbbVpVpUbCQVtNSXLCqUDSUXihdWRq is 40207
2025-07-01T09:30:01.614370Z DEBUG quedge::helpers: Cycle path Inisde Simulation Task:
2025-07-01T09:30:01.614447Z  INFO quedge::helpers: Offchain Earning percentage: -99.9999999843693
2025-07-01T09:30:01.614520Z  INFO quedge::helpers: Offchain Amount out after Offchain simulation: 0.00007765593260211698
2025-07-01T09:30:01.614597Z DEBUG quedge::helpers::display: Path: ["POM-EagleFi", "POM-Dusa", "WMAS-Dusa", "WMAS-EagleFi", "POM-EagleFi"]
2025-07-01T09:30:01.614682Z  WARN quedge::helpers: Not Valid Cycle Because Offchain Earning Percentage is less than margin -99.9999999843693 < 0.1
2025-07-01T09:30:01.614756Z  INFO quedge::helpers: Offchain Earning percentage: -99.99999980685968
2025-07-01T09:30:01.614829Z  INFO quedge::helpers: Default start amount for token AS1nqHKXpnFXqhDExTskXmBbbVpVpUbCQVtNSXLCqUDSUXihdWRq is 40207
2025-07-01T09:30:01.614904Z  WARN quedge::helpers: Not Valid Cycle Because Offchain Earning Percentage is less than margin -99.99999980685968 < 0.1
2025-07-01T09:30:01.615058Z  INFO quedge::helpers: Offchain Amount out after Offchain simulation: 0.00007765593260211698
2025-07-01T09:30:01.615138Z  INFO quedge::helpers: Offchain Earning percentage: -99.99999980685968
2025-07-01T09:30:01.615209Z  WARN quedge::helpers: Not Valid Cycle Because Offchain Earning Percentage is less than margin -99.99999980685968 < 0.1
2025-07-01T09:30:01.615327Z  INFO quedge::helpers: Default start amount for token AS12U4TZfNK7qoLyEERBBRDMu8nm5MKoRzPXDXans4v9wdATZedz9 is 100
2025-07-01T09:30:01.615401Z  INFO quedge::helpers: Offchain Amount out after Offchain simulation: 0.000000015630701406604442
2025-07-01T09:30:01.615469Z  INFO quedge::helpers: Offchain Earning percentage: -99.9999999843693
2025-07-01T09:30:01.615543Z  WARN quedge::helpers: Not Valid Cycle Because Offchain Earning Percentage is less than margin -99.9999999843693 < 0.1
2025-07-01T09:30:01.615665Z  INFO quedge::helpers: Found 0 valid cycles
2025-07-01T09:30:01.615751Z  WARN quedge::helpers: No profitable arbitrage cycle found.
2025-07-01T09:30:10.720871Z  WARN quedge::spawns::swap_fetcher: Operation O1qZG1ihCNstaXqEceqe6JP6Lh4parypxXYixWSmhQ4NirhusKo is not successful, skipping
2025-07-01T09:31:21.570133Z  INFO quedge::spawns::swap_fetcher: Found 1 Swap events from the operation O12CFMVY4PTBorZZuHd9GYoSvzTn7tDyUXPgo7Jzs8MZxW46CCXF. Processing them...
2025-07-01T09:31:21.570288Z DEBUG quedge::spawns::swap_fetcher: Processing last event of the operation O12CFMVY4PTBorZZuHd9GYoSvzTn7tDyUXPgo7Jzs8MZxW46CCXF...
2025-07-01T09:31:21.570423Z  WARN quedge::spawns::swap_fetcher: Pool AS12UMSUxgpRBB6ArZDJ19arHoxNkkpdfofQGekAiAJqsuE6PEFJy not found in the pools hash map
2025-07-01T09:31:21.570530Z  INFO quedge::spawns::swap_fetcher: New detected swap on pool: AS1F2SAbhvknGLQKzfjaizKiZhEEd6mS1HLpZqPc2atzKE4VTNv9
2025-07-01T09:31:22.003110Z  INFO quedge::strategies::bellman_ford_matrix: Updating sparse matrix by pool: Pool { address: "AS1F2SAbhvknGLQKzfjaizKiZhEEd6mS1HLpZqPc2atzKE4VTNv9", a_token: Token { address: "AS12LKs9txoSSy8JgFJgV96m8k5z9pgzjYMYSshwN67mFVuj3bdUV", name: "Bsc USDT", symbol: "USDT.b", decimals: 18, dex: Dusa }, b_token: Token { address: "AS1hCJXjndR4c9vekLWsXGnrdigp4AaZ7uYG3UKFzzKnWVsrNLPJ", name: "USD Coin", symbol: "USDC.e", decimals: 6, dex: Dusa }, a_reserve: 548.3726950390768, b_reserve: 31087.228615, dex: Dusa, swap_fee: 0.0, a_price: 1.0009030062984086, b_price: 0.999097808386301, active_bin_id: 8112293, bin_step: 1, bins: {8112292: Bin { id: 8112292, a_reserve: 0.0, b_reserve: 27.010864 }, 8112293: Bin { id: 8112293, a_reserve: 162.0060593387373, b_reserve: 25382.826771 }, 8112294: Bin { id: 8112294, a_reserve: 263.21335905959023, b_reserve: 0.0 }} }
2025-07-01T09:31:22.003315Z  INFO quedge::spawns::swap_fetcher: Swap event processed successfully
2025-07-01T09:31:22.003573Z DEBUG quedge::helpers: Detecting arbitrage cycles...
2025-07-01T09:31:22.015468Z DEBUG quedge::helpers: Arbitrage cycles found (4 total):
2025-07-01T09:31:22.015673Z DEBUG quedge::helpers: Cycle path Inisde Simulation Task:
2025-07-01T09:31:22.015773Z DEBUG quedge::helpers::display: Path: ["WMAS-Dusa", "WMAS-EagleFi", "POM-EagleFi", "POM-Dusa", "WMAS-Dusa"]
2025-07-01T09:31:22.015927Z DEBUG quedge::helpers: Cycle path Inisde Simulation Task:
2025-07-01T09:31:22.016009Z DEBUG quedge::helpers::display: Path: ["POM-Dusa", "WMAS-Dusa", "WMAS-EagleFi", "POM-EagleFi", "POM-Dusa"]
2025-07-01T09:31:22.016168Z DEBUG quedge::helpers: Cycle path Inisde Simulation Task:
2025-07-01T09:31:22.016274Z  INFO quedge::helpers: Default start amount for token AS12U4TZfNK7qoLyEERBBRDMu8nm5MKoRzPXDXans4v9wdATZedz9 is 100
2025-07-01T09:31:22.016369Z DEBUG quedge::helpers::display: Path: ["WMAS-EagleFi", "POM-EagleFi", "POM-Dusa", "WMAS-Dusa", "WMAS-EagleFi"]
2025-07-01T09:31:22.016464Z  INFO quedge::helpers: Offchain Amount out after Offchain simulation: 0.000000015630701406604442
2025-07-01T09:31:22.016559Z DEBUG quedge::helpers: Cycle path Inisde Simulation Task:
2025-07-01T09:31:22.016648Z  INFO quedge::helpers: Offchain Earning percentage: -99.9999999843693
2025-07-01T09:31:22.016726Z  INFO quedge::helpers: Default start amount for token AS1nqHKXpnFXqhDExTskXmBbbVpVpUbCQVtNSXLCqUDSUXihdWRq is 40207
2025-07-01T09:31:22.016803Z DEBUG quedge::helpers::display: Path: ["POM-EagleFi", "POM-Dusa", "WMAS-Dusa", "WMAS-EagleFi", "POM-EagleFi"]
2025-07-01T09:31:22.016891Z  WARN quedge::helpers: Not Valid Cycle Because Offchain Earning Percentage is less than margin -99.9999999843693 < 0.1
2025-07-01T09:31:22.016966Z  INFO quedge::helpers: Offchain Amount out after Offchain simulation: 0.00007765593260211698
2025-07-01T09:31:22.017046Z  INFO quedge::helpers: Default start amount for token AS1nqHKXpnFXqhDExTskXmBbbVpVpUbCQVtNSXLCqUDSUXihdWRq is 40207
2025-07-01T09:31:22.017133Z  INFO quedge::helpers: Default start amount for token AS12U4TZfNK7qoLyEERBBRDMu8nm5MKoRzPXDXans4v9wdATZedz9 is 100
2025-07-01T09:31:22.017274Z  INFO quedge::helpers: Offchain Earning percentage: -99.99999980685968
2025-07-01T09:31:22.017372Z  INFO quedge::helpers: Offchain Amount out after Offchain simulation: 0.00007765593260211698
2025-07-01T09:31:22.017457Z  INFO quedge::helpers: Offchain Amount out after Offchain simulation: 0.000000015630701406604442
2025-07-01T09:31:22.017543Z  WARN quedge::helpers: Not Valid Cycle Because Offchain Earning Percentage is less than margin -99.99999980685968 < 0.1
2025-07-01T09:31:22.017659Z  INFO quedge::helpers: Offchain Earning percentage: -99.99999980685968
2025-07-01T09:31:22.017728Z  INFO quedge::helpers: Offchain Earning percentage: -99.9999999843693
2025-07-01T09:31:22.017839Z  WARN quedge::helpers: Not Valid Cycle Because Offchain Earning Percentage is less than margin -99.99999980685968 < 0.1
2025-07-01T09:31:22.017945Z  WARN quedge::helpers: Not Valid Cycle Because Offchain Earning Percentage is less than margin -99.9999999843693 < 0.1
2025-07-01T09:31:22.018066Z  INFO quedge::helpers: Found 0 valid cycles
2025-07-01T09:31:22.018152Z  WARN quedge::helpers: No profitable arbitrage cycle found.
2025-07-01T09:31:30.859113Z  WARN quedge::spawns::swap_fetcher: Operation O12GtPcUsEYTEhyu6aaXs321jYFsxYXFrSSyijdwuUDMkcPtER4y is not successful, skipping
2025-07-01T09:34:48.717300Z  INFO quedge::spawns::swap_fetcher: Found 1 Swap events from the operation O19FbLsnYGQBBpRbmMS8xsrxvETnXetSkxioPTJ6m8fiNSaUpWi. Processing them...
2025-07-01T09:34:48.717449Z DEBUG quedge::spawns::swap_fetcher: Processing last event of the operation O19FbLsnYGQBBpRbmMS8xsrxvETnXetSkxioPTJ6m8fiNSaUpWi...
2025-07-01T09:34:48.717590Z  WARN quedge::spawns::swap_fetcher: Pool AS1KDohwRhneJemAfeKwwB1Q5y6Kx5knTiELu8j1U5S9i8FW9gJC not found in the pools hash map
2025-07-01T09:34:48.717730Z  WARN quedge::spawns::swap_fetcher: Pool AS12UMSUxgpRBB6ArZDJ19arHoxNkkpdfofQGekAiAJqsuE6PEFJy not found in the pools hash map
2025-07-01T09:34:48.717841Z  INFO quedge::spawns::swap_fetcher: New detected swap on pool: AS12AsqyNwL3WV8oaX6aiaPebZiZKncFg1xunPGRMo4GmY9vVz54J
2025-07-01T09:34:49.145071Z  INFO quedge::strategies::bellman_ford_matrix: Updating sparse matrix by pool: Pool { address: "AS12AsqyNwL3WV8oaX6aiaPebZiZKncFg1xunPGRMo4GmY9vVz54J", a_token: Token { address: "AS12fr54YtBY575Dfhtt7yftpT8KXgXb1ia5Pn1LofoLFLf9WcjGL", name: "WBTC", symbol: "WBTC.e", decimals: 8, dex: Dusa }, b_token: Token { address: "AS1hCJXjndR4c9vekLWsXGnrdigp4AaZ7uYG3UKFzzKnWVsrNLPJ", name: "USD Coin", symbol: "USDC.e", decimals: 6, dex: Dusa }, a_reserve: 0.14279766, b_reserve: 16022.665196, dex: Dusa, swap_fee: 0.0, a_price: 107198.60794844037, b_price: 9.328479344442355e-6, active_bin_id: 8393263, bin_step: 15, bins: {8393262: Bin { id: 8393262, a_reserve: 0.0, b_reserve: 130.899658 }, 8393263: Bin { id: 8393263, a_reserve: 0.00122073, b_reserve: 0.228885 }, 8393264: Bin { id: 8393264, a_reserve: 0.00122291, b_reserve: 0.0 }} }
2025-07-01T09:34:49.145312Z  INFO quedge::spawns::swap_fetcher: Swap event processed successfully
2025-07-01T09:34:49.145577Z DEBUG quedge::helpers: Detecting arbitrage cycles...
2025-07-01T09:34:49.158009Z DEBUG quedge::helpers: Arbitrage cycles found (4 total):
2025-07-01T09:34:49.158252Z DEBUG quedge::helpers: Cycle path Inisde Simulation Task:
2025-07-01T09:34:49.158382Z DEBUG quedge::helpers::display: Path: ["WMAS-Dusa", "WMAS-EagleFi", "POM-EagleFi", "POM-Dusa", "WMAS-Dusa"]
2025-07-01T09:34:49.158623Z DEBUG quedge::helpers: Cycle path Inisde Simulation Task:
2025-07-01T09:34:49.158725Z  INFO quedge::helpers: Default start amount for token AS12U4TZfNK7qoLyEERBBRDMu8nm5MKoRzPXDXans4v9wdATZedz9 is 100
2025-07-01T09:34:49.158829Z DEBUG quedge::helpers::display: Path: ["POM-Dusa", "WMAS-Dusa", "WMAS-EagleFi", "POM-EagleFi", "POM-Dusa"]
2025-07-01T09:34:49.158945Z  INFO quedge::helpers: Offchain Amount out after Offchain simulation: 0.000000015630701406604442
2025-07-01T09:34:49.159059Z DEBUG quedge::helpers: Cycle path Inisde Simulation Task:
2025-07-01T09:34:49.159195Z  INFO quedge::helpers: Default start amount for token AS1nqHKXpnFXqhDExTskXmBbbVpVpUbCQVtNSXLCqUDSUXihdWRq is 40207
2025-07-01T09:34:49.159325Z  INFO quedge::helpers: Offchain Earning percentage: -99.9999999843693
2025-07-01T09:34:49.159418Z DEBUG quedge::helpers::display: Path: ["WMAS-EagleFi", "POM-EagleFi", "POM-Dusa", "WMAS-Dusa", "WMAS-EagleFi"]
2025-07-01T09:34:49.159545Z  INFO quedge::helpers: Offchain Amount out after Offchain simulation: 0.00007765593260211698
2025-07-01T09:34:49.159652Z  WARN quedge::helpers: Not Valid Cycle Because Offchain Earning Percentage is less than margin -99.9999999843693 < 0.1
2025-07-01T09:34:49.159753Z DEBUG quedge::helpers: Cycle path Inisde Simulation Task:
2025-07-01T09:34:49.159844Z  INFO quedge::helpers: Default start amount for token AS12U4TZfNK7qoLyEERBBRDMu8nm5MKoRzPXDXans4v9wdATZedz9 is 100
2025-07-01T09:34:49.159946Z  INFO quedge::helpers: Offchain Earning percentage: -99.99999980685968
2025-07-01T09:34:49.160040Z DEBUG quedge::helpers::display: Path: ["POM-EagleFi", "POM-Dusa", "WMAS-Dusa", "WMAS-EagleFi", "POM-EagleFi"]
2025-07-01T09:34:49.160130Z  INFO quedge::helpers: Offchain Amount out after Offchain simulation: 0.000000015630701406604442
2025-07-01T09:34:49.160234Z  WARN quedge::helpers: Not Valid Cycle Because Offchain Earning Percentage is less than margin -99.99999980685968 < 0.1
2025-07-01T09:34:49.160323Z  INFO quedge::helpers: Default start amount for token AS1nqHKXpnFXqhDExTskXmBbbVpVpUbCQVtNSXLCqUDSUXihdWRq is 40207
2025-07-01T09:34:49.160411Z  INFO quedge::helpers: Offchain Earning percentage: -99.9999999843693
2025-07-01T09:34:49.160493Z  INFO quedge::helpers: Offchain Amount out after Offchain simulation: 0.00007765593260211698
2025-07-01T09:34:49.160590Z  WARN quedge::helpers: Not Valid Cycle Because Offchain Earning Percentage is less than margin -99.9999999843693 < 0.1
2025-07-01T09:34:49.160685Z  INFO quedge::helpers: Offchain Earning percentage: -99.99999980685968
2025-07-01T09:34:49.160787Z  WARN quedge::helpers: Not Valid Cycle Because Offchain Earning Percentage is less than margin -99.99999980685968 < 0.1
2025-07-01T09:34:49.160924Z  INFO quedge::helpers: Found 0 valid cycles
2025-07-01T09:34:49.161021Z  WARN quedge::helpers: No profitable arbitrage cycle found.
2025-07-01T09:34:58.726212Z  WARN quedge::spawns::swap_fetcher: Operation O12k3HLHj8QbzBo6qvLWDB5nAVMkWt2iyHE3dz6vusrxL2zSNeNA is not successful, skipping
2025-07-01T09:35:05.213783Z  WARN quedge::spawns::swap_fetcher: Operation O12wCCY8Z7FwPzULM7jWoZxWx54RnLExMXVJJ28xSsM3upcNPa1o is not successful, skipping
2025-07-01T09:36:57.220990Z  INFO quedge::spawns::swap_fetcher: Found 1 Swap events from the operation O1h8Us3k8dqRPbMhSYM5oM5EU925P9o4iGe3Q3FeUYPeiyeDDjo. Processing them...
2025-07-01T09:36:57.221143Z DEBUG quedge::spawns::swap_fetcher: Processing last event of the operation O1h8Us3k8dqRPbMhSYM5oM5EU925P9o4iGe3Q3FeUYPeiyeDDjo...
2025-07-01T09:36:57.221274Z  WARN quedge::spawns::swap_fetcher: Pool AS12UMSUxgpRBB6ArZDJ19arHoxNkkpdfofQGekAiAJqsuE6PEFJy not found in the pools hash map
2025-07-01T09:36:57.221399Z  INFO quedge::spawns::swap_fetcher: New detected swap on pool: AS1F2SAbhvknGLQKzfjaizKiZhEEd6mS1HLpZqPc2atzKE4VTNv9
2025-07-01T09:36:57.491980Z  INFO quedge::strategies::bellman_ford_matrix: Updating sparse matrix by pool: Pool { address: "AS1F2SAbhvknGLQKzfjaizKiZhEEd6mS1HLpZqPc2atzKE4VTNv9", a_token: Token { address: "AS12LKs9txoSSy8JgFJgV96m8k5z9pgzjYMYSshwN67mFVuj3bdUV", name: "Bsc USDT", symbol: "USDT.b", decimals: 18, dex: Dusa }, b_token: Token { address: "AS1hCJXjndR4c9vekLWsXGnrdigp4AaZ7uYG3UKFzzKnWVsrNLPJ", name: "USD Coin", symbol: "USDC.e", decimals: 6, dex: Dusa }, a_reserve: 578.4290609729023, b_reserve: 31057.145108, dex: Dusa, swap_fee: 0.0, a_price: 1.0009030062984086, b_price: 0.999097808386301, active_bin_id: 8112293, bin_step: 1, bins: {8112292: Bin { id: 8112292, a_reserve: 0.0, b_reserve: 27.010864 }, 8112293: Bin { id: 8112293, a_reserve: 192.0624252725628, b_reserve: 25352.743264 }, 8112294: Bin { id: 8112294, a_reserve: 263.21335905959023, b_reserve: 0.0 }} }
2025-07-01T09:36:57.492218Z  INFO quedge::spawns::swap_fetcher: Swap event processed successfully
2025-07-01T09:36:57.492451Z DEBUG quedge::helpers: Detecting arbitrage cycles...
2025-07-01T09:36:57.508256Z DEBUG quedge::helpers: Arbitrage cycles found (4 total):
2025-07-01T09:36:57.508505Z DEBUG quedge::helpers: Cycle path Inisde Simulation Task:
2025-07-01T09:36:57.508654Z DEBUG quedge::helpers::display: Path: ["WMAS-Dusa", "WMAS-EagleFi", "POM-EagleFi", "POM-Dusa", "WMAS-Dusa"]
2025-07-01T09:36:57.508880Z DEBUG quedge::helpers: Cycle path Inisde Simulation Task:
2025-07-01T09:36:57.509024Z  INFO quedge::helpers: Default start amount for token AS12U4TZfNK7qoLyEERBBRDMu8nm5MKoRzPXDXans4v9wdATZedz9 is 100
2025-07-01T09:36:57.509145Z DEBUG quedge::helpers::display: Path: ["POM-Dusa", "WMAS-Dusa", "WMAS-EagleFi", "POM-EagleFi", "POM-Dusa"]
2025-07-01T09:36:57.509245Z  INFO quedge::helpers: Offchain Amount out after Offchain simulation: 0.000000015630701406604442
2025-07-01T09:36:57.509371Z DEBUG quedge::helpers: Cycle path Inisde Simulation Task:
2025-07-01T09:36:57.509478Z  INFO quedge::helpers: Offchain Earning percentage: -99.9999999843693
2025-07-01T09:36:57.509578Z  INFO quedge::helpers: Default start amount for token AS1nqHKXpnFXqhDExTskXmBbbVpVpUbCQVtNSXLCqUDSUXihdWRq is 40207
2025-07-01T09:36:57.509679Z DEBUG quedge::helpers::display: Path: ["WMAS-EagleFi", "POM-EagleFi", "POM-Dusa", "WMAS-Dusa", "WMAS-EagleFi"]
2025-07-01T09:36:57.509791Z  WARN quedge::helpers: Not Valid Cycle Because Offchain Earning Percentage is less than margin -99.9999999843693 < 0.1
2025-07-01T09:36:57.509908Z  INFO quedge::helpers: Offchain Amount out after Offchain simulation: 0.00007765593260211698
2025-07-01T09:36:57.510034Z DEBUG quedge::helpers: Cycle path Inisde Simulation Task:
2025-07-01T09:36:57.510139Z  INFO quedge::helpers: Default start amount for token AS12U4TZfNK7qoLyEERBBRDMu8nm5MKoRzPXDXans4v9wdATZedz9 is 100
2025-07-01T09:36:57.510247Z  INFO quedge::helpers: Offchain Earning percentage: -99.99999980685968
2025-07-01T09:36:57.510356Z DEBUG quedge::helpers::display: Path: ["POM-EagleFi", "POM-Dusa", "WMAS-Dusa", "WMAS-EagleFi", "POM-EagleFi"]
2025-07-01T09:36:57.510458Z  INFO quedge::helpers: Offchain Amount out after Offchain simulation: 0.000000015630701406604442
2025-07-01T09:36:57.510566Z  WARN quedge::helpers: Not Valid Cycle Because Offchain Earning Percentage is less than margin -99.99999980685968 < 0.1
2025-07-01T09:36:57.510668Z  INFO quedge::helpers: Default start amount for token AS1nqHKXpnFXqhDExTskXmBbbVpVpUbCQVtNSXLCqUDSUXihdWRq is 40207
2025-07-01T09:36:57.510766Z  INFO quedge::helpers: Offchain Earning percentage: -99.9999999843693
2025-07-01T09:36:57.510865Z  INFO quedge::helpers: Offchain Amount out after Offchain simulation: 0.00007765593260211698
2025-07-01T09:36:57.510957Z  WARN quedge::helpers: Not Valid Cycle Because Offchain Earning Percentage is less than margin -99.9999999843693 < 0.1
2025-07-01T09:36:57.511048Z  INFO quedge::helpers: Offchain Earning percentage: -99.99999980685968
2025-07-01T09:36:57.511169Z  WARN quedge::helpers: Not Valid Cycle Because Offchain Earning Percentage is less than margin -99.99999980685968 < 0.1
2025-07-01T09:36:57.511333Z  INFO quedge::helpers: Found 0 valid cycles
2025-07-01T09:36:57.511459Z  WARN quedge::helpers: No profitable arbitrage cycle found.
2025-07-01T09:37:06.734408Z  WARN quedge::spawns::swap_fetcher: Operation O12frb2ppSQHzN6kZNfKzoqSKXrmRxQ2TYYXGUedP6q3eAXFWXSe is not successful, skipping
2025-07-01T09:39:05.233152Z  INFO quedge::spawns::swap_fetcher: Found 1 Swap events from the operation O1Fuhka9LFhjtf3Fjx8cWmTZhrRVSDiT9ZMAiWvVkbSK4jhTM5f. Processing them...
2025-07-01T09:39:05.233360Z DEBUG quedge::spawns::swap_fetcher: Processing last event of the operation O1Fuhka9LFhjtf3Fjx8cWmTZhrRVSDiT9ZMAiWvVkbSK4jhTM5f...
2025-07-01T09:39:05.233499Z  WARN quedge::spawns::swap_fetcher: Pool AS12UMSUxgpRBB6ArZDJ19arHoxNkkpdfofQGekAiAJqsuE6PEFJy not found in the pools hash map
2025-07-01T09:39:05.233666Z  INFO quedge::spawns::swap_fetcher: New detected swap on pool: AS1F2SAbhvknGLQKzfjaizKiZhEEd6mS1HLpZqPc2atzKE4VTNv9
2025-07-01T09:39:05.670836Z  INFO quedge::strategies::bellman_ford_matrix: Updating sparse matrix by pool: Pool { address: "AS1F2SAbhvknGLQKzfjaizKiZhEEd6mS1HLpZqPc2atzKE4VTNv9", a_token: Token { address: "AS12LKs9txoSSy8JgFJgV96m8k5z9pgzjYMYSshwN67mFVuj3bdUV", name: "Bsc USDT", symbol: "USDT.b", decimals: 18, dex: Dusa }, b_token: Token { address: "AS1hCJXjndR4c9vekLWsXGnrdigp4AaZ7uYG3UKFzzKnWVsrNLPJ", name: "USD Coin", symbol: "USDC.e", decimals: 6, dex: Dusa }, a_reserve: 552.669463051991, b_reserve: 31082.927967, dex: Dusa, swap_fee: 0.0, a_price: 1.0009030062984086, b_price: 0.999097808386301, active_bin_id: 8112293, bin_step: 1, bins: {8112292: Bin { id: 8112292, a_reserve: 0.0, b_reserve: 27.010864 }, 8112293: Bin { id: 8112293, a_reserve: 166.3028273516514, b_reserve: 25378.526123 }, 8112294: Bin { id: 8112294, a_reserve: 263.21335905959023, b_reserve: 0.0 }} }
2025-07-01T09:39:05.671106Z  INFO quedge::spawns::swap_fetcher: Swap event processed successfully
2025-07-01T09:39:05.671362Z DEBUG quedge::helpers: Detecting arbitrage cycles...
2025-07-01T09:39:05.684106Z DEBUG quedge::helpers: Arbitrage cycles found (4 total):
2025-07-01T09:39:05.684336Z DEBUG quedge::helpers: Cycle path Inisde Simulation Task:
2025-07-01T09:39:05.684511Z DEBUG quedge::helpers::display: Path: ["WMAS-Dusa", "WMAS-EagleFi", "POM-EagleFi", "POM-Dusa", "WMAS-Dusa"]
2025-07-01T09:39:05.684722Z DEBUG quedge::helpers: Cycle path Inisde Simulation Task:
2025-07-01T09:39:05.684822Z  INFO quedge::helpers: Default start amount for token AS12U4TZfNK7qoLyEERBBRDMu8nm5MKoRzPXDXans4v9wdATZedz9 is 100
2025-07-01T09:39:05.684918Z DEBUG quedge::helpers::display: Path: ["POM-Dusa", "WMAS-Dusa", "WMAS-EagleFi", "POM-EagleFi", "POM-Dusa"]
2025-07-01T09:39:05.685034Z  INFO quedge::helpers: Offchain Amount out after Offchain simulation: 0.000000015630701406604442
2025-07-01T09:39:05.685135Z DEBUG quedge::helpers: Cycle path Inisde Simulation Task:
2025-07-01T09:39:05.685222Z  INFO quedge::helpers: Offchain Earning percentage: -99.9999999843693
2025-07-01T09:39:05.685323Z  INFO quedge::helpers: Default start amount for token AS1nqHKXpnFXqhDExTskXmBbbVpVpUbCQVtNSXLCqUDSUXihdWRq is 40207
2025-07-01T09:39:05.685416Z DEBUG quedge::helpers::display: Path: ["WMAS-EagleFi", "POM-EagleFi", "POM-Dusa", "WMAS-Dusa", "WMAS-EagleFi"]
2025-07-01T09:39:05.685522Z  WARN quedge::helpers: Not Valid Cycle Because Offchain Earning Percentage is less than margin -99.9999999843693 < 0.1
2025-07-01T09:39:05.685617Z  INFO quedge::helpers: Offchain Amount out after Offchain simulation: 0.00007765593260211698
2025-07-01T09:39:05.685740Z DEBUG quedge::helpers: Cycle path Inisde Simulation Task:
2025-07-01T09:39:05.685868Z  INFO quedge::helpers: Default start amount for token AS12U4TZfNK7qoLyEERBBRDMu8nm5MKoRzPXDXans4v9wdATZedz9 is 100
2025-07-01T09:39:05.685999Z  INFO quedge::helpers: Offchain Earning percentage: -99.99999980685968
2025-07-01T09:39:05.686092Z DEBUG quedge::helpers::display: Path: ["POM-EagleFi", "POM-Dusa", "WMAS-Dusa", "WMAS-EagleFi", "POM-EagleFi"]
2025-07-01T09:39:05.686177Z  INFO quedge::helpers: Offchain Amount out after Offchain simulation: 0.000000015630701406604442
2025-07-01T09:39:05.686271Z  WARN quedge::helpers: Not Valid Cycle Because Offchain Earning Percentage is less than margin -99.99999980685968 < 0.1
2025-07-01T09:39:05.686368Z  INFO quedge::helpers: Offchain Earning percentage: -99.9999999843693
2025-07-01T09:39:05.686459Z  INFO quedge::helpers: Default start amount for token AS1nqHKXpnFXqhDExTskXmBbbVpVpUbCQVtNSXLCqUDSUXihdWRq is 40207
2025-07-01T09:39:05.686551Z  WARN quedge::helpers: Not Valid Cycle Because Offchain Earning Percentage is less than margin -99.9999999843693 < 0.1
2025-07-01T09:39:05.686643Z  INFO quedge::helpers: Offchain Amount out after Offchain simulation: 0.00007765593260211698
2025-07-01T09:39:05.686741Z  INFO quedge::helpers: Offchain Earning percentage: -99.99999980685968
2025-07-01T09:39:05.686844Z  WARN quedge::helpers: Not Valid Cycle Because Offchain Earning Percentage is less than margin -99.99999980685968 < 0.1
2025-07-01T09:39:05.686966Z  INFO quedge::helpers: Found 0 valid cycles
2025-07-01T09:39:05.687064Z  WARN quedge::helpers: No profitable arbitrage cycle found.
2025-07-01T09:39:14.732468Z  WARN quedge::spawns::swap_fetcher: Operation O12fDiQP4gju2KoyuYHqWEVsPUTxDYmnhLDWWAqHsnPrr9rUFxQj is not successful, skipping
2025-07-01T09:39:47.274456Z  INFO quedge::spawns::swap_fetcher: Found 1 Swap events from the operation O12fd7pGQZS8yHJLmjhm2XV91EB4QEjmXg2ehdc7xd5bvwubQq4q. Processing them...
2025-07-01T09:39:47.274620Z DEBUG quedge::spawns::swap_fetcher: Processing last event of the operation O12fd7pGQZS8yHJLmjhm2XV91EB4QEjmXg2ehdc7xd5bvwubQq4q...
2025-07-01T09:39:47.274764Z  WARN quedge::spawns::swap_fetcher: Pool AS12UMSUxgpRBB6ArZDJ19arHoxNkkpdfofQGekAiAJqsuE6PEFJy not found in the pools hash map
2025-07-01T09:39:47.274916Z  INFO quedge::spawns::swap_fetcher: New detected swap on pool: AS1Ba1T2mMpHvLEhTvsNkSDqm4djno2ezVojGrtLf1wRd8ThfZD3
2025-07-01T09:39:47.931592Z  INFO quedge::strategies::bellman_ford_matrix: Updating sparse matrix by pool: Pool { address: "AS1Ba1T2mMpHvLEhTvsNkSDqm4djno2ezVojGrtLf1wRd8ThfZD3", a_token: Token { address: "AS124vf3YfAJCSCQVYKczzuWWpXrximFpbTmX4rheLs5uNSftiiRY", name: "Wrapped Ether", symbol: "WETH.e", decimals: 18, dex: Dusa }, b_token: Token { address: "AS12U4TZfNK7qoLyEERBBRDMu8nm5MKoRzPXDXans4v9wdATZedz9", name: "Wrapped Massa", symbol: "WMAS", decimals: 9, dex: Dusa }, a_reserve: 2.3055179072322245, b_reserve: 540907.388864735, dex: Dusa, swap_fee: 0.0, a_price: 175913.6194988621, b_price: 5.684608177858955e-6, active_bin_id: 8382840, bin_step: 15, bins: {8382839: Bin { id: 8382839, a_reserve: 0.0, b_reserve: 2817.725887455 }, 8382840: Bin { id: 8382840, a_reserve: 0.00978563605629562, b_reserve: 455.854170675 }, 8382841: Bin { id: 8382841, a_reserve: 0.012447621082626861, b_reserve: 0.0 }} }
2025-07-01T09:39:47.931851Z  INFO quedge::spawns::swap_fetcher: Swap event processed successfully
2025-07-01T09:39:47.932165Z DEBUG quedge::helpers: Detecting arbitrage cycles...
2025-07-01T09:39:47.948791Z DEBUG quedge::helpers: Arbitrage cycles found (4 total):
2025-07-01T09:39:47.949005Z DEBUG quedge::helpers: Cycle path Inisde Simulation Task:
2025-07-01T09:39:47.949132Z DEBUG quedge::helpers::display: Path: ["WMAS-Dusa", "WMAS-EagleFi", "POM-EagleFi", "POM-Dusa", "WMAS-Dusa"]
2025-07-01T09:39:47.949341Z DEBUG quedge::helpers: Cycle path Inisde Simulation Task:
2025-07-01T09:39:47.949474Z DEBUG quedge::helpers::display: Path: ["POM-Dusa", "WMAS-Dusa", "WMAS-EagleFi", "POM-EagleFi", "POM-Dusa"]
2025-07-01T09:39:47.949679Z DEBUG quedge::helpers: Cycle path Inisde Simulation Task:
2025-07-01T09:39:47.949778Z  INFO quedge::helpers: Default start amount for token AS12U4TZfNK7qoLyEERBBRDMu8nm5MKoRzPXDXans4v9wdATZedz9 is 100
2025-07-01T09:39:47.949878Z DEBUG quedge::helpers::display: Path: ["WMAS-EagleFi", "POM-EagleFi", "POM-Dusa", "WMAS-Dusa", "WMAS-EagleFi"]
2025-07-01T09:39:47.949992Z  INFO quedge::helpers: Offchain Amount out after Offchain simulation: 0.000000015630701406604442
2025-07-01T09:39:47.950079Z DEBUG quedge::helpers: Cycle path Inisde Simulation Task:
2025-07-01T09:39:47.950164Z  INFO quedge::helpers: Offchain Earning percentage: -99.9999999843693
2025-07-01T09:39:47.950266Z  INFO quedge::helpers: Default start amount for token AS1nqHKXpnFXqhDExTskXmBbbVpVpUbCQVtNSXLCqUDSUXihdWRq is 40207
2025-07-01T09:39:47.950380Z DEBUG quedge::helpers::display: Path: ["POM-EagleFi", "POM-Dusa", "WMAS-Dusa", "WMAS-EagleFi", "POM-EagleFi"]
2025-07-01T09:39:47.950481Z  WARN quedge::helpers: Not Valid Cycle Because Offchain Earning Percentage is less than margin -99.9999999843693 < 0.1
2025-07-01T09:39:47.950588Z  INFO quedge::helpers: Offchain Amount out after Offchain simulation: 0.00007765593260211698
2025-07-01T09:39:47.950702Z  INFO quedge::helpers: Default start amount for token AS1nqHKXpnFXqhDExTskXmBbbVpVpUbCQVtNSXLCqUDSUXihdWRq is 40207
2025-07-01T09:39:47.950811Z  INFO quedge::helpers: Offchain Earning percentage: -99.99999980685968
2025-07-01T09:39:47.950915Z  INFO quedge::helpers: Offchain Amount out after Offchain simulation: 0.00007765593260211698
2025-07-01T09:39:47.951022Z  WARN quedge::helpers: Not Valid Cycle Because Offchain Earning Percentage is less than margin -99.99999980685968 < 0.1
2025-07-01T09:39:47.951123Z  INFO quedge::helpers: Offchain Earning percentage: -99.99999980685968
2025-07-01T09:39:47.951237Z  WARN quedge::helpers: Not Valid Cycle Because Offchain Earning Percentage is less than margin -99.99999980685968 < 0.1
2025-07-01T09:39:47.951416Z  INFO quedge::helpers: Default start amount for token AS12U4TZfNK7qoLyEERBBRDMu8nm5MKoRzPXDXans4v9wdATZedz9 is 100
2025-07-01T09:39:47.951542Z  INFO quedge::helpers: Offchain Amount out after Offchain simulation: 0.000000015630701406604442
2025-07-01T09:39:47.951675Z  INFO quedge::helpers: Offchain Earning percentage: -99.9999999843693
2025-07-01T09:39:47.951810Z  WARN quedge::helpers: Not Valid Cycle Because Offchain Earning Percentage is less than margin -99.9999999843693 < 0.1
2025-07-01T09:39:47.951972Z  INFO quedge::helpers: Found 0 valid cycles
2025-07-01T09:39:47.952088Z  WARN quedge::helpers: No profitable arbitrage cycle found.
2025-07-01T09:40:02.737775Z  WARN quedge::spawns::swap_fetcher: Operation O12UcKF6PajDvrbgEoeuxVbsuYddsdJH1tNM9mJU8SQjJVqNiDGv is not successful, skipping
2025-07-01T09:40:57.231712Z  INFO quedge::spawns::swap_fetcher: Found 1 Swap events from the operation O12hqJo11qrE1UBAAG5sfGkP33HWNK4XTU8BZZcs5LrSN9Ws43Ki. Processing them...
2025-07-01T09:40:57.231844Z DEBUG quedge::spawns::swap_fetcher: Processing last event of the operation O12hqJo11qrE1UBAAG5sfGkP33HWNK4XTU8BZZcs5LrSN9Ws43Ki...
2025-07-01T09:40:57.231987Z  WARN quedge::spawns::swap_fetcher: Pool AS12UMSUxgpRBB6ArZDJ19arHoxNkkpdfofQGekAiAJqsuE6PEFJy not found in the pools hash map
2025-07-01T09:40:57.232098Z  INFO quedge::spawns::swap_fetcher: New detected swap on pool: AS1F2SAbhvknGLQKzfjaizKiZhEEd6mS1HLpZqPc2atzKE4VTNv9
2025-07-01T09:40:57.606109Z  INFO quedge::strategies::bellman_ford_matrix: Updating sparse matrix by pool: Pool { address: "AS1F2SAbhvknGLQKzfjaizKiZhEEd6mS1HLpZqPc2atzKE4VTNv9", a_token: Token { address: "AS12LKs9txoSSy8JgFJgV96m8k5z9pgzjYMYSshwN67mFVuj3bdUV", name: "Bsc USDT", symbol: "USDT.b", decimals: 18, dex: Dusa }, b_token: Token { address: "AS1hCJXjndR4c9vekLWsXGnrdigp4AaZ7uYG3UKFzzKnWVsrNLPJ", name: "USD Coin", symbol: "USDC.e", decimals: 6, dex: Dusa }, a_reserve: 479.3205216078732, b_reserve: 31156.343143, dex: Dusa, swap_fee: 0.0, a_price: 1.0009030062984086, b_price: 0.999097808386301, active_bin_id: 8112293, bin_step: 1, bins: {8112292: Bin { id: 8112292, a_reserve: 0.0, b_reserve: 27.010864 }, 8112293: Bin { id: 8112293, a_reserve: 92.95388590753365, b_reserve: 25451.941299 }, 8112294: Bin { id: 8112294, a_reserve: 263.21335905959023, b_reserve: 0.0 }} }
2025-07-01T09:40:57.606335Z  INFO quedge::spawns::swap_fetcher: Swap event processed successfully
2025-07-01T09:40:57.606526Z DEBUG quedge::helpers: Detecting arbitrage cycles...
2025-07-01T09:40:57.618132Z DEBUG quedge::helpers: Arbitrage cycles found (4 total):
2025-07-01T09:40:57.618305Z DEBUG quedge::helpers: Cycle path Inisde Simulation Task:
2025-07-01T09:40:57.618407Z DEBUG quedge::helpers::display: Path: ["WMAS-Dusa", "WMAS-EagleFi", "POM-EagleFi", "POM-Dusa", "WMAS-Dusa"]
2025-07-01T09:40:57.618613Z DEBUG quedge::helpers: Cycle path Inisde Simulation Task:
2025-07-01T09:40:57.618722Z  INFO quedge::helpers: Default start amount for token AS12U4TZfNK7qoLyEERBBRDMu8nm5MKoRzPXDXans4v9wdATZedz9 is 100
2025-07-01T09:40:57.618824Z DEBUG quedge::helpers::display: Path: ["POM-Dusa", "WMAS-Dusa", "WMAS-EagleFi", "POM-EagleFi", "POM-Dusa"]
2025-07-01T09:40:57.618982Z  INFO quedge::helpers: Offchain Amount out after Offchain simulation: 0.000000015630701406604442
2025-07-01T09:40:57.619068Z DEBUG quedge::helpers: Cycle path Inisde Simulation Task:
2025-07-01T09:40:57.619157Z  INFO quedge::helpers: Default start amount for token AS1nqHKXpnFXqhDExTskXmBbbVpVpUbCQVtNSXLCqUDSUXihdWRq is 40207
2025-07-01T09:40:57.619226Z  INFO quedge::helpers: Offchain Earning percentage: -99.9999999843693
2025-07-01T09:40:57.619298Z DEBUG quedge::helpers::display: Path: ["WMAS-EagleFi", "POM-EagleFi", "POM-Dusa", "WMAS-Dusa", "WMAS-EagleFi"]
2025-07-01T09:40:57.619414Z  INFO quedge::helpers: Offchain Amount out after Offchain simulation: 0.00007765593260211698
2025-07-01T09:40:57.619490Z  WARN quedge::helpers: Not Valid Cycle Because Offchain Earning Percentage is less than margin -99.9999999843693 < 0.1
2025-07-01T09:40:57.619557Z DEBUG quedge::helpers: Cycle path Inisde Simulation Task:
2025-07-01T09:40:57.619631Z  INFO quedge::helpers: Default start amount for token AS12U4TZfNK7qoLyEERBBRDMu8nm5MKoRzPXDXans4v9wdATZedz9 is 100
2025-07-01T09:40:57.619701Z  INFO quedge::helpers: Offchain Earning percentage: -99.99999980685968
2025-07-01T09:40:57.619768Z DEBUG quedge::helpers::display: Path: ["POM-EagleFi", "POM-Dusa", "WMAS-Dusa", "WMAS-EagleFi", "POM-EagleFi"]
2025-07-01T09:40:57.619887Z  INFO quedge::helpers: Offchain Amount out after Offchain simulation: 0.000000015630701406604442
2025-07-01T09:40:57.619958Z  WARN quedge::helpers: Not Valid Cycle Because Offchain Earning Percentage is less than margin -99.99999980685968 < 0.1
2025-07-01T09:40:57.620036Z  INFO quedge::helpers: Default start amount for token AS1nqHKXpnFXqhDExTskXmBbbVpVpUbCQVtNSXLCqUDSUXihdWRq is 40207
2025-07-01T09:40:57.620099Z  INFO quedge::helpers: Offchain Earning percentage: -99.9999999843693
2025-07-01T09:40:57.620171Z  INFO quedge::helpers: Offchain Amount out after Offchain simulation: 0.00007765593260211698
2025-07-01T09:40:57.620238Z  WARN quedge::helpers: Not Valid Cycle Because Offchain Earning Percentage is less than margin -99.9999999843693 < 0.1
2025-07-01T09:40:57.620334Z  INFO quedge::helpers: Offchain Earning percentage: -99.99999980685968
2025-07-01T09:40:57.620444Z  WARN quedge::helpers: Not Valid Cycle Because Offchain Earning Percentage is less than margin -99.99999980685968 < 0.1
2025-07-01T09:40:57.620547Z  INFO quedge::helpers: Found 0 valid cycles
2025-07-01T09:40:57.620621Z  WARN quedge::helpers: No profitable arbitrage cycle found.
2025-07-01T09:41:06.731916Z  WARN quedge::spawns::swap_fetcher: Operation O12UWuhKjA3ZbD3hprPqBKSmRUULmCLk5kWJiDvkTMyh7TQNvf3v is not successful, skipping
2025-07-01T09:41:22.414197Z  INFO quedge::spawns::swap_fetcher: Found 2 Swap events from the operation O1ZaWDv4SVx916oLMhJKs9GU1rXYrqUM6HHjzfqUcqmJWRjSYKT. Processing them...
2025-07-01T09:41:22.414342Z  INFO quedge::spawns::swap_fetcher: New detected swap on pool: AS1Ba1T2mMpHvLEhTvsNkSDqm4djno2ezVojGrtLf1wRd8ThfZD3
2025-07-01T09:41:23.057511Z  INFO quedge::strategies::bellman_ford_matrix: Updating sparse matrix by pool: Pool { address: "AS1Ba1T2mMpHvLEhTvsNkSDqm4djno2ezVojGrtLf1wRd8ThfZD3", a_token: Token { address: "AS124vf3YfAJCSCQVYKczzuWWpXrximFpbTmX4rheLs5uNSftiiRY", name: "Wrapped Ether", symbol: "WETH.e", decimals: 18, dex: Dusa }, b_token: Token { address: "AS12U4TZfNK7qoLyEERBBRDMu8nm5MKoRzPXDXans4v9wdATZedz9", name: "Wrapped Massa", symbol: "WMAS", decimals: 9, dex: Dusa }, a_reserve: 2.3071607094169853, b_reserve: 541177.911098343, dex: Dusa, swap_fee: 0.0, a_price: 175650.14428243844, b_price: 5.6931350901257435e-6, active_bin_id: 8382839, bin_step: 15, bins: {8382838: Bin { id: 8382838, a_reserve: 0.0, b_reserve: 2830.866329531 }, 8382839: Bin { id: 8382839, a_reserve: 0.001872436391387441, b_reserve: 2506.231844255 }, 8382840: Bin { id: 8382840, a_reserve: 0.012475325474476844, b_reserve: 0.0 }} }
2025-07-01T09:41:23.057709Z  WARN quedge::spawns::swap_fetcher: Skipping arbitrage detection after updating the sparse matrix because of an order from the events stream
2025-07-01T09:41:23.057839Z  INFO quedge::spawns::swap_fetcher: Swap event processed successfully
2025-07-01T09:41:23.057959Z DEBUG quedge::spawns::swap_fetcher: Processing last event of the operation O1ZaWDv4SVx916oLMhJKs9GU1rXYrqUM6HHjzfqUcqmJWRjSYKT...
2025-07-01T09:41:23.058075Z  WARN quedge::spawns::swap_fetcher: Pool AS12UMSUxgpRBB6ArZDJ19arHoxNkkpdfofQGekAiAJqsuE6PEFJy not found in the pools hash map
2025-07-01T09:41:23.058229Z  INFO quedge::spawns::swap_fetcher: New detected swap on pool: AS1Ba1T2mMpHvLEhTvsNkSDqm4djno2ezVojGrtLf1wRd8ThfZD3
2025-07-01T09:41:23.485831Z  INFO quedge::strategies::bellman_ford_matrix: Updating sparse matrix by pool: Pool { address: "AS1Ba1T2mMpHvLEhTvsNkSDqm4djno2ezVojGrtLf1wRd8ThfZD3", a_token: Token { address: "AS124vf3YfAJCSCQVYKczzuWWpXrximFpbTmX4rheLs5uNSftiiRY", name: "Wrapped Ether", symbol: "WETH.e", decimals: 18, dex: Dusa }, b_token: Token { address: "AS12U4TZfNK7qoLyEERBBRDMu8nm5MKoRzPXDXans4v9wdATZedz9", name: "Wrapped Massa", symbol: "WMAS", decimals: 9, dex: Dusa }, a_reserve: 2.3071607094169853, b_reserve: 541177.911098343, dex: Dusa, swap_fee: 0.0, a_price: 175650.14428243844, b_price: 5.6931350901257435e-6, active_bin_id: 8382839, bin_step: 15, bins: {8382838: Bin { id: 8382838, a_reserve: 0.0, b_reserve: 2830.866329531 }, 8382839: Bin { id: 8382839, a_reserve: 0.001872436391387441, b_reserve: 2506.231844255 }, 8382840: Bin { id: 8382840, a_reserve: 0.012475325474476844, b_reserve: 0.0 }} }
2025-07-01T09:41:23.486024Z  INFO quedge::spawns::swap_fetcher: Swap event processed successfully
2025-07-01T09:41:23.486305Z DEBUG quedge::helpers: Detecting arbitrage cycles...
2025-07-01T09:41:23.497840Z DEBUG quedge::helpers: Arbitrage cycles found (4 total):
2025-07-01T09:41:23.497985Z DEBUG quedge::helpers: Cycle path Inisde Simulation Task:
2025-07-01T09:41:23.498084Z DEBUG quedge::helpers::display: Path: ["WMAS-Dusa", "WMAS-EagleFi", "POM-EagleFi", "POM-Dusa", "WMAS-Dusa"]
2025-07-01T09:41:23.498276Z DEBUG quedge::helpers: Cycle path Inisde Simulation Task:
2025-07-01T09:41:23.498393Z  INFO quedge::helpers: Default start amount for token AS12U4TZfNK7qoLyEERBBRDMu8nm5MKoRzPXDXans4v9wdATZedz9 is 100
2025-07-01T09:41:23.498507Z DEBUG quedge::helpers::display: Path: ["POM-Dusa", "WMAS-Dusa", "WMAS-EagleFi", "POM-EagleFi", "POM-Dusa"]
2025-07-01T09:41:23.498628Z  INFO quedge::helpers: Offchain Amount out after Offchain simulation: 0.000000015630701406604442
2025-07-01T09:41:23.498707Z DEBUG quedge::helpers: Cycle path Inisde Simulation Task:
2025-07-01T09:41:23.498781Z  INFO quedge::helpers: Default start amount for token AS1nqHKXpnFXqhDExTskXmBbbVpVpUbCQVtNSXLCqUDSUXihdWRq is 40207
2025-07-01T09:41:23.498850Z  INFO quedge::helpers: Offchain Earning percentage: -99.9999999843693
2025-07-01T09:41:23.498920Z DEBUG quedge::helpers::display: Path: ["WMAS-EagleFi", "POM-EagleFi", "POM-Dusa", "WMAS-Dusa", "WMAS-EagleFi"]
2025-07-01T09:41:23.499045Z  INFO quedge::helpers: Offchain Amount out after Offchain simulation: 0.00007765593260211698
2025-07-01T09:41:23.499117Z  WARN quedge::helpers: Not Valid Cycle Because Offchain Earning Percentage is less than margin -99.9999999843693 < 0.1
2025-07-01T09:41:23.499222Z DEBUG quedge::helpers: Cycle path Inisde Simulation Task:
2025-07-01T09:41:23.499297Z  INFO quedge::helpers: Default start amount for token AS12U4TZfNK7qoLyEERBBRDMu8nm5MKoRzPXDXans4v9wdATZedz9 is 100
2025-07-01T09:41:23.499370Z  INFO quedge::helpers: Offchain Earning percentage: -99.99999980685968
2025-07-01T09:41:23.499439Z DEBUG quedge::helpers::display: Path: ["POM-EagleFi", "POM-Dusa", "WMAS-Dusa", "WMAS-EagleFi", "POM-EagleFi"]
2025-07-01T09:41:23.499553Z  INFO quedge::helpers: Offchain Amount out after Offchain simulation: 0.000000015630701406604442
2025-07-01T09:41:23.499632Z  WARN quedge::helpers: Not Valid Cycle Because Offchain Earning Percentage is less than margin -99.99999980685968 < 0.1
2025-07-01T09:41:23.499705Z  INFO quedge::helpers: Default start amount for token AS1nqHKXpnFXqhDExTskXmBbbVpVpUbCQVtNSXLCqUDSUXihdWRq is 40207
2025-07-01T09:41:23.499779Z  INFO quedge::helpers: Offchain Earning percentage: -99.9999999843693
2025-07-01T09:41:23.499846Z  INFO quedge::helpers: Offchain Amount out after Offchain simulation: 0.00007765593260211698
2025-07-01T09:41:23.499916Z  WARN quedge::helpers: Not Valid Cycle Because Offchain Earning Percentage is less than margin -99.9999999843693 < 0.1
2025-07-01T09:41:23.499985Z  INFO quedge::helpers: Offchain Earning percentage: -99.99999980685968
2025-07-01T09:41:23.500061Z  WARN quedge::helpers: Not Valid Cycle Because Offchain Earning Percentage is less than margin -99.99999980685968 < 0.1
2025-07-01T09:41:23.500177Z  INFO quedge::helpers: Found 0 valid cycles
2025-07-01T09:41:23.500295Z  WARN quedge::helpers: No profitable arbitrage cycle found.
2025-07-01T09:41:27.259767Z  INFO quedge::spawns::swap_fetcher: Found 2 Swap events from the operation O1bamRjPCae3v5UdAh8gGspcKmj5Nnm5N5mk8mNY8jQ4WbUUdLE. Processing them...
2025-07-01T09:41:27.259891Z  INFO quedge::spawns::swap_fetcher: New detected swap on pool: AS1Ba1T2mMpHvLEhTvsNkSDqm4djno2ezVojGrtLf1wRd8ThfZD3
2025-07-01T09:41:27.604319Z  INFO quedge::strategies::bellman_ford_matrix: Updating sparse matrix by pool: Pool { address: "AS1Ba1T2mMpHvLEhTvsNkSDqm4djno2ezVojGrtLf1wRd8ThfZD3", a_token: Token { address: "AS124vf3YfAJCSCQVYKczzuWWpXrximFpbTmX4rheLs5uNSftiiRY", name: "Wrapped Ether", symbol: "WETH.e", decimals: 18, dex: Dusa }, b_token: Token { address: "AS12U4TZfNK7qoLyEERBBRDMu8nm5MKoRzPXDXans4v9wdATZedz9", name: "Wrapped Massa", symbol: "WMAS", decimals: 9, dex: Dusa }, a_reserve: 2.306592249878236, b_reserve: 541277.761098343, dex: Dusa, swap_fee: 0.0, a_price: 175650.14428243844, b_price: 5.6931350901257435e-6, active_bin_id: 8382839, bin_step: 15, bins: {8382838: Bin { id: 8382838, a_reserve: 0.0, b_reserve: 2830.866329531 }, 8382839: Bin { id: 8382839, a_reserve: 0.001303976852638572, b_reserve: 2606.081844255 }, 8382840: Bin { id: 8382840, a_reserve: 0.012475325474476844, b_reserve: 0.0 }} }
2025-07-01T09:41:27.604527Z  WARN quedge::spawns::swap_fetcher: Skipping arbitrage detection after updating the sparse matrix because of an order from the events stream
2025-07-01T09:41:27.604688Z  INFO quedge::spawns::swap_fetcher: Swap event processed successfully
2025-07-01T09:41:27.604795Z DEBUG quedge::spawns::swap_fetcher: Processing last event of the operation O1bamRjPCae3v5UdAh8gGspcKmj5Nnm5N5mk8mNY8jQ4WbUUdLE...
2025-07-01T09:41:27.604899Z  WARN quedge::spawns::swap_fetcher: Pool AS12RnQgozREcusBbmSTiyVbk1kwcqiaLV8bcFc4SSTsCG6CjyTHk not found in the pools hash map
2025-07-01T09:41:27.605015Z  WARN quedge::spawns::swap_fetcher: Pool AS1xoKcDq46fwKccuoit46KPzbXyKokPnxi6GWq8y4Xxk9ACsJ2g not found in the pools hash map
2025-07-01T09:41:27.605158Z  INFO quedge::spawns::swap_fetcher: New detected swap on pool: AS126PmvRK8NuSmVW3LRTgUuUw4csPW76bS9DZzn8bsGJCkhqySiz
2025-07-01T09:41:27.899227Z  INFO quedge::strategies::bellman_ford_matrix: Updating sparse matrix by pool: Pool { address: "AS126PmvRK8NuSmVW3LRTgUuUw4csPW76bS9DZzn8bsGJCkhqySiz", a_token: Token { address: "AS124vf3YfAJCSCQVYKczzuWWpXrximFpbTmX4rheLs5uNSftiiRY", name: "Wrapped Ether", symbol: "WETH.e", decimals: 18, dex: EagleFi }, b_token: Token { address: "AS12U4TZfNK7qoLyEERBBRDMu8nm5MKoRzPXDXans4v9wdATZedz9", name: "Wrapped Massa", symbol: "WMAS", decimals: 9, dex: EagleFi }, a_reserve: 2.1275555389844047, b_reserve: 376174.353839239, dex: EagleFi, swap_fee: 0.3, a_price: 176810.59175489584, b_price: 5.655769770773985e-6, active_bin_id: 0, bin_step: 0, bins: {} }
2025-07-01T09:41:27.899437Z  INFO quedge::spawns::swap_fetcher: Swap event processed successfully
2025-07-01T09:41:27.899720Z DEBUG quedge::helpers: Detecting arbitrage cycles...
2025-07-01T09:41:27.911910Z DEBUG quedge::helpers: Arbitrage cycles found (4 total):
2025-07-01T09:41:27.912078Z DEBUG quedge::helpers: Cycle path Inisde Simulation Task:
2025-07-01T09:41:27.912214Z DEBUG quedge::helpers::display: Path: ["WMAS-Dusa", "WMAS-EagleFi", "POM-EagleFi", "POM-Dusa", "WMAS-Dusa"]
2025-07-01T09:41:27.912395Z DEBUG quedge::helpers: Cycle path Inisde Simulation Task:
2025-07-01T09:41:27.912495Z  INFO quedge::helpers: Default start amount for token AS12U4TZfNK7qoLyEERBBRDMu8nm5MKoRzPXDXans4v9wdATZedz9 is 100
2025-07-01T09:41:27.912596Z DEBUG quedge::helpers::display: Path: ["POM-Dusa", "WMAS-Dusa", "WMAS-EagleFi", "POM-EagleFi", "POM-Dusa"]
2025-07-01T09:41:27.912714Z  INFO quedge::helpers: Offchain Amount out after Offchain simulation: 0.000000015630701406604442
2025-07-01T09:41:27.912810Z DEBUG quedge::helpers: Cycle path Inisde Simulation Task:
2025-07-01T09:41:27.912893Z  INFO quedge::helpers: Default start amount for token AS1nqHKXpnFXqhDExTskXmBbbVpVpUbCQVtNSXLCqUDSUXihdWRq is 40207
2025-07-01T09:41:27.912974Z  INFO quedge::helpers: Offchain Earning percentage: -99.9999999843693
2025-07-01T09:41:27.913038Z DEBUG quedge::helpers::display: Path: ["WMAS-EagleFi", "POM-EagleFi", "POM-Dusa", "WMAS-Dusa", "WMAS-EagleFi"]
2025-07-01T09:41:27.913120Z  INFO quedge::helpers: Offchain Amount out after Offchain simulation: 0.00007765593260211698
2025-07-01T09:41:27.913205Z  WARN quedge::helpers: Not Valid Cycle Because Offchain Earning Percentage is less than margin -99.9999999843693 < 0.1
2025-07-01T09:41:27.913270Z DEBUG quedge::helpers: Cycle path Inisde Simulation Task:
2025-07-01T09:41:27.913351Z  INFO quedge::helpers: Offchain Earning percentage: -99.99999980685968
2025-07-01T09:41:27.913465Z  INFO quedge::helpers: Default start amount for token AS12U4TZfNK7qoLyEERBBRDMu8nm5MKoRzPXDXans4v9wdATZedz9 is 100
2025-07-01T09:41:27.913544Z DEBUG quedge::helpers::display: Path: ["POM-EagleFi", "POM-Dusa", "WMAS-Dusa", "WMAS-EagleFi", "POM-EagleFi"]
2025-07-01T09:41:27.913613Z  WARN quedge::helpers: Not Valid Cycle Because Offchain Earning Percentage is less than margin -99.99999980685968 < 0.1
2025-07-01T09:41:27.913699Z  INFO quedge::helpers: Offchain Amount out after Offchain simulation: 0.000000015630701406604442
2025-07-01T09:41:27.913772Z  INFO quedge::helpers: Default start amount for token AS1nqHKXpnFXqhDExTskXmBbbVpVpUbCQVtNSXLCqUDSUXihdWRq is 40207
2025-07-01T09:41:27.913846Z  INFO quedge::helpers: Offchain Earning percentage: -99.9999999843693
2025-07-01T09:41:27.913917Z  INFO quedge::helpers: Offchain Amount out after Offchain simulation: 0.00007765593260211698
2025-07-01T09:41:27.914029Z  WARN quedge::helpers: Not Valid Cycle Because Offchain Earning Percentage is less than margin -99.9999999843693 < 0.1
2025-07-01T09:41:27.914095Z  INFO quedge::helpers: Offchain Earning percentage: -99.99999980685968
2025-07-01T09:41:27.914174Z  WARN quedge::helpers: Not Valid Cycle Because Offchain Earning Percentage is less than margin -99.99999980685968 < 0.1
2025-07-01T09:41:27.914293Z  INFO quedge::helpers: Found 0 valid cycles
2025-07-01T09:41:27.914378Z  WARN quedge::helpers: No profitable arbitrage cycle found.
2025-07-01T09:41:36.262010Z  INFO quedge::spawns::swap_fetcher: Found 2 Swap events from the operation O1jK8L4ZDChhuxRCby8UwWgm3Ms9gaNm9q7m285YBb6Ue8rgzkj. Processing them...
2025-07-01T09:41:36.262224Z  INFO quedge::spawns::swap_fetcher: New detected swap on pool: AS1Ba1T2mMpHvLEhTvsNkSDqm4djno2ezVojGrtLf1wRd8ThfZD3
2025-07-01T09:41:36.527246Z  INFO quedge::strategies::bellman_ford_matrix: Updating sparse matrix by pool: Pool { address: "AS1Ba1T2mMpHvLEhTvsNkSDqm4djno2ezVojGrtLf1wRd8ThfZD3", a_token: Token { address: "AS124vf3YfAJCSCQVYKczzuWWpXrximFpbTmX4rheLs5uNSftiiRY", name: "Wrapped Ether", symbol: "WETH.e", decimals: 18, dex: Dusa }, b_token: Token { address: "AS12U4TZfNK7qoLyEERBBRDMu8nm5MKoRzPXDXans4v9wdATZedz9", name: "Wrapped Massa", symbol: "WMAS", decimals: 9, dex: Dusa }, a_reserve: 2.3060237903394873, b_reserve: 541377.611098343, dex: Dusa, swap_fee: 0.0, a_price: 175650.14428243844, b_price: 5.6931350901257435e-6, active_bin_id: 8382839, bin_step: 15, bins: {8382838: Bin { id: 8382838, a_reserve: 0.0, b_reserve: 2830.866329531 }, 8382839: Bin { id: 8382839, a_reserve: 0.000735517313889703, b_reserve: 2705.931844255 }, 8382840: Bin { id: 8382840, a_reserve: 0.012475325474476844, b_reserve: 0.0 }} }
2025-07-01T09:41:36.527421Z  WARN quedge::spawns::swap_fetcher: Skipping arbitrage detection after updating the sparse matrix because of an order from the events stream
2025-07-01T09:41:36.527518Z  INFO quedge::spawns::swap_fetcher: Swap event processed successfully
2025-07-01T09:41:36.527615Z DEBUG quedge::spawns::swap_fetcher: Processing last event of the operation O1jK8L4ZDChhuxRCby8UwWgm3Ms9gaNm9q7m285YBb6Ue8rgzkj...
2025-07-01T09:41:36.527703Z  WARN quedge::spawns::swap_fetcher: Pool AS12RnQgozREcusBbmSTiyVbk1kwcqiaLV8bcFc4SSTsCG6CjyTHk not found in the pools hash map
2025-07-01T09:41:36.527778Z  WARN quedge::spawns::swap_fetcher: Pool AS1xoKcDq46fwKccuoit46KPzbXyKokPnxi6GWq8y4Xxk9ACsJ2g not found in the pools hash map
2025-07-01T09:41:36.527869Z  INFO quedge::spawns::swap_fetcher: New detected swap on pool: AS126PmvRK8NuSmVW3LRTgUuUw4csPW76bS9DZzn8bsGJCkhqySiz
2025-07-01T09:41:36.829252Z  INFO quedge::strategies::bellman_ford_matrix: Updating sparse matrix by pool: Pool { address: "AS126PmvRK8NuSmVW3LRTgUuUw4csPW76bS9DZzn8bsGJCkhqySiz", a_token: Token { address: "AS124vf3YfAJCSCQVYKczzuWWpXrximFpbTmX4rheLs5uNSftiiRY", name: "Wrapped Ether", symbol: "WETH.e", decimals: 18, dex: EagleFi }, b_token: Token { address: "AS12U4TZfNK7qoLyEERBBRDMu8nm5MKoRzPXDXans4v9wdATZedz9", name: "Wrapped Massa", symbol: "WMAS", decimals: 9, dex: EagleFi }, a_reserve: 2.1281235721784992, b_reserve: 376074.172387891, dex: EagleFi, swap_fee: 0.3, a_price: 176716.32291677245, b_price: 5.658786825657112e-6, active_bin_id: 0, bin_step: 0, bins: {} }
2025-07-01T09:41:36.829478Z  INFO quedge::spawns::swap_fetcher: Swap event processed successfully
2025-07-01T09:41:36.829796Z DEBUG quedge::helpers: Detecting arbitrage cycles...
2025-07-01T09:41:36.842006Z DEBUG quedge::helpers: Arbitrage cycles found (4 total):
2025-07-01T09:41:36.842163Z DEBUG quedge::helpers: Cycle path Inisde Simulation Task:
2025-07-01T09:41:36.842262Z DEBUG quedge::helpers::display: Path: ["WMAS-Dusa", "WMAS-EagleFi", "POM-EagleFi", "POM-Dusa", "WMAS-Dusa"]
2025-07-01T09:41:36.842485Z DEBUG quedge::helpers: Cycle path Inisde Simulation Task:
2025-07-01T09:41:36.842560Z  INFO quedge::helpers: Default start amount for token AS12U4TZfNK7qoLyEERBBRDMu8nm5MKoRzPXDXans4v9wdATZedz9 is 100
2025-07-01T09:41:36.842630Z DEBUG quedge::helpers::display: Path: ["POM-Dusa", "WMAS-Dusa", "WMAS-EagleFi", "POM-EagleFi", "POM-Dusa"]
2025-07-01T09:41:36.842723Z  INFO quedge::helpers: Offchain Amount out after Offchain simulation: 0.000000015630701406604442
2025-07-01T09:41:36.842805Z DEBUG quedge::helpers: Cycle path Inisde Simulation Task:
2025-07-01T09:41:36.842884Z  INFO quedge::helpers: Offchain Earning percentage: -99.9999999843693
2025-07-01T09:41:36.842969Z  INFO quedge::helpers: Default start amount for token AS1nqHKXpnFXqhDExTskXmBbbVpVpUbCQVtNSXLCqUDSUXihdWRq is 40207
2025-07-01T09:41:36.843045Z DEBUG quedge::helpers::display: Path: ["WMAS-EagleFi", "POM-EagleFi", "POM-Dusa", "WMAS-Dusa", "WMAS-EagleFi"]
2025-07-01T09:41:36.843164Z  WARN quedge::helpers: Not Valid Cycle Because Offchain Earning Percentage is less than margin -99.9999999843693 < 0.1
2025-07-01T09:41:36.843268Z  INFO quedge::helpers: Offchain Amount out after Offchain simulation: 0.00007765593260211698
2025-07-01T09:41:36.843338Z DEBUG quedge::helpers: Cycle path Inisde Simulation Task:
2025-07-01T09:41:36.843408Z  INFO quedge::helpers: Default start amount for token AS12U4TZfNK7qoLyEERBBRDMu8nm5MKoRzPXDXans4v9wdATZedz9 is 100
2025-07-01T09:41:36.843479Z  INFO quedge::helpers: Offchain Earning percentage: -99.99999980685968
2025-07-01T09:41:36.843548Z DEBUG quedge::helpers::display: Path: ["POM-EagleFi", "POM-Dusa", "WMAS-Dusa", "WMAS-EagleFi", "POM-EagleFi"]
2025-07-01T09:41:36.843689Z  INFO quedge::helpers: Offchain Amount out after Offchain simulation: 0.000000015630701406604442
2025-07-01T09:41:36.843788Z  WARN quedge::helpers: Not Valid Cycle Because Offchain Earning Percentage is less than margin -99.99999980685968 < 0.1
2025-07-01T09:41:36.843867Z  INFO quedge::helpers: Default start amount for token AS1nqHKXpnFXqhDExTskXmBbbVpVpUbCQVtNSXLCqUDSUXihdWRq is 40207
2025-07-01T09:41:36.843936Z  INFO quedge::helpers: Offchain Earning percentage: -99.9999999843693
2025-07-01T09:41:36.844007Z  INFO quedge::helpers: Offchain Amount out after Offchain simulation: 0.00007765593260211698
2025-07-01T09:41:36.844082Z  WARN quedge::helpers: Not Valid Cycle Because Offchain Earning Percentage is less than margin -99.9999999843693 < 0.1
2025-07-01T09:41:36.844168Z  INFO quedge::helpers: Offchain Earning percentage: -99.99999980685968
2025-07-01T09:41:36.844250Z  WARN quedge::helpers: Not Valid Cycle Because Offchain Earning Percentage is less than margin -99.99999980685968 < 0.1
2025-07-01T09:41:36.844362Z  INFO quedge::helpers: Found 0 valid cycles
2025-07-01T09:41:36.844472Z  WARN quedge::helpers: No profitable arbitrage cycle found.
2025-07-01T09:41:38.746581Z  WARN quedge::spawns::swap_fetcher: Operation O1VFUbcxTrfLxwyNP1UU96f5eESEkf8kvbaqjXFc8RfEDAMGeMQ is not successful, skipping
2025-07-01T09:41:44.758483Z  INFO quedge::spawns::swap_fetcher: Found 2 Swap events from the operation O12GpXQ5cpPyWSgdxd3jmZx1eY46e8LGs3gLFQTnCLHaPAybcP78. Processing them...
2025-07-01T09:41:44.758631Z  INFO quedge::spawns::swap_fetcher: New detected swap on pool: AS1Ba1T2mMpHvLEhTvsNkSDqm4djno2ezVojGrtLf1wRd8ThfZD3
2025-07-01T09:41:45.009396Z  INFO quedge::strategies::bellman_ford_matrix: Updating sparse matrix by pool: Pool { address: "AS1Ba1T2mMpHvLEhTvsNkSDqm4djno2ezVojGrtLf1wRd8ThfZD3", a_token: Token { address: "AS124vf3YfAJCSCQVYKczzuWWpXrximFpbTmX4rheLs5uNSftiiRY", name: "Wrapped Ether", symbol: "WETH.e", decimals: 18, dex: Dusa }, b_token: Token { address: "AS12U4TZfNK7qoLyEERBBRDMu8nm5MKoRzPXDXans4v9wdATZedz9", name: "Wrapped Massa", symbol: "WMAS", decimals: 9, dex: Dusa }, a_reserve: 2.3414769784838, b_reserve: 547431.95902363, dex: Dusa, swap_fee: 0.0, a_price: 175650.14428243844, b_price: 5.6931350901257435e-6, active_bin_id: 8382839, bin_step: 15, bins: {8382838: Bin { id: 8382838, a_reserve: 0.0, b_reserve: 3227.065968002 }, 8382839: Bin { id: 8382839, a_reserve: 0.002354434490986257, b_reserve: 2817.285192476 }, 8382840: Bin { id: 8382840, a_reserve: 0.014730943538957918, b_reserve: 0.0 }} }
2025-07-01T09:41:45.009575Z  WARN quedge::spawns::swap_fetcher: Skipping arbitrage detection after updating the sparse matrix because of an order from the events stream
2025-07-01T09:41:45.009670Z  INFO quedge::spawns::swap_fetcher: Swap event processed successfully
2025-07-01T09:41:45.009758Z DEBUG quedge::spawns::swap_fetcher: Processing last event of the operation O12GpXQ5cpPyWSgdxd3jmZx1eY46e8LGs3gLFQTnCLHaPAybcP78...
2025-07-01T09:41:45.009843Z  WARN quedge::spawns::swap_fetcher: Pool AS12RnQgozREcusBbmSTiyVbk1kwcqiaLV8bcFc4SSTsCG6CjyTHk not found in the pools hash map
2025-07-01T09:41:45.009935Z  WARN quedge::spawns::swap_fetcher: Pool AS1xoKcDq46fwKccuoit46KPzbXyKokPnxi6GWq8y4Xxk9ACsJ2g not found in the pools hash map
2025-07-01T09:41:45.010044Z  INFO quedge::spawns::swap_fetcher: New detected swap on pool: AS126PmvRK8NuSmVW3LRTgUuUw4csPW76bS9DZzn8bsGJCkhqySiz
2025-07-01T09:41:45.429664Z  INFO quedge::strategies::bellman_ford_matrix: Updating sparse matrix by pool: Pool { address: "AS126PmvRK8NuSmVW3LRTgUuUw4csPW76bS9DZzn8bsGJCkhqySiz", a_token: Token { address: "AS124vf3YfAJCSCQVYKczzuWWpXrximFpbTmX4rheLs5uNSftiiRY", name: "Wrapped Ether", symbol: "WETH.e", decimals: 18, dex: EagleFi }, b_token: Token { address: "AS12U4TZfNK7qoLyEERBBRDMu8nm5MKoRzPXDXans4v9wdATZedz9", name: "Wrapped Massa", symbol: "WMAS", decimals: 9, dex: EagleFi }, a_reserve: 2.128691605372594, b_reserve: 375974.044342453, dex: EagleFi, swap_fee: 0.3, a_price: 176622.1294778135, b_price: 5.6618046841384935e-6, active_bin_id: 0, bin_step: 0, bins: {} }
2025-07-01T09:41:45.429848Z  INFO quedge::spawns::swap_fetcher: Swap event processed successfully
2025-07-01T09:41:45.430133Z DEBUG quedge::helpers: Detecting arbitrage cycles...
2025-07-01T09:41:45.441760Z DEBUG quedge::helpers: Arbitrage cycles found (4 total):
2025-07-01T09:41:45.441936Z DEBUG quedge::helpers: Cycle path Inisde Simulation Task:
2025-07-01T09:41:45.442039Z DEBUG quedge::helpers::display: Path: ["WMAS-Dusa", "WMAS-EagleFi", "POM-EagleFi", "POM-Dusa", "WMAS-Dusa"]
2025-07-01T09:41:45.442215Z DEBUG quedge::helpers: Cycle path Inisde Simulation Task:
2025-07-01T09:41:45.442313Z  INFO quedge::helpers: Default start amount for token AS12U4TZfNK7qoLyEERBBRDMu8nm5MKoRzPXDXans4v9wdATZedz9 is 100
2025-07-01T09:41:45.442398Z DEBUG quedge::helpers::display: Path: ["POM-Dusa", "WMAS-Dusa", "WMAS-EagleFi", "POM-EagleFi", "POM-Dusa"]
2025-07-01T09:41:45.442476Z  INFO quedge::helpers: Offchain Amount out after Offchain simulation: 0.000000015630701406604442
2025-07-01T09:41:45.442584Z DEBUG quedge::helpers: Cycle path Inisde Simulation Task:
2025-07-01T09:41:45.442672Z  INFO quedge::helpers: Offchain Earning percentage: -99.9999999843693
2025-07-01T09:41:45.442757Z  INFO quedge::helpers: Default start amount for token AS1nqHKXpnFXqhDExTskXmBbbVpVpUbCQVtNSXLCqUDSUXihdWRq is 40207
2025-07-01T09:41:45.442840Z DEBUG quedge::helpers::display: Path: ["WMAS-EagleFi", "POM-EagleFi", "POM-Dusa", "WMAS-Dusa", "WMAS-EagleFi"]
2025-07-01T09:41:45.442920Z  WARN quedge::helpers: Not Valid Cycle Because Offchain Earning Percentage is less than margin -99.9999999843693 < 0.1
2025-07-01T09:41:45.443020Z  INFO quedge::helpers: Offchain Amount out after Offchain simulation: 0.00007765593260211698
2025-07-01T09:41:45.443095Z DEBUG quedge::helpers: Cycle path Inisde Simulation Task:
2025-07-01T09:41:45.443167Z  INFO quedge::helpers: Default start amount for token AS12U4TZfNK7qoLyEERBBRDMu8nm5MKoRzPXDXans4v9wdATZedz9 is 100
2025-07-01T09:41:45.443239Z  INFO quedge::helpers: Offchain Earning percentage: -99.99999980685968
2025-07-01T09:41:45.443311Z DEBUG quedge::helpers::display: Path: ["POM-EagleFi", "POM-Dusa", "WMAS-Dusa", "WMAS-EagleFi", "POM-EagleFi"]
2025-07-01T09:41:45.443431Z  INFO quedge::helpers: Offchain Amount out after Offchain simulation: 0.000000015630701406604442
2025-07-01T09:41:45.443507Z  WARN quedge::helpers: Not Valid Cycle Because Offchain Earning Percentage is less than margin -99.99999980685968 < 0.1
2025-07-01T09:41:45.443614Z  INFO quedge::helpers: Default start amount for token AS1nqHKXpnFXqhDExTskXmBbbVpVpUbCQVtNSXLCqUDSUXihdWRq is 40207
2025-07-01T09:41:45.443712Z  INFO quedge::helpers: Offchain Earning percentage: -99.9999999843693
2025-07-01T09:41:45.443825Z  INFO quedge::helpers: Offchain Amount out after Offchain simulation: 0.00007765593260211698
2025-07-01T09:41:45.443928Z  WARN quedge::helpers: Not Valid Cycle Because Offchain Earning Percentage is less than margin -99.9999999843693 < 0.1
2025-07-01T09:41:45.444010Z  INFO quedge::helpers: Offchain Earning percentage: -99.99999980685968
2025-07-01T09:41:45.444123Z  WARN quedge::helpers: Not Valid Cycle Because Offchain Earning Percentage is less than margin -99.99999980685968 < 0.1
2025-07-01T09:41:45.444239Z  INFO quedge::helpers: Found 0 valid cycles
2025-07-01T09:41:45.444318Z  WARN quedge::helpers: No profitable arbitrage cycle found.
2025-07-01T09:41:54.761725Z  WARN quedge::spawns::swap_fetcher: Operation O1X7nycFiLwfKGmTtvsVGAx6B9in7KqQMoUKMrF6GgHqmCWhpAN is not successful, skipping
2025-07-01T09:42:10.774549Z  WARN quedge::spawns::swap_fetcher: Operation O12NBnQ7wttc9KEYjhpaT8euzBzRau4fpPC6v8TiBG2af8Q7wR5 is not successful, skipping
2025-07-01T09:42:26.750359Z  WARN quedge::spawns::swap_fetcher: Operation O1xBDsA2RH5QmZ3agUqtx6e9NU9YBasb2FaHRak7F9ToGVS814C is not successful, skipping
2025-07-01T09:43:37.226749Z  WARN quedge::spawns::swap_fetcher: Operation O12ArzMatt3vUjNVx8wUu2kkVr7qZ2NVMpzD8m7XqSqib3qohQPS is not successful, skipping
2025-07-01T09:45:12.237282Z  INFO quedge::spawns::swap_fetcher: Found 1 Swap events from the operation O12rnwJvRWM6uD21veGonjKut7nPfQracq2scDCxGhRjKeiiELkw. Processing them...
2025-07-01T09:45:12.237394Z DEBUG quedge::spawns::swap_fetcher: Processing last event of the operation O12rnwJvRWM6uD21veGonjKut7nPfQracq2scDCxGhRjKeiiELkw...
2025-07-01T09:45:12.237482Z  WARN quedge::spawns::swap_fetcher: Pool AS12UMSUxgpRBB6ArZDJ19arHoxNkkpdfofQGekAiAJqsuE6PEFJy not found in the pools hash map
2025-07-01T09:45:12.237568Z  INFO quedge::spawns::swap_fetcher: New detected swap on pool: AS1Ba1T2mMpHvLEhTvsNkSDqm4djno2ezVojGrtLf1wRd8ThfZD3
2025-07-01T09:45:12.521249Z  INFO quedge::strategies::bellman_ford_matrix: Updating sparse matrix by pool: Pool { address: "AS1Ba1T2mMpHvLEhTvsNkSDqm4djno2ezVojGrtLf1wRd8ThfZD3", a_token: Token { address: "AS124vf3YfAJCSCQVYKczzuWWpXrximFpbTmX4rheLs5uNSftiiRY", name: "Wrapped Ether", symbol: "WETH.e", decimals: 18, dex: Dusa }, b_token: Token { address: "AS12U4TZfNK7qoLyEERBBRDMu8nm5MKoRzPXDXans4v9wdATZedz9", name: "Wrapped Massa", symbol: "WMAS", decimals: 9, dex: Dusa }, a_reserve: 2.3008263513689102, b_reserve: 540724.216563457, dex: Dusa, swap_fee: 0.0, a_price: 175650.14428243844, b_price: 5.6931350901257435e-6, active_bin_id: 8382839, bin_step: 15, bins: {8382838: Bin { id: 8382838, a_reserve: 0.0, b_reserve: 2780.002241435 }, 8382839: Bin { id: 8382839, a_reserve: 0.002364605627867293, b_reserve: 2368.434904248 }, 8382840: Bin { id: 8382840, a_reserve: 0.012190877028295763, b_reserve: 0.0 }} }
2025-07-01T09:45:12.521461Z  INFO quedge::spawns::swap_fetcher: Swap event processed successfully
2025-07-01T09:45:12.521672Z DEBUG quedge::helpers: Detecting arbitrage cycles...
2025-07-01T09:45:12.533600Z DEBUG quedge::helpers: Arbitrage cycles found (4 total):
2025-07-01T09:45:12.533788Z DEBUG quedge::helpers: Cycle path Inisde Simulation Task:
2025-07-01T09:45:12.533878Z DEBUG quedge::helpers::display: Path: ["WMAS-Dusa", "WMAS-EagleFi", "POM-EagleFi", "POM-Dusa", "WMAS-Dusa"]
2025-07-01T09:45:12.534018Z DEBUG quedge::helpers: Cycle path Inisde Simulation Task:
2025-07-01T09:45:12.534108Z DEBUG quedge::helpers::display: Path: ["POM-Dusa", "WMAS-Dusa", "WMAS-EagleFi", "POM-EagleFi", "POM-Dusa"]
2025-07-01T09:45:12.534246Z DEBUG quedge::helpers: Cycle path Inisde Simulation Task:
2025-07-01T09:45:12.534329Z  INFO quedge::helpers: Default start amount for token AS12U4TZfNK7qoLyEERBBRDMu8nm5MKoRzPXDXans4v9wdATZedz9 is 100
2025-07-01T09:45:12.534404Z DEBUG quedge::helpers::display: Path: ["WMAS-EagleFi", "POM-EagleFi", "POM-Dusa", "WMAS-Dusa", "WMAS-EagleFi"]
2025-07-01T09:45:12.534480Z  INFO quedge::helpers: Offchain Amount out after Offchain simulation: 0.000000015630701406604442
2025-07-01T09:45:12.534559Z DEBUG quedge::helpers: Cycle path Inisde Simulation Task:
2025-07-01T09:45:12.534629Z  INFO quedge::helpers: Offchain Earning percentage: -99.9999999843693
2025-07-01T09:45:12.534702Z  INFO quedge::helpers: Default start amount for token AS1nqHKXpnFXqhDExTskXmBbbVpVpUbCQVtNSXLCqUDSUXihdWRq is 40207
2025-07-01T09:45:12.534767Z DEBUG quedge::helpers::display: Path: ["POM-EagleFi", "POM-Dusa", "WMAS-Dusa", "WMAS-EagleFi", "POM-EagleFi"]
2025-07-01T09:45:12.534868Z  WARN quedge::helpers: Not Valid Cycle Because Offchain Earning Percentage is less than margin -99.9999999843693 < 0.1
2025-07-01T09:45:12.534962Z  INFO quedge::helpers: Offchain Amount out after Offchain simulation: 0.00007765593260211698
2025-07-01T09:45:12.535037Z  INFO quedge::helpers: Default start amount for token AS12U4TZfNK7qoLyEERBBRDMu8nm5MKoRzPXDXans4v9wdATZedz9 is 100
2025-07-01T09:45:12.535197Z  INFO quedge::helpers: Default start amount for token AS1nqHKXpnFXqhDExTskXmBbbVpVpUbCQVtNSXLCqUDSUXihdWRq is 40207
2025-07-01T09:45:12.535278Z  INFO quedge::helpers: Offchain Earning percentage: -99.99999980685968
2025-07-01T09:45:12.535355Z  INFO quedge::helpers: Offchain Amount out after Offchain simulation: 0.000000015630701406604442
2025-07-01T09:45:12.535430Z  INFO quedge::helpers: Offchain Amount out after Offchain simulation: 0.00007765593260211698
2025-07-01T09:45:12.535505Z  WARN quedge::helpers: Not Valid Cycle Because Offchain Earning Percentage is less than margin -99.99999980685968 < 0.1
2025-07-01T09:45:12.535586Z  INFO quedge::helpers: Offchain Earning percentage: -99.9999999843693
2025-07-01T09:45:12.535657Z  INFO quedge::helpers: Offchain Earning percentage: -99.99999980685968
2025-07-01T09:45:12.535743Z  WARN quedge::helpers: Not Valid Cycle Because Offchain Earning Percentage is less than margin -99.9999999843693 < 0.1
2025-07-01T09:45:12.535812Z  WARN quedge::helpers: Not Valid Cycle Because Offchain Earning Percentage is less than margin -99.99999980685968 < 0.1
2025-07-01T09:45:12.535923Z  INFO quedge::helpers: Found 0 valid cycles
2025-07-01T09:45:12.536000Z  WARN quedge::helpers: No profitable arbitrage cycle found.
2025-07-01T09:45:22.833943Z  WARN quedge::spawns::swap_fetcher: Operation O1GteT9Ux4c4FPyZfrePuWaWMGGinKZTZEHUTemTkh1s3dg7D2N is not successful, skipping
2025-07-01T09:45:45.231435Z  INFO quedge::spawns::swap_fetcher: Found 1 Swap events from the operation O12F7ZE9PkbYyyHijNa6k9vVvdH9xns3vXUosderVDMcj9K8fcHn. Processing them...
2025-07-01T09:45:45.231566Z DEBUG quedge::spawns::swap_fetcher: Processing last event of the operation O12F7ZE9PkbYyyHijNa6k9vVvdH9xns3vXUosderVDMcj9K8fcHn...
2025-07-01T09:45:45.231668Z  WARN quedge::spawns::swap_fetcher: Pool AS12UMSUxgpRBB6ArZDJ19arHoxNkkpdfofQGekAiAJqsuE6PEFJy not found in the pools hash map
2025-07-01T09:45:45.231752Z  INFO quedge::spawns::swap_fetcher: New detected swap on pool: AS1F2SAbhvknGLQKzfjaizKiZhEEd6mS1HLpZqPc2atzKE4VTNv9
2025-07-01T09:45:45.961203Z  INFO quedge::strategies::bellman_ford_matrix: Updating sparse matrix by pool: Pool { address: "AS1F2SAbhvknGLQKzfjaizKiZhEEd6mS1HLpZqPc2atzKE4VTNv9", a_token: Token { address: "AS12LKs9txoSSy8JgFJgV96m8k5z9pgzjYMYSshwN67mFVuj3bdUV", name: "Bsc USDT", symbol: "USDT.b", decimals: 18, dex: Dusa }, b_token: Token { address: "AS1hCJXjndR4c9vekLWsXGnrdigp4AaZ7uYG3UKFzzKnWVsrNLPJ", name: "USD Coin", symbol: "USDC.e", decimals: 6, dex: Dusa }, a_reserve: 578.7284593563301, b_reserve: 31056.84544, dex: Dusa, swap_fee: 0.0, a_price: 1.0009030062984086, b_price: 0.999097808386301, active_bin_id: 8112293, bin_step: 1, bins: {8112292: Bin { id: 8112292, a_reserve: 0.0, b_reserve: 27.010864 }, 8112293: Bin { id: 8112293, a_reserve: 192.3618236559905, b_reserve: 25352.443596 }, 8112294: Bin { id: 8112294, a_reserve: 263.21335905959023, b_reserve: 0.0 }} }
2025-07-01T09:45:45.961436Z  INFO quedge::spawns::swap_fetcher: Swap event processed successfully
2025-07-01T09:45:45.961694Z DEBUG quedge::helpers: Detecting arbitrage cycles...
2025-07-01T09:45:45.973530Z DEBUG quedge::helpers: Arbitrage cycles found (4 total):
2025-07-01T09:45:45.973705Z DEBUG quedge::helpers: Cycle path Inisde Simulation Task:
2025-07-01T09:45:45.973803Z DEBUG quedge::helpers::display: Path: ["WMAS-Dusa", "WMAS-EagleFi", "POM-EagleFi", "POM-Dusa", "WMAS-Dusa"]
2025-07-01T09:45:45.973990Z DEBUG quedge::helpers: Cycle path Inisde Simulation Task:
2025-07-01T09:45:45.974077Z  INFO quedge::helpers: Default start amount for token AS12U4TZfNK7qoLyEERBBRDMu8nm5MKoRzPXDXans4v9wdATZedz9 is 100
2025-07-01T09:45:45.974154Z DEBUG quedge::helpers::display: Path: ["POM-Dusa", "WMAS-Dusa", "WMAS-EagleFi", "POM-EagleFi", "POM-Dusa"]
2025-07-01T09:45:45.974245Z  INFO quedge::helpers: Offchain Amount out after Offchain simulation: 0.000000015630701406604442
2025-07-01T09:45:45.974322Z DEBUG quedge::helpers: Cycle path Inisde Simulation Task:
2025-07-01T09:45:45.974405Z  INFO quedge::helpers: Offchain Earning percentage: -99.9999999843693
2025-07-01T09:45:45.974473Z  INFO quedge::helpers: Default start amount for token AS1nqHKXpnFXqhDExTskXmBbbVpVpUbCQVtNSXLCqUDSUXihdWRq is 40207
2025-07-01T09:45:45.974550Z DEBUG quedge::helpers::display: Path: ["WMAS-EagleFi", "POM-EagleFi", "POM-Dusa", "WMAS-Dusa", "WMAS-EagleFi"]
2025-07-01T09:45:45.974623Z  WARN quedge::helpers: Not Valid Cycle Because Offchain Earning Percentage is less than margin -99.9999999843693 < 0.1
2025-07-01T09:45:45.974737Z DEBUG quedge::helpers: Cycle path Inisde Simulation Task:
2025-07-01T09:45:45.974813Z  INFO quedge::helpers: Offchain Amount out after Offchain simulation: 0.00007765593260211698
2025-07-01T09:45:45.974911Z  INFO quedge::helpers: Default start amount for token AS12U4TZfNK7qoLyEERBBRDMu8nm5MKoRzPXDXans4v9wdATZedz9 is 100
2025-07-01T09:45:45.974987Z DEBUG quedge::helpers::display: Path: ["POM-EagleFi", "POM-Dusa", "WMAS-Dusa", "WMAS-EagleFi", "POM-EagleFi"]
2025-07-01T09:45:45.975073Z  INFO quedge::helpers: Offchain Earning percentage: -99.99999980685968
2025-07-01T09:45:45.975146Z  INFO quedge::helpers: Default start amount for token AS1nqHKXpnFXqhDExTskXmBbbVpVpUbCQVtNSXLCqUDSUXihdWRq is 40207
2025-07-01T09:45:45.975222Z  INFO quedge::helpers: Offchain Amount out after Offchain simulation: 0.000000015630701406604442
2025-07-01T09:45:45.975291Z  WARN quedge::helpers: Not Valid Cycle Because Offchain Earning Percentage is less than margin -99.99999980685968 < 0.1
2025-07-01T09:45:45.975385Z  INFO quedge::helpers: Offchain Amount out after Offchain simulation: 0.00007765593260211698
2025-07-01T09:45:45.975453Z  INFO quedge::helpers: Offchain Earning percentage: -99.9999999843693
2025-07-01T09:45:45.975517Z  INFO quedge::helpers: Offchain Earning percentage: -99.99999980685968
2025-07-01T09:45:45.975586Z  WARN quedge::helpers: Not Valid Cycle Because Offchain Earning Percentage is less than margin -99.9999999843693 < 0.1
2025-07-01T09:45:45.975650Z  WARN quedge::helpers: Not Valid Cycle Because Offchain Earning Percentage is less than margin -99.99999980685968 < 0.1
2025-07-01T09:45:45.975760Z  INFO quedge::helpers: Found 0 valid cycles
2025-07-01T09:45:45.975835Z  WARN quedge::helpers: No profitable arbitrage cycle found.
2025-07-01T09:45:54.988130Z  WARN quedge::spawns::swap_fetcher: Operation O12JoFgpGVXbP5yVsFWiaAQsKn1PhjcktsCk9jjCFgpUX832GiTU is not successful, skipping
2025-07-01T09:49:13.248649Z  INFO quedge::spawns::swap_fetcher: Found 1 Swap events from the operation O1od6dgvg33rU2LUUgh7MWwAUZJXYGYn5wwqfHmMgfgEzAs49pg. Processing them...
2025-07-01T09:49:13.248772Z DEBUG quedge::spawns::swap_fetcher: Processing last event of the operation O1od6dgvg33rU2LUUgh7MWwAUZJXYGYn5wwqfHmMgfgEzAs49pg...
2025-07-01T09:49:13.248868Z  WARN quedge::spawns::swap_fetcher: Pool AS12UMSUxgpRBB6ArZDJ19arHoxNkkpdfofQGekAiAJqsuE6PEFJy not found in the pools hash map
2025-07-01T09:49:13.248965Z  INFO quedge::spawns::swap_fetcher: New detected swap on pool: AS1F2SAbhvknGLQKzfjaizKiZhEEd6mS1HLpZqPc2atzKE4VTNv9
2025-07-01T09:49:13.810176Z  INFO quedge::strategies::bellman_ford_matrix: Updating sparse matrix by pool: Pool { address: "AS1F2SAbhvknGLQKzfjaizKiZhEEd6mS1HLpZqPc2atzKE4VTNv9", a_token: Token { address: "AS12LKs9txoSSy8JgFJgV96m8k5z9pgzjYMYSshwN67mFVuj3bdUV", name: "Bsc USDT", symbol: "USDT.b", decimals: 18, dex: Dusa }, b_token: Token { address: "AS1hCJXjndR4c9vekLWsXGnrdigp4AaZ7uYG3UKFzzKnWVsrNLPJ", name: "USD Coin", symbol: "USDC.e", decimals: 6, dex: Dusa }, a_reserve: 543.9690283413598, b_reserve: 31091.636259, dex: Dusa, swap_fee: 0.0, a_price: 1.0009030062984086, b_price: 0.999097808386301, active_bin_id: 8112293, bin_step: 1, bins: {8112292: Bin { id: 8112292, a_reserve: 0.0, b_reserve: 27.010864 }, 8112293: Bin { id: 8112293, a_reserve: 157.60239264102026, b_reserve: 25387.234415 }, 8112294: Bin { id: 8112294, a_reserve: 263.21335905959023, b_reserve: 0.0 }} }
2025-07-01T09:49:13.810381Z  INFO quedge::spawns::swap_fetcher: Swap event processed successfully
2025-07-01T09:49:13.810626Z DEBUG quedge::helpers: Detecting arbitrage cycles...
2025-07-01T09:49:13.822105Z DEBUG quedge::helpers: Arbitrage cycles found (4 total):
2025-07-01T09:49:13.822263Z DEBUG quedge::helpers: Cycle path Inisde Simulation Task:
2025-07-01T09:49:13.822366Z DEBUG quedge::helpers::display: Path: ["WMAS-Dusa", "WMAS-EagleFi", "POM-EagleFi", "POM-Dusa", "WMAS-Dusa"]
2025-07-01T09:49:13.822510Z DEBUG quedge::helpers: Cycle path Inisde Simulation Task:
2025-07-01T09:49:13.822600Z DEBUG quedge::helpers::display: Path: ["POM-Dusa", "WMAS-Dusa", "WMAS-EagleFi", "POM-EagleFi", "POM-Dusa"]
2025-07-01T09:49:13.822742Z DEBUG quedge::helpers: Cycle path Inisde Simulation Task:
2025-07-01T09:49:13.822826Z  INFO quedge::helpers: Default start amount for token AS12U4TZfNK7qoLyEERBBRDMu8nm5MKoRzPXDXans4v9wdATZedz9 is 100
2025-07-01T09:49:13.822903Z DEBUG quedge::helpers::display: Path: ["WMAS-EagleFi", "POM-EagleFi", "POM-Dusa", "WMAS-Dusa", "WMAS-EagleFi"]
2025-07-01T09:49:13.822980Z  INFO quedge::helpers: Offchain Amount out after Offchain simulation: 0.000000015630701406604442
2025-07-01T09:49:13.823059Z DEBUG quedge::helpers: Cycle path Inisde Simulation Task:
2025-07-01T09:49:13.823135Z  INFO quedge::helpers: Offchain Earning percentage: -99.9999999843693
2025-07-01T09:49:13.823206Z  INFO quedge::helpers: Default start amount for token AS1nqHKXpnFXqhDExTskXmBbbVpVpUbCQVtNSXLCqUDSUXihdWRq is 40207
2025-07-01T09:49:13.823269Z DEBUG quedge::helpers::display: Path: ["POM-EagleFi", "POM-Dusa", "WMAS-Dusa", "WMAS-EagleFi", "POM-EagleFi"]
2025-07-01T09:49:13.823353Z  WARN quedge::helpers: Not Valid Cycle Because Offchain Earning Percentage is less than margin -99.9999999843693 < 0.1
2025-07-01T09:49:13.823431Z  INFO quedge::helpers: Offchain Amount out after Offchain simulation: 0.00007765593260211698
2025-07-01T09:49:13.823508Z  INFO quedge::helpers: Default start amount for token AS1nqHKXpnFXqhDExTskXmBbbVpVpUbCQVtNSXLCqUDSUXihdWRq is 40207
2025-07-01T09:49:13.823626Z  INFO quedge::helpers: Default start amount for token AS12U4TZfNK7qoLyEERBBRDMu8nm5MKoRzPXDXans4v9wdATZedz9 is 100
2025-07-01T09:49:13.823697Z  INFO quedge::helpers: Offchain Earning percentage: -99.99999980685968
2025-07-01T09:49:13.823770Z  INFO quedge::helpers: Offchain Amount out after Offchain simulation: 0.00007765593260211698
2025-07-01T09:49:13.823839Z  INFO quedge::helpers: Offchain Amount out after Offchain simulation: 0.000000015630701406604442
2025-07-01T09:49:13.823909Z  WARN quedge::helpers: Not Valid Cycle Because Offchain Earning Percentage is less than margin -99.99999980685968 < 0.1
2025-07-01T09:49:13.823975Z  INFO quedge::helpers: Offchain Earning percentage: -99.99999980685968
2025-07-01T09:49:13.824044Z  INFO quedge::helpers: Offchain Earning percentage: -99.9999999843693
2025-07-01T09:49:13.824113Z  WARN quedge::helpers: Not Valid Cycle Because Offchain Earning Percentage is less than margin -99.99999980685968 < 0.1
2025-07-01T09:49:13.824177Z  WARN quedge::helpers: Not Valid Cycle Because Offchain Earning Percentage is less than margin -99.9999999843693 < 0.1
2025-07-01T09:49:13.824286Z  INFO quedge::helpers: Found 0 valid cycles
2025-07-01T09:49:13.824362Z  WARN quedge::helpers: No profitable arbitrage cycle found.
2025-07-01T09:49:22.769421Z  WARN quedge::spawns::swap_fetcher: Operation O1QheuXZYUwUQbUF447VnwZF3PaPLbz2QzNcge232MxSHoGCzzQ is not successful, skipping
2025-07-01T09:52:09.324509Z  INFO quedge::spawns::swap_fetcher: Found 1 Swap events from the operation O12Kzqw3r3zUpBTDTXNZnHYZeg7L1UEVVC1qRwwFxv7ZrkVFJZNi. Processing them...
2025-07-01T09:52:09.324658Z DEBUG quedge::spawns::swap_fetcher: Processing last event of the operation O12Kzqw3r3zUpBTDTXNZnHYZeg7L1UEVVC1qRwwFxv7ZrkVFJZNi...
2025-07-01T09:52:09.324780Z  WARN quedge::spawns::swap_fetcher: Pool AS12UMSUxgpRBB6ArZDJ19arHoxNkkpdfofQGekAiAJqsuE6PEFJy not found in the pools hash map
2025-07-01T09:52:09.324924Z  INFO quedge::spawns::swap_fetcher: New detected swap on pool: AS1F2SAbhvknGLQKzfjaizKiZhEEd6mS1HLpZqPc2atzKE4VTNv9
2025-07-01T09:52:09.655640Z  INFO quedge::strategies::bellman_ford_matrix: Updating sparse matrix by pool: Pool { address: "AS1F2SAbhvknGLQKzfjaizKiZhEEd6mS1HLpZqPc2atzKE4VTNv9", a_token: Token { address: "AS12LKs9txoSSy8JgFJgV96m8k5z9pgzjYMYSshwN67mFVuj3bdUV", name: "Bsc USDT", symbol: "USDT.b", decimals: 18, dex: Dusa }, b_token: Token { address: "AS1hCJXjndR4c9vekLWsXGnrdigp4AaZ7uYG3UKFzzKnWVsrNLPJ", name: "USD Coin", symbol: "USDC.e", decimals: 6, dex: Dusa }, a_reserve: 455.08289008377693, b_reserve: 31180.602662, dex: Dusa, swap_fee: 0.0, a_price: 1.0009030062984086, b_price: 0.999097808386301, active_bin_id: 8112293, bin_step: 1, bins: {8112292: Bin { id: 8112292, a_reserve: 0.0, b_reserve: 27.010864 }, 8112293: Bin { id: 8112293, a_reserve: 68.71625438343737, b_reserve: 25476.200818 }, 8112294: Bin { id: 8112294, a_reserve: 263.21335905959023, b_reserve: 0.0 }} }
2025-07-01T09:52:09.655870Z  INFO quedge::spawns::swap_fetcher: Swap event processed successfully
2025-07-01T09:52:09.656071Z DEBUG quedge::helpers: Detecting arbitrage cycles...
2025-07-01T09:52:09.667479Z DEBUG quedge::helpers: Arbitrage cycles found (4 total):
2025-07-01T09:52:09.667623Z DEBUG quedge::helpers: Cycle path Inisde Simulation Task:
2025-07-01T09:52:09.667742Z DEBUG quedge::helpers::display: Path: ["WMAS-Dusa", "WMAS-EagleFi", "POM-EagleFi", "POM-Dusa", "WMAS-Dusa"]
2025-07-01T09:52:09.667981Z DEBUG quedge::helpers: Cycle path Inisde Simulation Task:
2025-07-01T09:52:09.668126Z  INFO quedge::helpers: Default start amount for token AS12U4TZfNK7qoLyEERBBRDMu8nm5MKoRzPXDXans4v9wdATZedz9 is 100
2025-07-01T09:52:09.668235Z DEBUG quedge::helpers::display: Path: ["POM-Dusa", "WMAS-Dusa", "WMAS-EagleFi", "POM-EagleFi", "POM-Dusa"]
2025-07-01T09:52:09.668336Z  INFO quedge::helpers: Offchain Amount out after Offchain simulation: 0.000000015630701406604442
2025-07-01T09:52:09.668441Z DEBUG quedge::helpers: Cycle path Inisde Simulation Task:
2025-07-01T09:52:09.668549Z  INFO quedge::helpers: Offchain Earning percentage: -99.9999999843693
2025-07-01T09:52:09.668648Z  INFO quedge::helpers: Default start amount for token AS1nqHKXpnFXqhDExTskXmBbbVpVpUbCQVtNSXLCqUDSUXihdWRq is 40207
2025-07-01T09:52:09.668716Z DEBUG quedge::helpers::display: Path: ["WMAS-EagleFi", "POM-EagleFi", "POM-Dusa", "WMAS-Dusa", "WMAS-EagleFi"]
2025-07-01T09:52:09.668829Z  WARN quedge::helpers: Not Valid Cycle Because Offchain Earning Percentage is less than margin -99.9999999843693 < 0.1
2025-07-01T09:52:09.668925Z  INFO quedge::helpers: Offchain Amount out after Offchain simulation: 0.00007765593260211698
2025-07-01T09:52:09.668997Z DEBUG quedge::helpers: Cycle path Inisde Simulation Task:
2025-07-01T09:52:09.669062Z  INFO quedge::helpers: Default start amount for token AS12U4TZfNK7qoLyEERBBRDMu8nm5MKoRzPXDXans4v9wdATZedz9 is 100
2025-07-01T09:52:09.669126Z  INFO quedge::helpers: Offchain Earning percentage: -99.99999980685968
2025-07-01T09:52:09.669193Z DEBUG quedge::helpers::display: Path: ["POM-EagleFi", "POM-Dusa", "WMAS-Dusa", "WMAS-EagleFi", "POM-EagleFi"]
2025-07-01T09:52:09.669294Z  INFO quedge::helpers: Offchain Amount out after Offchain simulation: 0.000000015630701406604442
2025-07-01T09:52:09.669367Z  WARN quedge::helpers: Not Valid Cycle Because Offchain Earning Percentage is less than margin -99.99999980685968 < 0.1
2025-07-01T09:52:09.669433Z  INFO quedge::helpers: Default start amount for token AS1nqHKXpnFXqhDExTskXmBbbVpVpUbCQVtNSXLCqUDSUXihdWRq is 40207
2025-07-01T09:52:09.669496Z  INFO quedge::helpers: Offchain Earning percentage: -99.9999999843693
2025-07-01T09:52:09.669563Z  INFO quedge::helpers: Offchain Amount out after Offchain simulation: 0.00007765593260211698
2025-07-01T09:52:09.669633Z  WARN quedge::helpers: Not Valid Cycle Because Offchain Earning Percentage is less than margin -99.9999999843693 < 0.1
2025-07-01T09:52:09.669696Z  INFO quedge::helpers: Offchain Earning percentage: -99.99999980685968
2025-07-01T09:52:09.669772Z  WARN quedge::helpers: Not Valid Cycle Because Offchain Earning Percentage is less than margin -99.99999980685968 < 0.1
2025-07-01T09:52:09.669889Z  INFO quedge::helpers: Found 0 valid cycles
2025-07-01T09:52:09.669964Z  WARN quedge::helpers: No profitable arbitrage cycle found.
2025-07-01T09:52:18.759217Z  WARN quedge::spawns::swap_fetcher: Operation O1xLoYTe63Uhc7Rv9cKuZMTmgGmRpbEaDFctN5WL1kxhHJPLa2f is not successful, skipping
2025-07-01T09:54:32.409717Z DEBUG quedge::spawns::balance_notifier: Balance of AU16raXifo3FExijWrhUBLBneaHnWGicWbShgyXgAKB1bnj1yLvH is 156.123241022
2025-07-01T09:54:49.331452Z  WARN quedge::spawns::swap_fetcher: Operation O12pK5bhz18dMh9FfXs5EHJXiKTHqN5hcwURDSufao558BpkvRiX is not successful, skipping
