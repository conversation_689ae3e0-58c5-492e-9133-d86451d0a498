import { Args, u256ToBytes } from '@massalabs/as-types';
import { Address, Context, generateEvent } from '@massalabs/massa-as-sdk';
import { IDusaPair } from '../interfaces/dusa/IDusaPair';
import { IDusaRouter } from '../interfaces/dusa/IDusaRouter';
import { u256 } from 'as-bignum/assembly';
import { IMRC20 } from '../interfaces/IMRC20';

const dusaRouterAddress =
  'AS1XqtvX3rz2RWbnqLfaYVKEjM3VS5pny9yKDdXcmJ5C1vrcLEFd';
const eagleFiRouterAddress =
  'AS1G6MLbnZDm6W8E8rxJcsgSRxdyNoJUuTLXhBzCbbkwCyiNjjd4';

export function constructor(_: StaticArray<u8>): void {
  assert(Context.isDeployingContract());

  generateEvent('Constructor called');
}

export function swapDusa(binaryArgs: StaticArray<u8>): StaticArray<u8> {
  const args = new Args(binaryArgs);

  const amount = args.nextU256().expect('Amount is missing');
  const to = args.nextString().expect('To is missing');
  const binStep = args.nextU64().expect('Bin step is missing');
  const tokenPathString = args
    .nextStringArray()
    .expect('Token path is missing');
  const deadline = args.nextU64().expect('Deadline is missing');
  const masToSend = args.nextU64().expect('Massa to send is missing');

  const tokenPath = new Array<IMRC20>(tokenPathString.length);
  for (let i = 0; i < tokenPathString.length; i++) {
    tokenPath[i] = new IMRC20(new Address(tokenPathString[i]));
  }

  const dusaRouter = new IDusaRouter(new Address(dusaRouterAddress));

  const res = dusaRouter.swapExactTokensForTokens(
    amount,
    u256.Zero,
    [binStep],
    tokenPath,
    new Address(to),
    deadline,
    masToSend,
  );

  return u256ToBytes(res);
}

export function increaseAllowanceForDusa(binaryArgs: StaticArray<u8>): void {
  const args = new Args(binaryArgs);

  const token = args.nextString().expect('Token is missing');
  const amount = u256.Max;

  const tokenContract = new IMRC20(new Address(token));

  tokenContract.increaseAllowance(new Address(dusaRouterAddress), amount, 1000);
}

export function receiveCoins(_: StaticArray<u8>): void {
  generateEvent(`Received coins: ${Context.transferredCoins().toString()}`);
}
