use anyhow::Result;
use rust_massa_web3::client::grpc_client::PublicGrpcClient;
use tracing::{debug, error, info, warn};

use crate::{
    dexes::{dusa, eaglefi},
    types::{DEX, PoolArb},
};

pub async fn run_onchain_simulation(
    execution_path: &[PoolArb],
    start_amount: f64,
    grpc_client: &mut PublicGrpcClient,
) -> Result<f64> {
    let mut last_amount_out = start_amount;

    for pool_arb in execution_path {
        let pool = &pool_arb.pool;

        let amount_out: f64;

        // if it is an eaglefi pool, we need to calculate the amount out estimation
        if pool.dex == DEX::EagleFi {
            let token_in_address = if pool_arb.is_a_in {
                pool.a_token.address.clone()
            } else {
                pool.b_token.address.clone()
            };

            amount_out = eaglefi::get_swap_out_estimation(pool, last_amount_out, &token_in_address);
        } else {
            //  Calculate the amount out if swap happens
            amount_out = match dusa::get_onchain_swap_out_estimation(
                pool,
                last_amount_out,
                pool_arb.is_a_in,
                grpc_client,
            )
            .await
            {
                Ok(amount_out) => amount_out,
                Err(e) => {
                    error!(
                        "Failed to get onchain swap out estimation dor dusa pool {} : {}",
                        &pool.address, e
                    );
                    return Err(e);
                }
            }

            //  The Old way in case wants to rollback to fully offchain
            // amount_out = if pool_arb.is_a_in {
            //     last_amount_out * pool_arb.pool.a_price
            // } else {
            //     last_amount_out * pool_arb.pool.b_price
            // };
        }

        last_amount_out = amount_out;
    }

    Ok(last_amount_out)
}

pub async fn run_offchain_simulation(execution_path: &[PoolArb], start_amount: f64) -> Result<f64> {
    let mut last_amount_out = start_amount;

    for pool_arb in execution_path {
        let pool = &pool_arb.pool;

        let amount_out: f64;

        // if it is an eaglefi pool, we need to calculate the amount out estimation
        if pool.dex == DEX::EagleFi {
            let token_in_address = if pool_arb.is_a_in {
                pool.a_token.address.clone()
            } else {
                pool.b_token.address.clone()
            };

            amount_out = eaglefi::get_swap_out_estimation(pool, last_amount_out, &token_in_address);
        } else {
            //  The Old way in case wants to rollback to fully offchain
            amount_out = if pool_arb.is_a_in {
                last_amount_out * pool_arb.pool.a_price
            } else {
                last_amount_out * pool_arb.pool.b_price
            };
        }

        last_amount_out = amount_out;
    }

    Ok(last_amount_out)
}
