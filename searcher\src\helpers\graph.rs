use crate::types::TokenGraph;
use anyhow::{Result, anyhow};
use petgraph::dot::Dot;
use std::{
    fs::File,
    io::{BufWriter, Write},
};

pub fn save_graph_to_dot(token_graph: &TokenGraph) -> Result<String> {
    let graph = &token_graph.graph;
    let filename = "./graphs/graph.dot";

    let mut file = BufWriter::new(File::create(filename)?);

    let dot = format!("{:?}", Dot::with_config(graph, &[]));

    writeln!(file, "{}", dot)?;

    file.flush()?;

    Ok(filename.to_string())
}

pub fn save_graph_to_png(token_graph: &TokenGraph) -> Result<()> {
    let dot = save_graph_to_dot(token_graph)?;

    let png_filename = "./graphs/graph.png";

    let output = std::process::Command::new("dot")
        .arg("-Tpng")
        .arg(dot)
        .arg("-o")
        .arg(png_filename)
        .output()
        .expect("Failed to execute `dot` command. Is Graphviz installed?");
    if !output.status.success() {
        eprintln!(
            "Error generating PNG: {}",
            String::from_utf8_lossy(&output.stderr)
        );
        return Err(anyhow!("Dot command failed"));
    }
    Ok(())
}
