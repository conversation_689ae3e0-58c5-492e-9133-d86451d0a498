use std::collections::HashMap;

use anyhow::{Context, Result};
use display::print_cycle_path_symbols;
use futures::future::join_all;
use tracing::{debug, info, warn};

use crate::{
    config::{self},
    executor::{
        builders::{build_execution_arb_route, build_execution_path}, execute_arb, execute_arb_with_flash_loan, simulation::{run_offchain_simulation, run_onchain_simulation}
    },
    helpers::display::{find_token, print_sparse_matrix},
    serializable::arb_route::ArbRoute,
    state::{GlobalState, SharedAppState, GLOBAL_START_AMOUNTS},
    strategies,
    types::{Pool, PoolArb, Token, TokenMatrix},
};

pub mod csv_arb;
pub mod display;
pub mod dusa;
pub mod email;
pub mod graph;
pub mod massa;
pub mod sparse;

pub async fn detect_and_execute_arb(app_state: SharedAppState) -> Result<bool> {
    debug!("Detecting arbitrage cycles...");

    let grpc_client = app_state.grpc_client.clone();

    // Instead of locking the mutexes, then dropping them manually, we put the mutexes lock inside a scoped block. which means the lock is dropped at the end of the block.
    let pools_hash_map = &app_state.pools;

    let token_matrix = {
        let token_matrix_mut = app_state.token_matrix.lock().await;

        token_matrix_mut.clone()
    };

    // print_sparse_matrix(&token_matrix);

    let cycles = detect_arb_cycles(&token_matrix).await?;

    if cycles.is_empty() {
        warn!("No arbitrage opportunities found.");
        return Ok(false);
    };

    let all_eaglefi_tokens = app_state.all_eaglefi_tokens.clone();
    let all_dusa_tokens = app_state.all_dusa_tokens.clone();

    debug!("Arbitrage cycles found ({} total):", cycles.len());

    let simulation_tasks = cycles.iter().map(|cycle| {
        let grpc_client = grpc_client.clone();
        let token_matrix = token_matrix.clone();
        let pools_hash_map = pools_hash_map.clone();

        let cycle_path = build_tokens_path_from_cycle(
            cycle,
            &token_matrix,
            &all_eaglefi_tokens,
            &all_dusa_tokens,
        )
        .expect("Failed to build cycle path");

        debug!("Cycle path Inisde Simulation Task:");
        print_cycle_path_symbols(&cycle_path);

        tokio::spawn(async move {
            let execution_path = build_execution_path(&cycle_path, &pools_hash_map).await;

            // if execution_path.len() > 4 {
            //     return None;
            // }

            let first_token = &cycle_path[0];

            if !GLOBAL_START_AMOUNTS
                .tokens
                .contains_key(&first_token.address)
            {
                return None;
            }

            let default_start_amount =
                GLOBAL_START_AMOUNTS.get_default_start_amount_for_token(&first_token.address);

            info!(
                "Default start amount for token {} is {}",
                first_token.address, default_start_amount
            );

            let offchain_amount_out =
                run_offchain_simulation(&execution_path, default_start_amount)
                    .await
                    .ok()?;

            info!(
                "Offchain Amount out after Offchain simulation: {}",
                offchain_amount_out
            );

            let offchain_earning_percentage =
                ((offchain_amount_out - default_start_amount) / default_start_amount) * 100.0;

            info!(
                "Offchain Earning percentage: {}",
                offchain_earning_percentage
            );

            if offchain_earning_percentage < config::current::MARGIN {
                warn!(
                    "Not Valid Cycle Because Offchain Earning Percentage is less than margin {} < {}",
                    offchain_earning_percentage,
                    config::current::MARGIN
                );
                return None;
            }

            // If the offchain earning percentage is greater than the margin, we need to run the onchain simulation

            // Get the best start amount
            let best_start_amount = GLOBAL_START_AMOUNTS
                .get_start_amount_for_token(&cycle_path[0].address, offchain_earning_percentage);

            // Calauclat the actual simulation with the best start amount
            let amount_out = run_onchain_simulation(&execution_path, best_start_amount, &mut grpc_client.clone()).await.unwrap_or(0.0);

            info!("Amount out after Onchain simulation: {}", amount_out);

            let earning_percentage = ((amount_out - best_start_amount) / best_start_amount) * 100.0;

            info!("Earning percentage: {}", earning_percentage);

            if earning_percentage < config::current::MIN_PROFIT_PERCENT {
                warn!(
                    "Not Valid Cycle Because Earning Percentage is less than min profit percent {} < {}",
                    earning_percentage,
                    config::current::MIN_PROFIT_PERCENT
                );
                return None;
            }

            Some((cycle_path, execution_path, earning_percentage, amount_out, best_start_amount))
        })
    });

    let simulation_results = join_all(simulation_tasks).await;

    // Filter out failed or invalid results
    let valid_cycles: Vec<_> = simulation_results
        .into_iter()
        .filter_map(Result::ok)
        .filter_map(|opt| opt)
        .collect();

    info!("Found {} valid cycles", valid_cycles.len());

    // Now find the best among them
    if let Some((
        best_cycle_path,
        _best_execution_path,
        best_earning_percentage,
        best_simul_amount_out,
        best_start_amount,
    )) = valid_cycles
        .into_iter()
        .max_by(|a, b| a.3.partial_cmp(&b.3).unwrap_or(std::cmp::Ordering::Equal))
    {
        let best_execution_route =
            build_execution_arb_route(&best_cycle_path, &pools_hash_map, best_start_amount, 99.0)
                .await?;

        // Get the token config
        let token_config = GLOBAL_START_AMOUNTS
            .tokens
            .get(&best_cycle_path[0].address)
            .context("Failed to get token config")?;

        if token_config.is_support_flash_loan {
            // Execute the arbitrage with flash loan
            execute_arb_with_flash_loan(
                &best_execution_route,
                &best_cycle_path,
                &mut grpc_client.clone(),
                &token_matrix,
                best_start_amount,
                best_simul_amount_out,
                best_earning_percentage,
                token_config,
            )
            .await?;
        } else {
            execute_arb(
                &best_execution_route,
                &best_cycle_path,
                &mut grpc_client.clone(),
                &token_matrix,
                best_start_amount,
                best_simul_amount_out,
                best_earning_percentage,
            )
            .await?;
        }

        return Ok(true);
    }

    warn!("No profitable arbitrage cycle found.");
    Ok(false)
}

pub async fn detect_arb_cycles(token_matrix: &TokenMatrix) -> Result<Vec<Vec<usize>>> {
    use crate::config::current::MAX_DETECTED_CYCLES;

    let sparse_matrix = token_matrix.sparse_matrix.to_csr::<usize>();

    let cycles = strategies::bellman_ford_matrix::detect_all_negative_cycles_from_all_start_nodes(
        &sparse_matrix,
        MAX_DETECTED_CYCLES,
    )?;

    // let cycles: Vec<Vec<usize>>;

    // // Instead of detecting all cycles, we detect the best one from a specific start node (USDC-EagleFi or USDC-Dusa)
    // let usdc_dusa_key = format!("{}-{:?}", USDC_ADDRESS, DEX::Dusa);
    // let usdc_eglefi_key = format!("{}-{:?}", USDC_ADDRESS, DEX::EagleFi);

    // let usdc_dusa_idx = token_matrix
    //     .token_indices
    //     .get(&usdc_dusa_key)
    //     .context(format!("Token not found: {}", usdc_dusa_key))?;

    // let usdc_eglefi_idx = token_matrix
    //     .token_indices
    //     .get(&usdc_eglefi_key)
    //     .context(format!("Token not found: {}", usdc_eglefi_key))?;

    // let mut cycle = strategies::bellman_ford_matrix::detect_negative_cycle_from_each(
    //     &sparse_matrix,
    //     *usdc_eglefi_idx,
    // )?;

    // if cycle.is_none() {
    //     // If no cycle is found with usdc eaglefi, we try with the usdc dusa token
    //     // cycle = strategies::bellman_ford_matrix::detect_negative_cycle_from_each(
    //     //     &sparse_matrix,
    //     //     *usdc_dusa_idx,
    //     // )?;

    //     // if cycle.is_none() {
    //     //     warn!("No arbitrage opportunities found.");
    //     //     return Ok(vec![]);
    //     // }
    //     return Ok(vec![]);
    // };

    // cycles = vec![cycle.expect("cycle should not be none")];

    if cycles.is_empty() {
        warn!("No arbitrage opportunities found.");
        return Ok(vec![]);
    };

    Ok(cycles)
}

// Build the tokens path from the cycle
pub fn build_tokens_path_from_cycle(
    cycle: &[usize],
    matrix: &TokenMatrix,
    all_eaglefi_tokens: &HashMap<String, Token>,
    all_dusa_tokens: &HashMap<String, Token>,
) -> Result<Vec<Token>> {
    let mut path = Vec::new();
    for idx in cycle {
        let token_key = &matrix.index_tokens[*idx];
        let parts: Vec<&str> = token_key.split('-').collect();
        let address = parts[0];
        let dex_str = parts[1];

        // Find full token object from pools
        let token = find_token(address, dex_str, all_eaglefi_tokens, all_dusa_tokens)
            .context("Failed to find token")?;

        // Add token to path
        path.push(token);
    }

    Ok(path)
}
