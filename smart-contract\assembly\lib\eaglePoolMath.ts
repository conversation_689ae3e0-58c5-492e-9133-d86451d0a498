import { u256 } from "as-bignum/assembly";
import { SCALING_FACTOR } from "./constants";
import { SafeMath256 } from "./safeMath";

/**
 * Calculates the fee from a given input amount and fee rate.
 *
 * @param inputAmount - The amount from which the fee is to be calculated, represented as a u256.
 * @param feeRate - The fee rate as a floating-point number.
 * @returns The calculated fee as a u256, derived by multiplying the input amount by the fee rate
 *          and dividing by the SCALING_FACTOR (1,000,000).
 */
export function getFeeFromAmount(inputAmount: u256, feeRate: u64): u256 {
  // convert fee rate to u256
  const feeRate256 = u256.fromU64(feeRate);

  // Calculate the fee as: (inputAmount * feeRate256)
  const product = SafeMath256.mul(inputAmount, feeRate256);

  // Calculate the fee as: (inputAmount * feeRate256) / SCALING_FACTOR (1_000_000)
  const fee = SafeMath256.div(product, SCALING_FACTOR);

  return fee;
}
