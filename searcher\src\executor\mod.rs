pub mod allownace;
pub mod arb_start_amount;
pub mod builders;
pub mod simulation;

use anyhow::{Context, Result};
use rust_massa_web3::{
    alloy_primitives::{U256, utils::format_units},
    basic_elements::args::Args,
    client::grpc_client::PublicGrpcClient,
    constants::MAX_GAS_CALL,
    massa_proto_rs::massa::model::v1::OperationExecutionStatus,
};
use tracing::{debug, error, info};

use crate::{
    config::{CONFIG, current::ARB_CONTRACT_ADDRESS},
    executor::arb_start_amount::TokenArbConfig,
    helpers::{
        csv_arb::{ArbLogEntry, log_arb_result_to_csv},
        massa::extract_event_details,
    },
    serializable::{arb_route::ArbRoute, flash_loan::FlashLoan},
    types::{Token, TokenMatrix},
};

pub async fn execute_arb(
    execution_route: &[ArbRoute],
    cycle_path: &Vec<Token>,
    grpc_client: &mut PublicGrpcClient,
    _token_matrix: &TokenMatrix,
    start_amount: f64,
    amount_simul_out: f64,
    estim_percent: f64,
) -> Result<String> {
    let is_arb_execute = CONFIG.is_arb_execute;

    if !is_arb_execute {
        info!("Arb execute is disabled. Skipping execution...");
        return Ok(String::new());
    }

    let formatted_cycle_path = cycle_path
        .iter()
        .map(|t| format!("{}-{:?}", t.symbol, t.dex))
        .collect::<Vec<String>>();

    // Execute the execution route of the first cycle path found
    info!(
        "Executing arbitrage with start amount {} for cycle path: {:?}...",
        start_amount, formatted_cycle_path
    );

    let args = Args::new()
        .add_serializable_object_array(&execution_route)
        .serialize();

    let expire_period = grpc_client
        .get_absolute_expire_period()
        .await
        .context("Failed to get absolute expire period in execute_arb")?;

    // Get the token_in decimals
    let first_token_in_decimals = cycle_path[0].decimals;

    let operation_id = grpc_client
        .call_sc(
            ARB_CONTRACT_ADDRESS,
            "execute",
            args,
            "0.01",
            MAX_GAS_CALL,
            0.0,
            expire_period,
        )
        .await
        .context(format!(
            "Failed to call Execute on {}",
            ARB_CONTRACT_ADDRESS
        ))?;

    let operation_status = grpc_client
        .wait_for_operation(operation_id.clone(), true)
        .await
        .context("Failed to wait for operation")?;

    let operation_events = grpc_client
        .get_operation_events(&operation_id)
        .await
        .context("Failed to get operation events")?;

    // if the operation the last event will be the error event but if the operation succeeds it will contains the amout out
    let last_event = operation_events
        .last()
        .context("Failed to get last event")?;

    let event_details = extract_event_details(last_event);

    if operation_status != OperationExecutionStatus::Success as i32 {
        // debug!(
        //     "Showing all operation events data for operation id: {} ...",
        //     operation_id
        // );

        // for event in operation_events {
        //     let event_details = extract_event_details(&event);
        //     let event_data = event_details.event_data;

        //     debug!("Event Data: {:?}", &event_data);
        // }

        // Try to extract loss amount but don't propagate error if it fails
        let loss_amount_u256: U256 = event_details
            .event_data
            .split("LOSS_OF_FUNDS: ")
            .nth(1)
            .and_then(|err_part| err_part.split(" at ").next())
            .and_then(|err_part| err_part.parse().ok())
            .unwrap_or(U256::ZERO); // Default to 0.0 if parsing fails

        // format units to f64
        let loss_amount: f64 = format_units(loss_amount_u256, first_token_in_decimals)
            .context("Failed to format loss amount")?
            .parse()
            .context("Failed to parse loss amount as f64")?;

        let end_amount = start_amount - loss_amount;

        let end_percent = ((end_amount - start_amount) / start_amount) * 100.0;

        // Save the arb exec to csv
        if let Err(e) = log_arb_result_to_csv(ArbLogEntry {
            timestamp: chrono::Utc::now().to_string(),
            operation_id: operation_id.clone(),
            status: "FAILED".to_string(),
            start_amount,
            end_amount,
            estimated_out: amount_simul_out,
            cycle_path: formatted_cycle_path.join(" -> "),
            error_msg: event_details.event_data.clone(),
            estim_percent,
            pools_count: execution_route.len() as u64,
            end_percent,
        }) {
            error!("Failed to log arb result to csv: {}", e);
        }

        return Err(anyhow::anyhow!(
            "Failed to execute arbitrage. operation_id: {}, operation_status: {},  Cycle path: {:?}, simul amount out: {}, Last event data: {}",
            operation_id,
            operation_status,
            formatted_cycle_path,
            amount_simul_out,
            event_details.event_data,
        ));
    }

    let amount_out_str = event_details
        .event_data
        .split(":")
        .nth(1)
        .context("Failed to get amount out after splitting")?;

    let amount_out_u256: U256 = amount_out_str
        .parse()
        .context("Failed to parse amount out to U256")?;

    let token_out_decimals = cycle_path
        .last()
        .context("Failed to get last token from cycle path")?
        .decimals;

    let amount_out: f64 = format_units(amount_out_u256, token_out_decimals)
        .context("Failed to format amount out")?
        .parse()
        .context("Failed to parse amount out as f64")?;

    info!(
        "Arbitrage executed successfully with start amount {}. Operation id: {} Cycle path: {:?}",
        start_amount, operation_id, formatted_cycle_path,
    );

    let end_percent = ((amount_out - start_amount) / start_amount) * 100.0;

    // Save the arb exec to csv
    if let Err(e) = log_arb_result_to_csv(ArbLogEntry {
        timestamp: chrono::Utc::now().to_string(),
        operation_id: operation_id.clone(),
        status: "SUCCESS".to_string(),
        start_amount,
        end_amount: amount_out,
        estimated_out: amount_simul_out,
        cycle_path: formatted_cycle_path.join(" -> "),
        error_msg: String::new(),
        estim_percent,
        pools_count: execution_route.len() as u64,
        end_percent,
    }) {
        error!("Failed to log arb result to csv: {}", e);
    }

    Ok(operation_id)
}

pub async fn execute_arb_with_flash_loan(
    execution_route: &[ArbRoute],
    cycle_path: &Vec<Token>,
    grpc_client: &mut PublicGrpcClient,
    _token_matrix: &TokenMatrix,
    start_amount: f64,
    amount_simul_out: f64,
    estim_percent: f64,
    token_config: &TokenArbConfig,
) -> Result<String> {
    let is_arb_execute = CONFIG.is_arb_execute;

    if !is_arb_execute {
        info!("Arb execute is disabled. Skipping execution...");
        return Ok(String::new());
    }

    let formatted_cycle_path = cycle_path
        .iter()
        .map(|t| format!("{}-{:?}", t.symbol, t.dex))
        .collect::<Vec<String>>();

    // Execute the execution route of the first cycle path found
    info!(
        "Executing arbitrage with start amount {} for cycle path: {:?}...",
        start_amount, formatted_cycle_path
    );

    // Build the Flash Loan data object
    let flash_loan_data = FlashLoan {
        pool_address: token_config.flash_loan_pool_address.clone(),
        token_address: cycle_path[0].address.clone(),
        amount: execution_route[0].amount_in,
    };

    let args = Args::new()
        .add_serializable(&flash_loan_data)
        .add_serializable_object_array(&execution_route)
        .serialize();

    let expire_period = grpc_client
        .get_absolute_expire_period()
        .await
        .context("Failed to get absolute expire period in execute_arb")?;

    // Get the token_in decimals
    let first_token_in_decimals = cycle_path[0].decimals;

    let operation_id = grpc_client
        .call_sc(
            ARB_CONTRACT_ADDRESS,
            "executeWithFlashloan",
            args,
            "0.01",
            MAX_GAS_CALL,
            0.03,
            expire_period,
        )
        .await
        .context(format!(
            "Failed to call Execute With Flashloan on {}",
            ARB_CONTRACT_ADDRESS
        ))?;

    let operation_status = grpc_client
        .wait_for_operation(operation_id.clone(), true)
        .await
        .context("Failed to wait for operation")?;

    let operation_events = grpc_client
        .get_operation_events(&operation_id)
        .await
        .context("Failed to get operation events")?;

    // if the operation the last event will be the error event but if the operation succeeds it will contains the amout out
    let mut last_event = operation_events
        .last()
        .context("Failed to get last event")?;

    let mut event_details = extract_event_details(last_event);

    if operation_status != OperationExecutionStatus::Success as i32 {
        // debug!(
        //     "Showing all operation events data for operation id: {} ...",
        //     operation_id
        // );

        // for event in operation_events {
        //     let event_details = extract_event_details(&event);
        //     let event_data = event_details.event_data;

        //     debug!("Event Data: {:?}", &event_data);
        // }

        // Try to extract loss amount but don't propagate error if it fails
        let loss_amount_u256: U256 = event_details
            .event_data
            .split("LOSS_OF_FUNDS: ")
            .nth(1)
            .and_then(|err_part| err_part.split(" at ").next())
            .and_then(|err_part| err_part.parse().ok())
            .unwrap_or(U256::ZERO); // Default to 0.0 if parsing fails

        // format units to f64
        let loss_amount: f64 = format_units(loss_amount_u256, first_token_in_decimals)
            .context("Failed to format loss amount")?
            .parse()
            .context("Failed to parse loss amount as f64")?;

        let end_amount = start_amount - loss_amount;

        let end_percent = ((end_amount - start_amount) / start_amount) * 100.0;

        // Save the arb exec to csv
        if let Err(e) = log_arb_result_to_csv(ArbLogEntry {
            timestamp: chrono::Utc::now().to_string(),
            operation_id: operation_id.clone(),
            status: "FAILED".to_string(),
            start_amount,
            end_amount,
            estimated_out: amount_simul_out,
            cycle_path: formatted_cycle_path.join(" -> "),
            error_msg: event_details.event_data.clone(),
            estim_percent,
            pools_count: execution_route.len() as u64,
            end_percent,
        }) {
            error!("Failed to log arb result to csv: {}", e);
        }

        return Err(anyhow::anyhow!(
            "Failed to execute arbitrage. operation_id: {}, operation_status: {},  Cycle path: {:?}, simul amount out: {}, Last event data: {}",
            operation_id,
            operation_status,
            formatted_cycle_path,
            amount_simul_out,
            event_details.event_data,
        ));
    }

    debug!(
        "Showing all operation events data for operation id: {} ...",
        operation_id
    );

    for event in operation_events {
        let local_event_details = extract_event_details(&event);
        let event_data = &local_event_details.event_data;

        // Get the event that starts with "FLASH_ARB_EXEC"
        if event_data.starts_with("FLASH_ARB_EXEC") {
            last_event = &event;
            event_details = local_event_details.clone();
        }

        // debug!("Event Data: {:?}", event_data);
    }

    let amount_out_str = event_details
        .event_data
        .split(":")
        .nth(1)
        .context("Failed to get amount out after splitting")?;

    let amount_out_u256: U256 = amount_out_str
        .parse()
        .context("Failed to parse amount out to U256")?;

    let token_out_decimals = cycle_path
        .last()
        .context("Failed to get last token from cycle path")?
        .decimals;

    let amount_out: f64 = format_units(amount_out_u256, token_out_decimals)
        .context("Failed to format amount out")?
        .parse()
        .context("Failed to parse amount out as f64")?;

    info!(
        "Flash Loan Arbitrage executed successfully with start amount {}. Operation id: {} Cycle path: {:?}",
        start_amount, operation_id, formatted_cycle_path,
    );

    let end_percent = ((amount_out - start_amount) / start_amount) * 100.0;

    // Save the arb exec to csv
    if let Err(e) = log_arb_result_to_csv(ArbLogEntry {
        timestamp: chrono::Utc::now().to_string(),
        operation_id: operation_id.clone(),
        status: "SUCCESS".to_string(),
        start_amount,
        end_amount: amount_out,
        estimated_out: amount_simul_out,
        cycle_path: formatted_cycle_path.join(" -> "),
        error_msg: String::new(),
        estim_percent,
        pools_count: execution_route.len() as u64,
        end_percent,
    }) {
        error!("Failed to log arb result to csv: {}", e);
    }

    Ok(operation_id)
}
