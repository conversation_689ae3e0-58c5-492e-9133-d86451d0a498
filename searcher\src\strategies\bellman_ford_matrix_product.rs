use crate::types::{Pool, TokenMatrix};
use sprs::TriMat;
use std::collections::HashMap;

// Build the sparse matrix from the pools hash map
pub fn build_sparse_matrix(pools: &HashMap<String, Pool>) -> TokenMatrix {
    let mut matrix = TokenMatrix::default();

    // Step 1: Add each token to the matrix
    for pool in pools.values() {
        matrix.add_token(pool.a_token.clone());
        matrix.add_token(pool.b_token.clone());
    }

    // Step 2: Create the matrix's sparse matrix
    let n = matrix.token_indices.len();
    let mut triplet = TriMat::new((n, n));

    // Step 3: Populate matrix with log(exchange rates)
    for pool in pools.values() {
        let i = matrix.token_indices[&format!("{}-{:?}", pool.a_token.address, pool.a_token.dex)];
        let j = matrix.token_indices[&format!("{}-{:?}", pool.b_token.address, pool.b_token.dex)];

        let rate_a_to_b = pool.a_price;

        let rate_b_to_a = pool.b_price;

        triplet.add_triplet(i, j, rate_a_to_b);
        triplet.add_triplet(j, i, rate_b_to_a);
    }

    matrix.sparse_matrix = triplet;

    matrix
}

pub fn detect_negative_cycle(matrix: &TriMat<f64>, start: usize) -> Option<Vec<usize>> {
    let num_nodes = matrix.rows();
    let mut dist = vec![0.0; num_nodes];
    let mut prev = vec![None; num_nodes];

    dist[start] = 1.0;

    // Bellman-Ford: relax all edges n-1 times
    for _ in 0..(num_nodes - 1) {
        for (weight, (row_ind, col_ind)) in matrix.triplet_iter() {
            if dist[row_ind] * weight > dist[col_ind] {
                dist[col_ind] = dist[row_ind] * weight;
                prev[col_ind] = Some(row_ind);
            }
        }
    }

    // Check for positive product cycle
    for (weight, (row_ind, col_ind)) in matrix.triplet_iter() {
        if dist[row_ind] * weight > dist[col_ind] {
            // Arbitrage detected - reconstruct cycle
            let mut cycle = vec![col_ind];
            let mut current = row_ind;

            let mut visited = vec![false; num_nodes];
            while !visited[current] {
                visited[current] = true;
                cycle.push(current);
                if let Some(prev_node) = prev[current] {
                    current = prev_node;
                } else {
                    break;
                }
            }

            cycle.push(current);
            cycle.reverse();
            return Some(cycle);
        }
    }

    None
}

pub fn detect_all_negative_cycles(matrix: &TriMat<f64>, max_cycles: usize) -> Vec<Vec<usize>> {
    let num_nodes = matrix.rows();
    let mut cycles = Vec::new();

    for start in 0..num_nodes {
        if let Some(cycle) = detect_negative_cycle(matrix, start) {
            if !cycles.contains(&cycle) {
                cycles.push(cycle);
            }
        }

        if cycles.len() >= max_cycles {
            break;
        }
    }

    cycles
}
