use std::collections::HashSet;

use anyhow::{Context, Result};
use rust_massa_web3::{
    alloy_primitives::U256,
    basic_elements::{args::Args, serializers::bytes_to_string},
    client::grpc_client::PublicGrpcClient,
    constants::MAX_GAS_CALL,
    massa_proto_rs::massa::model::v1::OperationExecutionStatus,
};
use tracing::{debug, info};

use crate::{
    config::current::{
        ARB_CONTRACT_ADDRESS, DUSA_SWAP_ROUTER_ADDRESS, EAGLEFI_SWAP_ROUTER_ADDRESS,
    },
    helpers::massa::get_token_allowance,
    types::Token,
};

pub async fn increase_max_allowance_arb_to_router(
    router_address: &str,
    token_address: &str,
    grpc_client: &mut PublicGrpcClient,
) -> Result<String> {
    // Before increasing the allowance, check if the allowance is already set to the max
    let owner_address = ARB_CONTRACT_ADDRESS.to_string();
    let spender_address = router_address.to_string();

    let allowance =
        get_token_allowance(grpc_client, token_address, &owner_address, &spender_address).await?;

    debug!(
        "Allowance from {} to {} for token {} is {}",
        owner_address, spender_address, token_address, allowance
    );

    if allowance > (U256::MAX / U256::from(2)) {
        info!("Already max allowance for arb contract");
        return Ok(String::new());
    }

    info!(
        "Increasing allowance from {} to {} for token: {}",
        owner_address, spender_address, token_address
    );

    let args = Args::new()
        .add_string(token_address)
        .add_string(router_address)
        .serialize();

    let fee = "0.01";

    let expire_period = grpc_client
        .get_absolute_expire_period()
        .await
        .context("Failed to get absolute expire period")?;

    let operation_id = grpc_client
        .call_sc(
            ARB_CONTRACT_ADDRESS,
            "increaseMaxAllownaceForRoute",
            args,
            fee,
            MAX_GAS_CALL,
            0.02,
            expire_period,
        )
        .await
        .context("Failed to call increaseMaxAllownaceForRoute for arb contract")?;

    // wait for the operation to complete speculative
    let operation_status = grpc_client
        .wait_for_operation(operation_id.clone(), true)
        .await
        .context("Failed to wait for operation")?;

    if operation_status != OperationExecutionStatus::Success as i32 {
        // Get the last event of the operation
        let operation_events = grpc_client
            .get_operation_events(&operation_id)
            .await
            .context("Failed to get operation events")?;

        let last_event = operation_events.last().unwrap();

        // Get the error message from the last event
        let last_event_data = bytes_to_string(&last_event.data);

        return Err(anyhow::anyhow!(
            "Failed to increase max allowance for arb contract, error: {}",
            last_event_data
        ));
    }

    Ok(operation_id)
}

pub async fn increase_max_allowance_arb_for_all_tokens(
    grpc_client: &mut PublicGrpcClient,
    all_tokens: &HashSet<Token>,
) -> Result<()> {
    // Each token exists in two instances: one for EagleFi and one for Dusa. However, we only need to set the allowance once per token address.
    // We' ll processall tokens and skip the ones that have already been handled to avoid duplication.

    let mut handled_tokens: HashSet<String> = HashSet::new();

    for token in all_tokens {
        if !handled_tokens.contains(&token.address) {
            info!(
                "Starting to increase all allowance for token: {}...",
                token.symbol
            );
            let _ = increase_max_allowance_arb_to_router(
                &EAGLEFI_SWAP_ROUTER_ADDRESS,
                &token.address,
                grpc_client,
            )
            .await
            .context(format!(
                "Failed to increase max allowance for eaglefi router for token: {}",
                token.symbol
            ))?;

            let _ = increase_max_allowance_arb_to_router(
                &DUSA_SWAP_ROUTER_ADDRESS,
                &token.address,
                grpc_client,
            )
            .await
            .context(format!(
                "Failed to increase max allowance for dusa router for token: {}",
                token.symbol
            ))?;

            // Before increasing the allowance, check if the allowance is already set to the max
            let owner_address = grpc_client.get_current_address()?.to_string();
            let spender_address = ARB_CONTRACT_ADDRESS;
            let _allowance =
                get_token_allowance(grpc_client, &token.address, &owner_address, spender_address)
                    .await?;

            info!(
                "Increasing allowance from {} to {} for token: {}",
                owner_address, spender_address, token.symbol
            );

            let _ =
                increase_allownace(grpc_client, &token.address, ARB_CONTRACT_ADDRESS, U256::MAX)
                    .await
                    .context("Failed to increase allowance for arb contract")?;

            handled_tokens.insert(token.address.clone());

            info!(
                "Increased all needed allowance to use token: {}",
                token.symbol
            );
        }
    }

    Ok(())
}

pub async fn increase_allownace(
    grpc_client: &mut PublicGrpcClient,
    token_address: &str,
    spender_address: &str,
    amount: U256,
) -> Result<String> {
    let args = Args::new()
        .add_string(spender_address)
        .add_u256(amount)
        .serialize();

    let expire_period = grpc_client
        .get_absolute_expire_period()
        .await
        .context("Failed to get absolute expire period")?;

    let fee = "0.01";

    let increase_allowance_operation_id = grpc_client
        .call_sc(
            token_address,
            "increaseAllowance",
            args,
            fee,
            MAX_GAS_CALL,
            0.01,
            expire_period,
        )
        .await
        .context("Failed to call smart contract")?;

    // wait for the operation to complete speculative
    let increase_allowance_operation_status = grpc_client
        .wait_for_operation(increase_allowance_operation_id.clone(), true)
        .await
        .context("Failed to wait for operation")?;

    if increase_allowance_operation_status != OperationExecutionStatus::Success as i32 {
        return Err(anyhow::anyhow!(
            "Failed to increase allowance for arb contract"
        ));
    }

    Ok(increase_allowance_operation_id)
}
