use anyhow::Result;
use petgraph::{algo::find_negative_cycle, prelude::*};
use std::collections::HashMap;
use tracing::warn;

use crate::types::{LnEpsilon, Pool, Token, TokenGraph};

pub fn build_petgraph(
    pools: &HashMap<String, Pool>,
) -> (DiGraph<Token, f64>, HashMap<Token, NodeIndex>) {
    let mut graph = DiGraph::<Token, f64>::new();

    // Create a map to store the indices of the tokens
    let mut token_indices: HashMap<Token, NodeIndex> = HashMap::new();

    // Add nodes to the graph for each token
    for pool in pools.values() {
        let a_token = &pool.a_token;
        let b_token = &pool.b_token;

        // Add the tokens to the graph if they are not already present
        let a_token_index = match token_indices.contains_key(a_token) {
            true => *token_indices.get(a_token).expect("Token index not found"),
            false => graph.add_node(a_token.clone()),
        };

        let b_token_index = match token_indices.contains_key(b_token) {
            true => *token_indices.get(b_token).expect("Token index not found"),
            false => graph.add_node(b_token.clone()),
        };

        // Calculate the weights for the edges

        let a_weight = -pool.a_price.ln_epsilon();
        let b_weight = -pool.b_price.ln_epsilon();

        // Add edges between the nodes
        graph.add_edge(a_token_index, b_token_index, a_weight);
        graph.add_edge(b_token_index, a_token_index, b_weight);

        // Add the token indices to the map
        token_indices.insert(a_token.clone(), a_token_index);
        token_indices.insert(b_token.clone(), b_token_index);
    }

    // Display edges
    tracing::info!(
        "Graph edges: {:?}",
        graph.edge_references().collect::<Vec<_>>()
    );

    // Add INFINITY in remaining edges
    for i in 0..graph.node_count() {
        for j in 0..graph.node_count() {
            let node_i = NodeIndex::new(i);
            let node_j = NodeIndex::new(j);

            // If there is no edge between node_i and node_j, add an edge with weight INFINITY
            if !graph.contains_edge(node_i, node_j) && i != j {
                graph.add_edge(node_i, node_j, f64::INFINITY);
            }
        }
    }

    // Display edges
    tracing::info!(
        "Graph edges with INFINITY: {:?}",
        graph.edge_references().collect::<Vec<_>>()
    );

    // Return the graph and the token indices
    (graph, token_indices)
}

pub fn detect_arb_bellman_ford_cycle(token_graph: &TokenGraph) -> Result<Vec<Vec<usize>>> {
    let graph = &token_graph.graph;

    let mut all_path_cycles = Vec::new();

    // Detect all negative cycles in the graph from all start nodes
    for start_idx in 0..graph.node_count() {
        if let Some(cycle_path) = find_negative_cycle(graph, NodeIndex::new(start_idx)) {
            // Convert NodeIndex to usize
            let cycle_usize: Vec<usize> = cycle_path.iter().map(|&idx| idx.index()).collect();
            all_path_cycles.push(cycle_usize);
        }
    }

    // If no cycles were found, return an empty Vec
    if all_path_cycles.is_empty() {
        warn!("No negative cycles found in the graph.");
        return Ok(vec![]);
    }

    Ok(all_path_cycles)
}
