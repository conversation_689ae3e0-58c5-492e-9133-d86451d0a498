use std::collections::HashMap;

use anyhow::{Ok, Result};
use tracing::debug;

use crate::types::{Pool, Token, TokenMatrix, DEX};

pub fn print_arbitrage_cycle_with_symbols(
    cycle: &[usize],
    graph: &TokenMatrix,
    pools: &HashMap<String, Pool>,
) {
    println!("╔═══════════════════════════════════════════════════════════");
    println!("║ Arbitrage Path:");

    for (i, &idx) in cycle.iter().enumerate() {
        let token_key = &graph.index_tokens[idx];
        let parts: Vec<&str> = token_key.split('-').collect();
        let address = parts[0];
        let dex_str = parts[1];

        // Find token symbol from pools
        let symbol = find_token_symbol(address, dex_str, pools);

        if i < cycle.len() - 1 {
            // i:2 means i is 2 characters wide, right aligned
            println!("║ {i:2}. {symbol} ({address}) on {dex_str} ↓");
        } else {
            println!("║ {i:2}. {symbol} ({address}) on {dex_str}");
        }
    }

    println!("╚═══════════════════════════════════════════════════════════");
}

pub fn find_token_symbol(address: &str, dex_str: &str, pools: &HashMap<String, Pool>) -> String {
    // Convert dex_str to DEX enum
    let dex = match dex_str {
        "EagleFi" => DEX::EagleFi,
        "Dusa" => DEX::Dusa,
        _ => DEX::Both,
    };

    // Look through all pools to find the token
    for pool in pools.values() {
        if pool.a_token.address == address && pool.a_token.dex == dex {
            return pool.a_token.symbol.clone();
        }
        if pool.b_token.address == address && pool.b_token.dex == dex {
            return pool.b_token.symbol.clone();
        }
    }

    // If not found, return a shortened address
    if address.len() > 8 {
        format!("{}...", &address[0..8])
    } else {
        address.to_string()
    }
}

pub fn find_token(
    address: &str,
    dex_str: &str,
    all_eaglefi_tokens: &HashMap<String, Token>,
    all_dusa_tokens: &HashMap<String, Token>,
) -> Result<Token> {
    // Convert dex_str to DEX enum
    let dex = match dex_str {
        "EagleFi" => DEX::EagleFi,
        "Dusa" => DEX::Dusa,
        _ => DEX::Both,
    };

    // Look through all tokens to find the token based on the dex 
    if dex == DEX::EagleFi {
        for token in all_eaglefi_tokens.values() {
            if token.address == address {
                return Ok(token.clone());
            }
        }
    } else if dex == DEX::Dusa {
        for token in all_dusa_tokens.values() {
            if token.address == address {
                return Ok(token.clone());
            }
        }
    }

    Err(anyhow::format_err!("Token not found"))
}

pub fn print_cycle_path_symbols(cycle_path: &[Token]) {
    // print it as an arry of token addresses
    let mut tokens = Vec::new();
    for token in cycle_path {
        tokens.push(format!("{}-{:?}", token.symbol, token.dex));
    }

    debug!("Path: {:?}", tokens);
}
// matrix: &CsMat<f64>, node_index_map: &HashMap<String, usize>
pub fn print_sparse_matrix(token_matrix: &TokenMatrix) {
    let matrix_trimat = &token_matrix.sparse_matrix;
    // use to csr matrix
    let matrix = matrix_trimat.to_csr::<usize>();
    let node_index_map = &token_matrix.token_indices;

    // Reverse map to print names
    println!(
        "*********************************************Sparse Matrix:*************************************************"
    );
    let index_to_token: Vec<_> = {
        let mut v = vec!["".to_string(); node_index_map.len()];
        for (token, &i) in node_index_map {
            v[i] = token.clone();
        }
        v
    };

    for (row, vec) in matrix.outer_iterator().enumerate() {
        let from_token = &index_to_token[row];
        for (col, &weight) in vec.iter() {
            let to_token = &index_to_token[col];
            println!(
                "{} -> {} | weight: {} (rate: {})",
                from_token,
                to_token,
                weight,
                (-weight).exp()
            );
        }
    }
}

pub fn print_tokens_indices_from_matrix(
    token_matrix: &TokenMatrix,
    all_dusa_tokens: &HashMap<String, Token>,
    all_eaglefi_tokens: &HashMap<String, Token>,
) {
    for (token_key, index) in token_matrix.token_indices.iter() {
        let parts: Vec<&str> = token_key.split('-').collect();
        let address = parts[0];
        let dex_str = parts[1];
        let token: &Token;

        if dex_str == "EagleFi" {
            token = all_eaglefi_tokens.get(address).expect("Token not found");
        } else if dex_str == "Dusa" {
            token = all_dusa_tokens.get(address).expect("Token not found");
        } else {
            continue;
        }

        println!(
            "{} -> {}-{} -> {}",
            token.address, token.symbol, dex_str, index
        );
    }
}
