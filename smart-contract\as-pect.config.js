import createMockedABI from '@massalabs/massa-as-sdk/vm-mock';

export default {
  /**
   * A set of globs passed to the glob package that qualify typescript files for testing.
   */
  entries: ['assembly/__tests__/**/*.spec.ts'],
  /**
   * A set of globs passed to the glob package that quality files to be added to each test.
   */
  include: ['assembly/__tests__/**/*.include.ts'],
  /**
   * A set of regexp that will disclude source files from testing.
   */
  disclude: [/node_modules/],
  /**
   * Add your required AssemblyScript imports here.
   */
  async instantiate(memory, createImports, instantiate, binary) {
    return createMockedABI(memory, createImports, instantiate, binary);
  },
  /** Enable code coverage. */
  // coverage: ["assembly/**/*.ts"],
  /**
   * Specify if the binary wasm file should be written to the file system.
   */
  outputBinary: false,
};
