use std::{collections::HashMap, sync::Arc};

use anyhow::{Context, Result};
use sprs::{CsMat, TriMat};
use tracing::{debug, info, warn};

use crate::{
    config,
    state::SharedPool,
    types::{LnEpsilon, Pool, TokenMatrix},
};

const EPSILON: f64 = 1e-6;
const MARGIN: f64 = config::current::MARGIN  / 100.0;
// const MARGIN: f64 = 0.0;
use std::f64::INFINITY;

// Build the sparse graph from the pools hash map
pub async fn build_sparse_matrix(pools: &HashMap<String, SharedPool>) -> TokenMatrix {
    let mut matrix = TokenMatrix::default();

    // Step 1: Add each token to the matrix
    for pool in pools.values() {
        let pool = pool.lock().await;

        matrix.add_token(pool.a_token.clone());
        matrix.add_token(pool.b_token.clone());

        drop(pool);
    }

    // Step 2: Create the matrix's sparse matrix
    let n = matrix.token_indices.len();

    // Initialize a dense matrix with INFINITY values
    // This will be used to create the sparse matrix later
    let mut dense = vec![vec![INFINITY; n]; n];
    for pool in pools.values() {
        let pool = pool.lock().await;

        let i = matrix.token_indices[&format!("{}-{:?}", pool.a_token.address, pool.a_token.dex)];
        let j = matrix.token_indices[&format!("{}-{:?}", pool.b_token.address, pool.b_token.dex)];

        let ln_rate_a_to_b = -pool.a_price.ln_epsilon();
        let ln_rate_b_to_a = -pool.b_price.ln_epsilon();

        dense[i][j] = ln_rate_a_to_b;
        dense[j][i] = ln_rate_b_to_a;

        drop(pool);
    }

    let mut triplet = TriMat::new((n, n));

    for i in 0..n {
        for j in 0..n {
            triplet.add_triplet(i, j, dense[i][j]);
        }
    }

    matrix.sparse_matrix = triplet;

    matrix
}

pub async fn update_sparse_matrix_by_pool(mut matrix: TokenMatrix, pool: &Pool) -> TokenMatrix {
    info!("Updating sparse matrix by pool: {:?}", pool);

    // Get indices for the tokens in this pool
    let i = matrix.token_indices[&format!("{}-{:?}", pool.a_token.address, pool.a_token.dex)];
    let j = matrix.token_indices[&format!("{}-{:?}", pool.b_token.address, pool.b_token.dex)];

    // Calculate new log exchange rates
    let ln_rate_a_to_b = -pool.a_price.ln_epsilon();
    let ln_rate_b_to_a = -pool.b_price.ln_epsilon();

    let sparse_matrix = &mut matrix.sparse_matrix;

    // Find the position of the element at coordinates (i,j) in the triplet storage
    // This returns the index in the internal storage array where this element is stored
    let locations_a_to_b = sparse_matrix.find_locations(i, j);

    sparse_matrix.set_triplet(locations_a_to_b[0], i, j, ln_rate_a_to_b);

    // Find the position of the element at coordinates (j,i) in the triplet storage
    let locations_b_to_a = sparse_matrix.find_locations(j, i);

    sparse_matrix.set_triplet(locations_b_to_a[0], j, i, ln_rate_b_to_a);

    matrix
}

// Detect negative cycle in the sparse matrix
pub fn detect_negative_cycle(matrix: &CsMat<f64>, start_idx: usize) -> Result<Option<Vec<usize>>> {
    let n = matrix.rows();
    let mut dist = vec![f64::INFINITY; n];
    let mut predecessor = vec![None; n];
    dist[start_idx] = 0.0; // start from the start_idx node

    // Bellman-Ford V-1 relaxations
    for _ in 0..n - 1 {
        for (&weight, (i, j)) in matrix.iter() {
            if dist[i] != INFINITY && dist[i] + weight < dist[j] - EPSILON {
                dist[j] = dist[i] + weight;
                predecessor[j] = Some(i);
            }
        }
    }

    if dist[start_idx] < -MARGIN {
        // Follow the predecessor pointers n times to ensure we're in the cycle.
        let mut current = start_idx;
        for _ in 0..n {
            current = predecessor[current].context("No predecessor")?;
        }
        let cycle_start = current;
        let mut cycle = vec![cycle_start];
        current = predecessor[cycle_start].context("No predecessor")?;
        while !cycle.contains(&current) {
            cycle.push(current);
            current = predecessor[current].context("No predecessor")?;
        }

        cycle.push(cycle_start); // Close the cycle.
        cycle.reverse();
        // debug!("Detected negative cycle: {:?}", cycle);
        return Ok(Some(cycle));
    }

    Ok(None)
}

// Detect all negative cycles from all start nodes
pub fn detect_all_negative_cycles_from_all_start_nodes(
    matrix: &CsMat<f64>,
    max_cycles: usize,
) -> Result<Vec<Vec<usize>>> {
    let mut all_cycles = Vec::new();

    // Try all nodes as starting points
    for start_idx in 0..matrix.rows() {
        if let Some(cycle) = detect_negative_cycle(matrix, start_idx)? {
            if !all_cycles.contains(&cycle) {
                if cycle[0] != cycle[cycle.len() - 1] {
                    warn!(
                        "Ignoring cycle with a cycle starting and ending at different nodes. Cycle: {:?}",
                        cycle
                    );
                    continue;
                }
                all_cycles.push(cycle);
            }
        }

        if all_cycles.len() >= max_cycles {
            // Break if we have found enough cycles
            break;
        }
    }

    // Do the same but with cols instead of rows if we didn't find enough cycles
    if all_cycles.len() < max_cycles {
        for idx in 0..matrix.cols() {
            if let Some(cycle) = detect_negative_cycle(matrix, idx)? {
                if !all_cycles.contains(&cycle) {
                    if cycle[0] != cycle[cycle.len() - 1] {
                        warn!(
                            "Ignoring cycle with a cycle starting and ending at different nodes. Cycle: {:?}",
                            cycle
                        );
                        continue;
                    }
                    all_cycles.push(cycle);
                }
            }

            if all_cycles.len() >= max_cycles {
                // Break if we have found enough cycles
                break;
            }
        }
    }
    // debug!("All negative cycles: {:?}", all_cycles);

    // OPT: When you can have multiple cycles of same size, eather select the best estimation or the one that start with our token.
    // We need to take min size cycles because you're going back and forth in longer cycles.
    // Sort the cycles by shortest length to highest length
    all_cycles.sort_by(|a, b| a.len().cmp(&b.len()));

    // debug!("All negative cycles sorted: {:?}", all_cycles);

    Ok(all_cycles)
}

// Detect negative cycle in the sparse matrix from each token index
// This version allows us to start from a specific index but can have longer cycles!
pub fn detect_negative_cycle_from_each(
    matrix: &CsMat<f64>,
    start_idx: usize,
) -> Result<Option<Vec<usize>>> {
    let n = matrix.rows();
    let mut dist = vec![f64::INFINITY; n];
    let mut predecessor = vec![None; n];
    dist[start_idx] = 0.0; // start from the start_idx node

    // Bellman-Ford V-1 relaxations
    // OPT: Improvments we can make: We no changes in two consecutive iterations, we can stop early.
    // This is because if we don't change the distance in an iteration, it means we have reached a stable state.
    // This is a common optimization in the Bellman-Ford algorithm.
    // This optimization can significantly reduce the number of iterations in practice, especially for sparse graphs.
    for _ in 0..n - 1 {
        for (&weight, (i, j)) in matrix.iter() {
            // We check if the current estimated distance to `i` is finite (meaning `i` is considered reachable)
            if dist[i] != INFINITY && dist[i] + weight < dist[j] - EPSILON {
                dist[j] = dist[i] + weight;
                // On ne peut sauver qu'un predecesseur c'est pourquoi qu'un cycle.
                predecessor[j] = Some(i);
            }
        }
    }

    // We check if the distance to the start index is negative, which indicates a cycle.
    // Even though if we take the shortest cycle we are good.
    // OPT: We can get the amounts in wallet and send tokens to increase input amount.
    // OPT: Check if we merge better with WMAS (more edges).
    if dist[start_idx] < -MARGIN {
        let mut current = start_idx;
        let cycle_start = current;
        let mut cycle = vec![cycle_start];
        current = predecessor[cycle_start].context("No predecessor")?;
        while !cycle.contains(&current) {
            cycle.push(current);
            current = predecessor[current].context("No predecessor")?;
        }
        cycle.push(current);
        let mut i = 0;
        while cycle[i] != current {
            i = i + 1;
        }
        while i > 0 {
            cycle.push(cycle[i]);
            i = i - 1;
        }
        cycle.push(cycle_start);
        if cycle[cycle.len() - 1] == cycle[cycle.len() - 2] {
            cycle.pop();
        }

        cycle.reverse();
        debug!("Detected negative cycle: {:?}", cycle);
        return Ok(Some(cycle));
    }

    Ok(None)
}

#[cfg(test)]
mod tests {

    use super::*;

    #[test]
    fn test_update_triplet() {
        let mut triplet_mat = TriMat::with_capacity((4, 4), 6);

        triplet_mat.add_triplet(0, 0, 1.);
        triplet_mat.add_triplet(0, 1, 2.);
        triplet_mat.add_triplet(1, 0, 3.);
        triplet_mat.add_triplet(2, 3, 4.);
        triplet_mat.add_triplet(3, 2, 5.);
        triplet_mat.add_triplet(3, 3, 6.);

        // update_triplet with row = 0, col = 1, value = 10.;
        let locations = triplet_mat.find_locations(0, 1);
        assert_eq!(locations.len(), 1);

        triplet_mat.set_triplet(locations[0], 0, 1, 10.);

        let mut expected = TriMat::with_capacity((4, 4), 6);

        expected.add_triplet(0, 0, 1.);
        expected.add_triplet(0, 1, 10.);
        expected.add_triplet(1, 0, 3.);
        expected.add_triplet(2, 3, 4.);
        expected.add_triplet(3, 2, 5.);
        expected.add_triplet(3, 3, 6.);

        assert_eq!(triplet_mat, expected);
    }
}
