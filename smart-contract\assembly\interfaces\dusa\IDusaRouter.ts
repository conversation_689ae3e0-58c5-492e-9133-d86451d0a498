import { Args, bytesToString, bytesToU256 } from '@massalabs/as-types';
import { Address, call } from '@massalabs/massa-as-sdk';
import { u256 } from 'as-bignum/assembly/integer/u256';
import { IMRC20 } from '../IMRC20';

export class IDusaRouter {
  _origin: Address;

  /**
   * Wraps a smart contract exposing standard token FFI.
   *
   * @param {Address} at - Address of the smart contract.
   */
  constructor(at: Address) {
    this._origin = at;
  }

  /**
   * Swaps exact tokens for tokens while performing safety checks
   *
   * @param {u256} amountIn - The amount of tokens to send
   * @param {u256} amountOutMin - The min amount of tokens to receive
   * @param {Array<u64>} pairBinSteps - The bin step of the pairs
   * @param {IMRC20[]} tokenPath - The swap path using the binSteps following `_pairBinSteps`
   * @param {Address} to - The address of the recipient
   * @param {u64} deadline - The deadline of the tx
   * @param {u64} masToSend - The amount of Massa to send for storage
   * @return {u256} - The output amount of the swap
   */
  swapExactTokensForTokens(
    amountIn: u256,
    amountOutMin: u256,
    pairBinSteps: Array<u64>,
    tokenPath: IMRC20[],
    to: Address,
    deadline: u64,
    masToSend: u64,
  ): u256 {
    const args = new Args()
      .add(amountIn)
      .add(amountOutMin)
      .add(pairBinSteps)
      .addSerializableObjectArray(tokenPath)
      .add(to)
      .add(deadline);
    const res = call(this._origin, 'swapExactTokensForTokens', args, masToSend);
    return bytesToU256(res);
  }

  /**
   * Swaps exact tokens for MAS while performing safety checks
   *
   * @param {u256} amountIn - The amount of tokens to send
   * @param {u256} amountOutMinMAS - The min amount of MAS to receive
   * @param {Array<u64>} pairBinSteps - The bin step of the pairs
   * @param {IMRC20[]} tokenPath - The swap path using the binSteps following `_pairBinSteps`
   * @param {Address} to - The address of the recipient
   * @param {u64} deadline - The deadline of the tx
   * @param {u64} masToSend - The amount of Massa to send for storage
   * @return {u256} - The output amount of the swap
   */
  swapExactTokensForMAS(
    amountIn: u256,
    amountOutMinMAS: u256,
    pairBinSteps: Array<u64>,
    tokenPath: IMRC20[],
    to: Address,
    deadline: u64,
    masToSend: u64,
  ): u256 {
    const args = new Args()
      .add(amountIn)
      .add(amountOutMinMAS)
      .add(pairBinSteps)
      .addSerializableObjectArray(tokenPath)
      .add(to)
      .add(deadline);
    const res = call(this._origin, 'swapExactTokensForMAS', args, masToSend);
    return bytesToU256(res);
  }

  /**
   * Swaps exact MAS for tokens while performing safety checks
   *
   * @param {u256} amountIn - The amount of MAS to send for swap and storage
   * @param {u256} amountOutMin - The min amount of token to receive
   * @param {Array<u64>} pairBinSteps - The bin step of the pairs
   * @param {IMRC20[]} tokenPath - The swap path using the binSteps following `_pairBinSteps`
   * @param {Address} to - The address of the recipient
   * @param {u64} deadline - The deadline of the tx
   * @param {u64} masToSend - The amount of Massa to send for storage
   * @return {u256} - The output amount of the swap
   */
  swapExactMASForTokens(
    amountIn: u256,
    amountOutMin: u256,
    pairBinSteps: Array<u64>,
    tokenPath: IMRC20[],
    to: Address,
    deadline: u64,
    masToSend: u64,
  ): u256 {
    const args = new Args()
      .add(amountOutMin)
      .add(pairBinSteps)
      .addSerializableObjectArray(tokenPath)
      .add(to)
      .add(deadline)
      .add(masToSend);
    const res = call(
      this._origin,
      'swapExactMASForTokens',
      args,
      amountIn.toU64(),
    );
    return bytesToU256(res);
  }

  /**
   * Swaps tokens for exact tokens while performing safety checks
   *
   * @param {u256} amountOut - The amount of token to receive
   * @param {u256} amountInMax - The max amount of token to send
   * @param {Array<u64>} pairBinSteps - The bin step of the pairs
   * @param {IMRC20[]} tokenPath - The swap path using the binSteps following `_pairBinSteps`
   * @param {Address} to - The address of the recipient
   * @param {u64} deadline - The deadline of the tx
   * @param {u64} masToSend - The amount of Massa to send for storage
   * @return {u256} - The output amount of the swap
   */
  swapTokensForExactTokens(
    amountOut: u256,
    amountInMax: u256,
    pairBinSteps: Array<u64>,
    tokenPath: IMRC20[],
    to: Address,
    deadline: u64,
    masToSend: u64,
  ): u256 {
    const args = new Args()
      .add(amountOut)
      .add(amountInMax)
      .add(pairBinSteps)
      .addSerializableObjectArray(tokenPath)
      .add(to)
      .add(deadline);
    const res = call(this._origin, 'swapTokensForExactTokens', args, masToSend);
    return bytesToU256(res);
  }

  /**
   * Swaps tokens for exact MAS while performing safety checks
   *
   * @param {u256} amountOut - The amount of MAS to receive
   * @param {u256} amountInMax - The max amount of token to send
   * @param {Array<u64>} pairBinSteps - The bin step of the pairs
   * @param {IMRC20[]} tokenPath - The swap path using the binSteps following `_pairBinSteps`
   * @param {Address} to - The address of the recipient
   * @param {u64} deadline - The deadline of the tx
   * @param {u64} masToSend - The amount of Massa to send for storage
   * @return {u256} - The output amount of the swap
   */
  swapTokensForExactMAS(
    amountOut: u256,
    amountInMax: u256,
    pairBinSteps: Array<u64>,
    tokenPath: IMRC20[],
    to: Address,
    deadline: u64,
    masToSend: u64,
  ): u256 {
    const args = new Args()
      .add(amountOut)
      .add(amountInMax)
      .add(pairBinSteps)
      .addSerializableObjectArray(tokenPath)
      .add(to)
      .add(deadline);
    const res = call(this._origin, 'swapTokensForExactMAS', args, masToSend);
    return bytesToU256(res);
  }

  /**
   * Swaps MAS for exact tokens while performing safety checks
   *
   * @param {u256} amountOut - The amount of token to receive
   * @param {u256} amountInMax - The max amount of Mas to send
   * @param {Array<u64>} pairBinSteps - The bin step of the pairs
   * @param {IMRC20[]} tokenPath - The swap path using the binSteps following `_pairBinSteps`
   * @param {Address} to - The address of the recipient
   * @param {u64} deadline - The deadline of the tx
   * @param {u64} masToSend - The amount of Massa to send for storage
   * @return {u256} - The output amount of the swap
   */
  swapMASForExactTokens(
    amountOut: u256,
    amountInMax: u256,
    pairBinSteps: Array<u64>,
    tokenPath: IMRC20[],
    to: Address,
    deadline: u64,
    masToSend: u64,
  ): u256 {
    const args = new Args()
      .add(amountOut)
      .add(pairBinSteps)
      .addSerializableObjectArray(tokenPath)
      .add(to)
      .add(deadline)
      .add(masToSend);
    const res = call(
      this._origin,
      'swapMASForExactTokens',
      args,
      amountInMax.toU64(),
    );
    return bytesToU256(res);
  }

  /**
   * Swaps exact tokens for tokens while performing safety checks supporting for fee on transfer tokens
   *
   * @param {u256} amountIn - The amount of token to send
   * @param {u256} amountOutMin - The min amount of token to receive
   * @param {Array<u64>} pairBinSteps - The bin step of the pairs
   * @param {IMRC20[]} tokenPath - The swap path using the binSteps following `_pairBinSteps`
   * @param {Address} to - The address of the recipient
   * @param {u64} deadline - The deadline of the tx
   * @param {u64} masToSend - The amount of Massa to send for storage
   * @return {u256} - The output amount of the swap
   */
  swapExactTokensForTokensSupportingFeeOnTransferTokens(
    amountIn: u256,
    amountOutMin: u256,
    pairBinSteps: Array<u64>,
    tokenPath: IMRC20[],
    to: Address,
    deadline: u64,
    masToSend: u64,
  ): u256 {
    const args = new Args()
      .add(amountIn)
      .add(amountOutMin)
      .add(pairBinSteps)
      .addSerializableObjectArray(tokenPath)
      .add(to)
      .add(deadline);
    const res = call(
      this._origin,
      'swapExactTokensForTokensSupportingFeeOnTransferTokens',
      args,
      masToSend,
    );
    return bytesToU256(res);
  }

  /**
   * Swaps exact tokens for MAS while performing safety checks supporting for fee on transfer tokens
   *
   * @param {u256} amountIn - The amount of token to send
   * @param {u256} amountOutMinMAS - The min amount of MAS to receive
   * @param {Array<u64>} pairBinSteps - The bin step of the pairs
   * @param {IMRC20[]} tokenPath - The swap path using the binSteps following `_pairBinSteps`
   * @param {Address} to - The address of the recipient
   * @param {u64} deadline - The deadline of the tx
   * @param {u64} masToSend - The amount of Massa to send for storage
   * @return {u256} - The output amount of the swap
   */
  swapExactTokensForMASSupportingFeeOnTransferTokens(
    amountIn: u256,
    amountOutMinMAS: u256,
    pairBinSteps: Array<u64>,
    tokenPath: IMRC20[],
    to: Address,
    deadline: u64,
    masToSend: u64,
  ): u256 {
    const args = new Args()
      .add(amountIn)
      .add(amountOutMinMAS)
      .add(pairBinSteps)
      .addSerializableObjectArray(tokenPath)
      .add(to)
      .add(deadline);
    const res = call(
      this._origin,
      'swapExactTokensForMASSupportingFeeOnTransferTokens',
      args,
      masToSend,
    );
    return bytesToU256(res);
  }
}
