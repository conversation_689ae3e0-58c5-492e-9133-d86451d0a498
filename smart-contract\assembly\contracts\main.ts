// The entry file of your WebAssembly module.
import {
  Address,
  assertIsSmartContract,
  call,
  Context,
  generateEvent,
  Storage,
  transferCoins,
} from '@massalabs/massa-as-sdk';
import { Args, stringToBytes, u256ToBytes } from '@massalabs/as-types';
import { ArbRoute } from '../structs/arbRoute';
import { IMRC20 } from '../interfaces/IMRC20';
import { getBalanceEntryCost } from '@massalabs/sc-standards/assembly/contracts/MRC20/MRC20-external';
import { IDusaRouter } from '../interfaces/dusa/IDusaRouter';
import { IEaglefiRouter } from '../interfaces/eaglefi/ISwapRouter';
import { SafeMath256 } from '../lib/safeMath';
import { SwapPath } from '../structs/eaglefi/swapPath';
import { u256 } from 'as-bignum/assembly';
import { FlashLoan } from '../structs/flashLoan';
import { IEaglePool } from '../interfaces/eaglefi/IEaglePool';
import { getFeeFromAmount } from '../lib/eaglePoolMath';

export const ALLOWANCE_KEY_PREFIX = 'ALLOWANCE';
export const BENEFICIARY_ADDRESS_KEY = 'BENEFICIARY_ADDRESS_KEY';

/**
 * This function is meant to be called only one time: when the contract is deployed.
 *
 * @param binaryArgs - Arguments serialized with Args
 */
export function constructor(_: StaticArray<u8>): void {
  // This line is important. It ensures that this function can't be called in the future.
  // If you remove this check, someone could call your constructor function and reset your smart contract.
  assert(Context.isDeployingContract());

  Storage.set(
    BENEFICIARY_ADDRESS_KEY,
    'AU1Ru6cfN65sUaWycZaerWcUUjRMakgLLuFiWJeTVUAEm9knwpB7',
  );

  generateEvent('Constructor called');
}

export function receiveCoins(_: StaticArray<u8>): void {
  generateEvent(`Received coins`);
}

export function withdraw(binaryArgs: StaticArray<u8>): void {
  const args = new Args(binaryArgs);

  const token = args.nextString().expect('Token is missing');
  const amount = args.nextU256().expect('Amount is missing');
  const to = args.nextString().expect('To is missing');

  const tokenContract = new IMRC20(new Address(token));

  tokenContract.transfer(
    new Address(to),
    amount,
    getBalanceEntryCost(token, to),
  );

  generateEvent(`Withdrawed ${amount.toString()} of ${token}`);
}

export function withdrawMas(binaryArgs: StaticArray<u8>): void {
  const args = new Args(binaryArgs);

  const amount = args.nextU256().expect('Amount is missing');
  const to = args.nextString().expect('To is missing');

  transferCoins(new Address(to), amount.toU64());

  generateEvent(`Withdrawed ${amount.toString()} of Native MAS TO ${to}`);
}

export function SetBeneficiaryAddress(binaryArgs: StaticArray<u8>): void {
  const args = new Args(binaryArgs);

  const beneficiaryAddress = args
    .nextString()
    .expect('Beneficiary address is missing');

  Storage.set(BENEFICIARY_ADDRESS_KEY, beneficiaryAddress);

  generateEvent(`Set beneficiary address to ${beneficiaryAddress}`);
}

/**
 * This function will execute the arbitrage routes.
 * @param binaryArgs  - Arguments serialized with Args
 * @returns void
 */
export function execute(binaryArgs: StaticArray<u8>): StaticArray<u8> {
  const args = new Args(binaryArgs);

  const arbsRoutes = args
    .nextSerializableObjectArray<ArbRoute>()
    .expect('Invalid arb routes');

  const caller = Context.caller();
  const callee = Context.callee();

  // First route need to use transferfrom tokenin amount from caller to current contract
  // Transfer tokenIn amount from caller to current contract
  const firstRoute = arbsRoutes[0];
  const lastRoute = arbsRoutes[arbsRoutes.length - 1];

  const tokenInAddress = firstRoute.tokenInAddress;
  const tokenIn = new IMRC20(tokenInAddress);

  // Assert that the tokenIn address of the first route is the same as the tokenoUT address of the last route
  assert(
    tokenInAddress == lastRoute.tokenOutAddress,
    'INVALID_ARB_ROUTES: TOKEN_IN != LAST_TOKEN_OUT',
  );

  tokenIn.transferFrom(
    caller,
    callee,
    firstRoute.amountIn,
    getBalanceEntryCost(
      firstRoute.tokenInAddress.toString(),
      callee.toString(),
    ),
  );

  // Get the balance of the current contract of token in
  const tokenInBalanceBefore = tokenIn.balanceOf(callee);

  let lastAmountOut = u256.Zero;

  // Print all the routes using generateEvent
  for (let i = 0; i < arbsRoutes.length; i++) {
    const route = arbsRoutes[i];

    let amountOut: u256 = u256.Zero;

    if (route.isDusa) {
      const dusaRouter = new IDusaRouter(route.routerAddress);

      let tokenPath: IMRC20[] = [];

      tokenPath.push(new IMRC20(route.tokenInAddress));
      tokenPath.push(new IMRC20(route.tokenOutAddress));

      amountOut = dusaRouter.swapExactTokensForTokens(
        route.amountIn,
        route.amountOutMin,
        [route.binStep],
        tokenPath,
        route.to,
        route.deadline,
        route.coinsToUse,
      );
    } else {
      const eaglefiRouter = new IEaglefiRouter(route.routerAddress);

      const swapPath = new SwapPath(
        route.poolAddress,
        route.tokenInAddress,
        route.tokenOutAddress,
        route.to,
        route.amountIn,
        route.amountOutMin,
        true,
      );

      amountOut = eaglefiRouter.swap(
        [swapPath],
        route.coinsToUse,
        route.deadline,
        route.coinsToUse,
      );
    }

    lastAmountOut = amountOut;
    // Update the amountIn of the next route if this route is not the last one
    if (i < arbsRoutes.length - 1) {
      arbsRoutes[i + 1].amountIn = amountOut;
    }
  }

  // Blance of tokenIn after should be greater than balance before
  const tokenInBalanceAfter = tokenIn.balanceOf(callee);

  // Assert that the balance of tokenIn after is greater than balance before
  assert(
    tokenInBalanceAfter > tokenInBalanceBefore,
    `LOSS_OF_FUNDS: ${SafeMath256.sub(
      tokenInBalanceBefore,
      tokenInBalanceAfter,
    ).toString()}`,
  );

  const profit = SafeMath256.sub(tokenInBalanceAfter, tokenInBalanceBefore);

  const beneficiaryAddress = Storage.get(BENEFICIARY_ADDRESS_KEY);

  // Transfer the profit to the benificiary address
  tokenIn.transfer(
    new Address(beneficiaryAddress),
    profit,
    getBalanceEntryCost(tokenInAddress.toString(), beneficiaryAddress),
  );

  // Trnasfer the start balance of tokenIn to the caller
  tokenIn.transfer(
    caller,
    tokenInBalanceBefore,
    getBalanceEntryCost(tokenInAddress.toString(), caller.toString()),
  );

  // Generate event for the execution of the arbitrage and the amount of the last route
  generateEvent(`ARB_EXEC:${lastAmountOut.toString()}`);

  return u256ToBytes(lastAmountOut);
}

export function executeWithFlashloan(binaryArgs: StaticArray<u8>): void {
  const args = new Args(binaryArgs);

  const flashLoanData = args
    .nextSerializable<FlashLoan>()
    .expect('Invalid flash loan data');

  const arbsRoutes = args
    .nextSerializableObjectArray<ArbRoute>()
    .expect('Invalid arb routes');

  // const coinsToUse = args.nextU64().unwrap();

  generateEvent(
    `ArbRoutes inside execuwithFlmashLoan: ${arbsRoutes.toString()}`,
  );

  const caller = Context.caller();
  const callee = Context.callee();

  // Use the flash loan instead of usual transferFrom
  const flashLoanAmount = flashLoanData.amount;
  const flashLoanPool = new IEaglePool(flashLoanData.poolAddress);
  const flashLoanToken = flashLoanData.tokenAddress;

  // Decide if the flash loan is for token A or token B
  if (flashLoanToken.toString() == flashLoanPool.getATokenAddress()) {
    flashLoanPool.flashLoan(
      flashLoanAmount,
      u256.Zero,
      callee.toString(),
      new Args()
        .add(flashLoanData)
        .addSerializableObjectArray(arbsRoutes)
        .serialize(),
    );
  } else {
    flashLoanPool.flashLoan(
      u256.Zero,
      flashLoanAmount,
      callee.toString(),
      new Args()
        .add(flashLoanData)
        .addSerializableObjectArray(arbsRoutes)
        .serialize(),
    );
  }
}

// Callback function for flash loan (to be called by the pool contract that this contract is trying to flash loan from it)
export function eagleCall(binaryArgs: StaticArray<u8>): void {
  const args = new Args(binaryArgs);

  // Do the execute arb logic here
  const profitAddress = Storage.get(BENEFICIARY_ADDRESS_KEY);

  const pAdd = args.nextString().expect('Invalid pADD');

  const aAmount = args.nextU256().expect('Invalid aAmount');
  const bAmount = args.nextU256().expect('Invalid bAmount');

  const data = args.nextBytes().expect('Invalid data');

  const dataArgs = new Args(data);

  const flashLoanData = dataArgs
    .nextSerializable<FlashLoan>()
    .expect('Invalid flash loan data');

  const flashLoanAmount = flashLoanData.amount;

  const arbsRoutes = dataArgs
    .nextSerializableObjectArray<ArbRoute>()
    .expect('Invalid arb routes');

  const poolAddress = Context.caller();

  // The caller here is the pool contract that we re trying to flash loan from
  const pool = new IEaglePool(poolAddress);

  const flashFeeRate = pool.getFlashLoanFee();

  // Calculate the flash loan fee
  const flashLoanFee = getFeeFromAmount(flashLoanAmount, flashFeeRate);

  // Calculate amoujnt to repay flash loan
  const flashLoanRepayAmount = SafeMath256.add(flashLoanAmount, flashLoanFee);

  const callee = Context.callee();

  const firstRoute = arbsRoutes[0];
  const lastRoute = arbsRoutes[arbsRoutes.length - 1];

  const tokenInAddress = firstRoute.tokenInAddress;
  const tokenIn = new IMRC20(tokenInAddress);

  // Assert that the tokenIn address of the first route is the same as the tokenoUT address of the last route
  assert(
    tokenInAddress == lastRoute.tokenOutAddress,
    'INVALID_ARB_ROUTES: TOKEN_IN != LAST_TOKEN_OUT',
  );

  // Check if the current contract has enough balance of tokenIn to execute the arb
  const tokenInCallleBalanceBefore = tokenIn.balanceOf(callee);

  assert(
    tokenInCallleBalanceBefore >= firstRoute.amountIn,
    'INVALID_ARB_ROUTES: TOKEN_IN < AMOUNT_IN',
  );

  // Get the balance of the current contract of token in
  const tokenInBalanceBefore = tokenIn.balanceOf(callee);

  let lastAmountOut = u256.Zero;

  // Print all the routes using generateEvent
  for (let i = 0; i < arbsRoutes.length; i++) {
    const route = arbsRoutes[i];

    let amountOut: u256 = u256.Zero;

    if (route.isDusa) {
      const dusaRouter = new IDusaRouter(route.routerAddress);

      let tokenPath: IMRC20[] = [];

      tokenPath.push(new IMRC20(route.tokenInAddress));
      tokenPath.push(new IMRC20(route.tokenOutAddress));

      amountOut = dusaRouter.swapExactTokensForTokens(
        route.amountIn,
        route.amountOutMin,
        [route.binStep],
        tokenPath,
        route.to,
        route.deadline,
        route.coinsToUse,
      );
    } else {
      const eaglefiRouter = new IEaglefiRouter(route.routerAddress);

      const swapPath = new SwapPath(
        route.poolAddress,
        route.tokenInAddress,
        route.tokenOutAddress,
        route.to,
        route.amountIn,
        route.amountOutMin,
        true,
      );

      amountOut = eaglefiRouter.swap(
        [swapPath],
        route.coinsToUse,
        route.deadline,
        route.coinsToUse,
      );
    }

    lastAmountOut = amountOut;
    // Update the amountIn of the next route if this route is not the last one
    if (i < arbsRoutes.length - 1) {
      arbsRoutes[i + 1].amountIn = amountOut;
    }
  }

  // Blance of tokenIn after should be greater than amountToRepay
  const tokenInBalanceAfter = tokenIn.balanceOf(callee);

  // Assert that the balance of tokenIn after is greater than amountToRepay
  assert(
    tokenInBalanceAfter > flashLoanRepayAmount,
    `FLASH_LOSS_OF_FUNDS: ${SafeMath256.sub(
      flashLoanRepayAmount,
      tokenInBalanceAfter,
    ).toString()}`,
  );

  const profit = SafeMath256.sub(tokenInBalanceAfter, flashLoanRepayAmount);

  // Transfer the profit to the benificiary address
  tokenIn.transfer(
    new Address(profitAddress),
    profit,
    getBalanceEntryCost(tokenInAddress.toString(), profitAddress),
  );

  // Trnasfer the amountToRepay of tokenIn to the pool contract(caller)
  tokenIn.transfer(
    poolAddress,
    flashLoanRepayAmount,
    getBalanceEntryCost(tokenInAddress.toString(), poolAddress.toString()),
  );

  // Generate event for the execution of the arbitrage and the amount of the last route
  generateEvent(`FLASH_ARB_EXEC:${lastAmountOut.toString()}`);
}

export function increaseMaxAllownaceForRoute(
  binaryArgs: StaticArray<u8>,
): void {
  const args = new Args(binaryArgs);
  const token = args.nextString().expect('Token is missing');
  const router = args.nextString().expect('Router is missing');

  const caller = Context.caller();
  const callee = Context.callee();

  const amount = u256.Max;

  const tokenContract = new IMRC20(new Address(token));

  tokenContract.increaseAllowance(
    new Address(router),
    amount,
    getAllownaceEntryCost(token, callee.toString(), router),
  );

  generateEvent(
    `Increased allowance ${amount.toString()} for ${router} from ${caller.toString()} to ${callee.toString()}`,
  );
}

export function getBeneficiaryAddress(): string {
  return Storage.get(BENEFICIARY_ADDRESS_KEY);
}

// Function that will always fails.
export function executeFail(binaryArgs: StaticArray<u8>): void {
  const args = new Args(binaryArgs);

  const arbsRoutes = args
    .nextSerializableObjectArray<ArbRoute>()
    .expect('Invalid arb routes');

  const caller = Context.caller();
  const callee = Context.callee();

  // First route need to use transferfrom tokenin amount from caller to current contract
  // Transfer tokenIn amount from caller to current contract
  const firstRoute = arbsRoutes[0];
  const lastRoute = arbsRoutes[arbsRoutes.length - 1];

  const tokenInAddress = firstRoute.tokenInAddress;
  const tokenIn = new IMRC20(tokenInAddress);

  // Assert that the tokenIn address of the first route is the same as the tokenoUT address of the last route
  assert(
    tokenInAddress == lastRoute.tokenOutAddress,
    'INVALID_ARB_ROUTES: TOKEN_IN != LAST_TOKEN_OUT',
  );

  tokenIn.transferFrom(
    caller,
    callee,
    firstRoute.amountIn,
    getBalanceEntryCost(
      firstRoute.tokenInAddress.toString(),
      callee.toString(),
    ),
  );

  // Print all the routes using generateEvent
  for (let i = 0; i < arbsRoutes.length; i++) {
    const route = arbsRoutes[i];

    let amountOut: u256 = u256.Zero;

    if (route.isDusa) {
      const dusaRouter = new IDusaRouter(route.routerAddress);

      let tokenPath: IMRC20[] = [];

      tokenPath.push(new IMRC20(route.tokenInAddress));
      tokenPath.push(new IMRC20(route.tokenOutAddress));

      amountOut = dusaRouter.swapExactTokensForTokens(
        route.amountIn,
        route.amountOutMin,
        [route.binStep],
        tokenPath,
        route.to,
        route.deadline,
        route.coinsToUse,
      );
    } else {
      const eaglefiRouter = new IEaglefiRouter(route.routerAddress);

      const swapPath = new SwapPath(
        route.poolAddress,
        route.tokenInAddress,
        route.tokenOutAddress,
        route.to,
        route.amountIn,
        route.amountOutMin,
        true,
      );

      amountOut = eaglefiRouter.swap(
        [swapPath],
        route.coinsToUse,
        route.deadline,
        route.coinsToUse,
      );
    }

    // Update the amountIn of the next route if this route is not the last one
    if (i < arbsRoutes.length - 1) {
      arbsRoutes[i + 1].amountIn = amountOut;
    }
  }

  assert(false, 'This should fail');
}

/**
 * @param owner - address of the token owner
 * @param spender - address of the token spender
 * @returns the key of the allowance in the storage for the given addresses
 */
function allowanceKey(owner: string, spender: string): StaticArray<u8> {
  return stringToBytes(ALLOWANCE_KEY_PREFIX + owner.concat(spender));
}

function getAllownaceEntryCost(
  tokenAddress: string,
  owner: string,
  spender: string,
): u64 {
  let cost = 0;
  if (!Storage.hasOf(new Address(tokenAddress), allowanceKey(owner, spender))) {
    // baseCost = NEW_LEDGER_ENTRY_COST = STORAGE_BYTE_COST * 4 = 100_000 * 4 = 400_000
    cost = 400_000;
    // keyCost =
    // LEDGER_COST_PER_BYTE * stringToBytes(ALLOWANCE_KEY_PREFIX + owner + spender).length = 100_000 * (9 + owner.length + spender.length)
    cost += 100_000 * (9 + owner.length + spender.length);
    // valCost = LEDGER_COST_PER_BYTE * u256ToBytes(u256.Zero).length = 100_000 * 32 = 3_200_000
    cost += 3_200_000;
  }

  return cost;
}
